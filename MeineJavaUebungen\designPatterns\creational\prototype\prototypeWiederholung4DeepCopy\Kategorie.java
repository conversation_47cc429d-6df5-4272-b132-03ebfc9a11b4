package designPatterns.creational.prototype.prototypeWiederholung4DeepCopy;

public class <PERSON><PERSON><PERSON> implements Cloneable {

    private long id;
    private String name;

    public Kategorie() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    protected Kategorie clone() throws CloneNotSupportedException {
        return (Kategorie) super.clone();
    }
}
