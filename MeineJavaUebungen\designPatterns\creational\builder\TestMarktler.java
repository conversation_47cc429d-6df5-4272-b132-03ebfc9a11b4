package designPatterns.creational.builder;

public class TestMarktler {

    public static void main(String[] args) {

        Haus haus1 = new Haus();
        haus1.setStadt("Berlin");
        haus1.setBezirk("Friedrichshain-Kreuzberg");
        haus1.setStrasse("Karl-Marx-Allee");
        haus1.setZimmerZahl(4);
        haus1.set<PERSON>ebaeudeJahr(2016);
        haus1.setSpielplatz(true);

        /*designPatterns.creational.builder.Haus @Contract(pure = true)
            public Haus(String strasse,
                    String bezirk,
                    String stadt,
                    int gebaedeJahr,
                    int zimmerZahl,
                    int badZahl,
                    int balkon,
                    boolean isMoebeliert,
                    boolean isParkplatz,
                    boolean isSpielplatz,
                    boolean isAbstellplatz)
            Inferred annotations:
            @org.jetbrains.annotations.Contract(pure = true)*/
        Haus haus2 = new Haus("Frankfurter-Allee", "Friedrichshain-Kreuzberg", "Berlin", 2018, 3, 1, 0, false, false, false, false);



        printHaus(haus1);
        printHaus(haus2);

    }

    private static void printHaus(Haus haus){

        System.out.println();

        System.out.println("Haus wurde hinzugefügt -> " + haus);

    }
}
