package jders11_einfuerungArrays.eindimensionaleArrays;

public class ArrayFortlaufendeBerechnung3 {

    public static void main(String[] args) {

        int[] nums = {1, 2, 3, 4};
        int[] runningSum = calculateRunningSum(nums);

        /*
        System.out.print("[");
        boolean firstElement = true;
        for (int num : runningSum) {
            if (!firstElement) {
                System.out.print(" ");
            }
            System.out.print(num);
            firstElement = false;
        }
        System.out.println("]");
        */

        System.out.print("[");
        for (int i = 0; i < runningSum.length; i++) {
            System.out.print(runningSum[i]);
            if (i != runningSum.length - 1) {
                System.out.print(" ");
            }
        }
        System.out.println("]");
    }

    // Methode zur Berechnung der laufenden Summe
    public static int[] calculateRunningSum(int[] nums) {
        int[] runningSum = new int[nums.length]; // Array zur Speicherung der laufenden Summe erstellen
        int sum = 0; // Variable zur Berechnung der aktuellen Summe

        // Schleife zum Durchlaufen des Eingabe-Arrays
        for (int i = 0; i < nums.length; i++) {
            sum += nums[i]; // Den aktuellen Wert des Eingabe-Arrays zur Summe addieren
            runningSum[i] = sum; // Die Summe im runningSum-Array für den entsprechenden Index speichern
        }

        return runningSum; // Das runningSum-Array zurückgeben
    }
}
