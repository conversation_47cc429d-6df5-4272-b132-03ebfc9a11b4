package ders12_switchStatements;

import java.util.Scanner;

public class C02_switchStatements {
    public static void main(String[] args) {

        // Im-switch-case sind keine Datentypen wie double, float, boolean, long  zu benutzen

        // Vom Nutzer den Tag als Zahl nehmen und Namen des Tages Ausgeben

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte Tag in Zahl eingeben");
        int tagNo = scan.nextInt();

        switch (tagNo) {
            case 1:
                System.out.println("Montag");
                break;
            case 2:
                System.out.println("Dienstag");
                break;
            case 3:
                System.out.println("Mittwoch");
                break;
            case 4:
                System.out.println("Donnerstag");
                break;
            case 5:
                System.out.println("Freitag");
                break;
            case 6:
                System.out.println("Samstag");
                break;
            case 7:
                System.out.println("Sonntag");
                break;
            default:
                System.out.println("Ungültige Zahl für Tag der Woche");

        }
    }
}
