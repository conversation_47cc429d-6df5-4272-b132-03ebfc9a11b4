/*
package com.example.mcpjavaapp.optimization;

import ai.onnxruntime.*;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Component
public class OnnxModelOptimizer {

    private OrtEnvironment env;
    private OrtSession session;

    public void initializeModel(String modelPath) {
        try {
            // ONNX Runtime-Umgebung erstellen
            env = OrtEnvironment.getEnvironment();
            
            // Optimierungsoptionen für CPU
            SessionOptions sessionOptions = new SessionOptions();
            sessionOptions.setOptimizationLevel(OptLevel.ALL_OPT);
            sessionOptions.setExecutionMode(ExecutionMode.SEQUENTIAL);
            
            // Anzahl der zu verwendenden Threads festlegen (an Ihre CPU anpassen)
            sessionOptions.setInterOpNumThreads(4);
            sessionOptions.setIntraOpNumThreads(8);
            
            // Modell laden
            Path path = Paths.get(modelPath);
            session = env.createSession(path.toString(), sessionOptions);
            
            System.out.println("ONNX-Modell erfolgreich geladen und optimiert für CPU-Inferenz");
        } catch (OrtException e) {
            System.err.println("Fehler beim Laden des ONNX-Modells: " + e.getMessage());
        }
    }

    public Map<String, OnnxTensor> runInference(Map<String, OnnxTensor> inputs) {
        try {
            // Inferenz durchführen
            OrtSession.Result result = session.run(inputs);
            
            // Ergebnisse in eine Map umwandeln
            Map<String, OnnxTensor> outputs = new HashMap<>();
            result.forEach(output -> {
                try {
                    outputs.put(output.getName(), (OnnxTensor) output.getValue());
                } catch (OrtException e) {
                    System.err.println("Fehler beim Verarbeiten der Ausgabe: " + e.getMessage());
                }
            });
            
            return outputs;
        } catch (OrtException e) {
            System.err.println("Fehler bei der Inferenz: " + e.getMessage());
            return new HashMap<>();
        }
    }

    public void close() {
        try {
            if (session != null) {
                session.close();
            }
            if (env != null) {
                env.close();
            }
        } catch (OrtException e) {
            System.err.println("Fehler beim Schließen der ONNX-Ressourcen: " + e.getMessage());
        }
    }
}
*/
