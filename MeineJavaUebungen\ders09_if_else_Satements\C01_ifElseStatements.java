package ders09_if_else_Satements;

import java.util.Scanner;

public class C01_ifElseStatements {
    public static void main(String[] args) {
        // Vom Nutzer die Längen eines Dreiecks Einlesen.
        // Wenn die "Ecken des Dreiecks gleich sind" wenn nicht "verschieden eckiges Dreieck" ausgeben

        Scanner scan =new Scanner(System.in);
        System.out.println("bitte geben sie die 3 Seiten des Dreiecks an, " +
                "\nbestätigen sie jede Seite des " +        // hier das \n für next line
                "Dreiecks mit Enter");                      // Zweite Aufforderung an Nutzer in einer Zeile

        double seite1= scan.nextDouble();
        double seite2= scan.nextDouble();
        double seite3= scan.nextDouble();

        if (seite1==seite2 && seite2==seite3){
            System.out.println("Gleichwinkliges Dreieck");
        }else {
            System.out.println("Dreieck besitzt keine gleichen Winkel");
        }

    }
}
