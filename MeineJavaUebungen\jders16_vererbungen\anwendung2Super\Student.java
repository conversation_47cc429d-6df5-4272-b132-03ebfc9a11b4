package jders16_vererbungen.anwendung2Super;

import java.util.ArrayList;

public class Student extends Person {
    // Ein Student kann nur von einer Klasse erben in dem fall Person.
    // Wir werden verschiedene Constructor Einstellungen uns anschauen, welche Möglichkeiten wir uns ermöglichen.
    // Auch bei den Super parameter werten können die Variablennamen unabhängig in der Unterklasse
    // anders benannt im Constructor aufgerufen werden, denn die Übergabe findet sein Anschluss zur Variable der Hauptklasse.

    private String studentenNummer;

    public ArrayList<String> unterrichtsFeacher;


    public Student() {

    }



    // Wenn wir keine Werte zur Oberklasse senden wollen. Müssen wir nicht, können auch einzeln später mit Set-Methoden gesetzt werden
    public Student(String studentenNummer, ArrayList<String> unterrichtsFeacher) {
        // oder super(ConstructorAuswählen);
        this.studentenNummer = studentenNummer;
        this.unterrichtsFeacher = unterrichtsFeacher;
    }

    // Erhält alle parameter von der Oberklasse, an die gesendet wird
    public Student(String vorname, String nachname, int geburtsjahr) {
        super(vorname, nachname, geburtsjahr);
    }

    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer, ArrayList<String> unterrichtsFeacher) {
        // Als Erstes müssen wir in die Klasse aus der wir erben, die Attribute mit super laden
        // super weil wir private vorname, nachname, geburtsjahr selbst in der Unterklasse nicht erreichen
        super(vorname, nachname, geburtsjahr);
        // Anders könnten wir ohne super auch das nutzen.
        // setVorname(vorname);
        // setNachname(nachname);
        // setGeburtsjahr(geburtsjahr);
        this.studentenNummer = studentenNummer;
        this.unterrichtsFeacher = unterrichtsFeacher;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    public ArrayList<String> getUnterrichtsFeacher() {
        return unterrichtsFeacher;
    }

    public void setUnterrichtsFeacher(ArrayList<String> unterrichtsFeacher) {
        this.unterrichtsFeacher = unterrichtsFeacher;
    }
}
