package jders19_interface.anwendung2;

public class Test {
    public static void main(String[] args) {

        Kreis kreis1 = new Kreis(4);

        Viereck viereck1 = new Viereck(4);

        // an der Stelle sind alle Klassen zu senden, jedoch ist
        inhaltUndUmfangAusgeben(viereck1);

        inhaltUndUmfangAusgeben(kreis1);

        System.out.println("-----------------------------------------");

        // an der Stelle sind nur die jeweiligen Klassen zu senden
        viereckBerechnen(viereck1);
        keisBerechnen(kreis1);


    }

    // Generelle Methode für alle Interface Form implementierten Klassen
    public static void inhaltUndUmfangAusgeben(Form form){

        System.out.println("Inhalt : " + form.inhaltBerchnen());
        System.out.println("Umfang : " + form.umfangBerechnen());

    }

    // Wir können auch es Spezifisches senden und ausgeben
    public static void viereckBerechnen(Viereck viereck){

        System.out.println("Kreis Informationen :");
        System.out.println("Seite : " + viereck.getSeite());
        System.out.println("Inhalt : " + viereck.inhaltBerchnen());
        System.out.println("Umfang : " + viereck.umfangBerechnen());
    }

    public static void keisBerechnen(Kreis kreis){

        System.out.println("Kreis Informationen :");
        System.out.println("Radius : " + kreis.getRadius());
        System.out.println("Inhalt : " + kreis.inhaltBerchnen());
        System.out.println("Umfang : " + kreis.umfangBerechnen());
    }
}
