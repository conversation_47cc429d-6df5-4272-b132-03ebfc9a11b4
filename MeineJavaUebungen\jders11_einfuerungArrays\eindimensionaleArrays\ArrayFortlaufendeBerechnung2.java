package jders11_einfuerungArrays.eindimensionaleArrays;

public class ArrayFortlaufendeBerechnung2 {
    public static void main(String[] args) {

        int[] nums = {1, 2, 3, 4}; // Beispiel-Eingabe

        int[] runningSum = new int[nums.length]; // Array zur Speicherung der Running Sum erstellen

        int sum = 0; // Variable zur Berechnung der laufenden Summe

        for (int i = 0; i < nums.length; i++) {
            sum += nums[i]; // Aktuellen Wert zum Summenwert addieren
            runningSum[i] = sum; // Laufende Summe an der entsprechenden Position im Array speichern
        }

        // Ausgabe der Running Sum
        System.out.print("Output: [");
        for (int i = 0; i < runningSum.length; i++) {
            System.out.print(runningSum[i]);
            if (i < runningSum.length - 1) {
                System.out.print(", ");
            }
        }
        System.out.println("]");
    }
}
