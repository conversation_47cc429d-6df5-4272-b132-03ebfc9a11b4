package jders08_mathMethoden;

public class MathMethoden {
    public static void main(String[] args) {

        //double erg = Math.sqrt(17);   Gibt die Wurzel der Zahl als Ergebnis aus

        //double zahl = Math.ceil(5.7); Rundet Zahl auf

        // double zahl = Math.pow(2,3);  <PERSON><PERSON><PERSON><PERSON>nen in wobei die erste Zahl die Basis und die zweite Zahl der Exponent ist. 2^3 = 8

        // double zahl = Math.max(20,10);  Findet den größeren Parameter, im andern fall Math.min

        double zahl = 1 + Math.random() * 100;

        System.out.println(zahl);

    }
}
