package jders04_operatoren;

@SuppressWarnings("unused")
public class ArithmetischesAufUndAbzaehlen {
    public static void main(String[] args) {
        
        int zahl1 = 25;
        int zahl2 = 35;
        int zahl3 = 15, zahl4 = 5;  // immer in großen Projekten

        /*  zahl1++;  //Aufzählen nach dem Semikolon
        ++zahl2;  //Aufzählen vor dem Semikolon
        zahl3--;  //Abzählen nach dem Semikolon
        --zahl4;  //Abzählen vor dem Semikolon */

        int ergebnis = zahl1++ + zahl2;

        System.out.println("Ergebnis : " + ergebnis);  // 60 post increment

    }
}
