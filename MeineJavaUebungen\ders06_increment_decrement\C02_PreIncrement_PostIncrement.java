package ders06_increment_decrement;

/**
 * Diese Klasse demonstriert den Unterschied zwischen Pre-Inkrement (++variable)
 * und Post-Inkrement (variable++) in Java.
 *
 * Wichtige Konzepte:
 * - Post-Inkrement (variable++): Erst wird der aktuelle Wert verwendet, dann erhöht
 * - Pre-Inkrement (++variable): Erst wird der <PERSON> erhöht, dann verwendet
 * - Auswirkungen bei Zuweisungen: Der Zeitpunkt der Erhöhung beeinflusst den zugewiesenen Wert
 *
 * Die Klasse zeigt verschiedene Beispiele, um den Unterschied zu verdeutlichen,
 * sowohl mit separaten Anweisungen als auch mit kombinierten Zuweisungen.
 */
public class C02_PreIncrement_PostIncrement {
    /**
     * Die Hauptmethode demonstriert den Unterschied zwischen Pre-Inkrement und Post-Inkrement.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        System.out.println("Demonstration des Unterschieds zwischen Pre-Inkrement und Post-Inkrement");

        // BEISPIEL 1: Separate Anweisungen (Zuweisung und Inkrement getrennt)
        System.out.println("\nBEISPIEL 1: Separate Anweisungen");

        // Initialisierung der Variablen
        int zahl = 10;
        System.out.println("Ausgangswert: zahl = " + zahl);

        // Zuweisung und Inkrement als separate Anweisungen
        int b = zahl;    // Wert von zahl (10) wird b zugewiesen
        zahl++;          // zahl wird um 1 erhöht (auf 11)

        System.out.println("Nach 'int b = zahl; zahl++;':");
        System.out.println("b = " + b);      // 10 (ursprünglicher Wert von zahl)
        System.out.println("zahl = " + zahl); // 11 (nach Inkrement)

        // BEISPIEL 2: Post-Inkrement in Kombination mit Zuweisung
        System.out.println("\nBEISPIEL 2: Post-Inkrement mit Zuweisung");

        // Zurücksetzen von zahl auf 11 für das nächste Beispiel
        zahl = 11;
        System.out.println("Ausgangswert: zahl = " + zahl);

        // Post-Inkrement: Erst wird der aktuelle Wert verwendet, dann erhöht
        int c = zahl++;  // c bekommt den aktuellen Wert von zahl (11), dann wird zahl um 1 erhöht (auf 12)

        System.out.println("Nach 'int c = zahl++;':");
        System.out.println("c = " + c);      // 11 (Wert von zahl VOR dem Inkrement)
        System.out.println("zahl = " + zahl); // 12 (NACH dem Inkrement)

        // BEISPIEL 3: Pre-Inkrement in Kombination mit Zuweisung
        System.out.println("\nBEISPIEL 3: Pre-Inkrement mit Zuweisung");

        // Zurücksetzen von zahl auf 11 für das nächste Beispiel
        zahl = 11;
        System.out.println("Ausgangswert: zahl = " + zahl);

        // Pre-Inkrement: Erst wird der Wert erhöht, dann verwendet
        c = ++zahl;  // zahl wird zuerst um 1 erhöht (auf 12), dann bekommt c diesen neuen Wert (12)

        System.out.println("Nach 'c = ++zahl;':");
        System.out.println("c = " + c);      // 12 (Wert von zahl NACH dem Inkrement)
        System.out.println("zahl = " + zahl); // 12 (NACH dem Inkrement)

        // ZUSAMMENFASSUNG
        System.out.println("\nZUSAMMENFASSUNG:");
        System.out.println("1. Post-Inkrement (zahl++): Erst wird der aktuelle Wert verwendet, dann erhöht");
        System.out.println("   - Bei 'int c = zahl++;' bekommt c den ursprünglichen Wert, zahl wird danach erhöht");
        System.out.println("2. Pre-Inkrement (++zahl): Erst wird der Wert erhöht, dann verwendet");
        System.out.println("   - Bei 'c = ++zahl;' wird zahl zuerst erhöht, dann bekommt c den neuen Wert");

        /*
         * WICHTIG: Der Unterschied zwischen Pre- und Post-Inkrement wird nur sichtbar,
         * wenn der Wert im selben Ausdruck verwendet wird (z.B. bei einer Zuweisung).
         *
         * Wenn das Inkrement als eigenständige Anweisung steht, gibt es keinen
         * funktionalen Unterschied zwischen zahl++ und ++zahl.
         */
    }
}
