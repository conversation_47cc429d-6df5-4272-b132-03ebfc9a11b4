package effectiveJava.effectiveJava03;

// Das Interface Cache. Das Interface definiert zwei Methoden, put() und get().
/*Der Code definiert eine Schnittstelle namens Cache mit zwei Methoden: put und get.
  Der Zweck dieser Schnittstelle ist es, einen Vertrag für die Implementierung einer
  Cache-Datenstruktur bereitzustellen. Die put-Methode nimmt einen Schlüssel vom Typ F und einen Wert
  vom Typ S entgegen und ist für die Speicherung des Wertes im Cache mit dem angegebenen Schlüssel verantwortlich.
  Die get-Methode nimmt einen Schlüssel vom Typ F entgegen und gibt den entsprechenden Wert aus dem Cache zurück.*/
public interface Cache <F,S> {

    // Die Methode put() nimmt zwei Parameter entgegen: einen Schlüssel key vom Typ F und
    // einen Wert value vom Typ S. Die Methode sollte den Wert value mit dem Schlüssel key in den Cache speichern.
    public void put (F key, S value);
    // Die Methode get() nimmt einen Schlüssel key vom Typ F entgegen und gibt den zugehörigen Wert vom Typ S zurück.
    public S get(F key);
}
