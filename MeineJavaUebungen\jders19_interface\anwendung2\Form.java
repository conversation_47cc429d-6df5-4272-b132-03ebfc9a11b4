package jders19_interface.anwendung2;

public interface Form {

    /* Definition: Ein Interface ist eine Schnittstelle, die eine Reihe von Methoden definiert.
       Es kann verwendet werden, um allgemeine Konzepte zu definieren, die von anderen Klassen implementiert werden können.
       Merkmale:
      -Interfaces können nur Methoden definieren.
      -Methoden in Interfaces sind immer abstrakt.
      -Interfaces können von anderen Klassen implementiert werden.*/

    // Bei abstract müsste eine Methode bekannt sein!
    // Wenn wir wollen, können wir auch in der Rumpflosen Methode unsere zu sendenden Parameter angeben
    public double umfangBerechnen();
    // Methode noch Leer
    public double inhaltBerchnen();



}
