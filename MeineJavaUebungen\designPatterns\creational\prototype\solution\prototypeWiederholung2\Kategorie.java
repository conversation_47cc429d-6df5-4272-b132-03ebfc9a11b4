package designPatterns.creational.prototype.solution.prototypeWiederholung2;

public class Kate<PERSON>ie {

    private long id;
    private String name;

    public Kategorie() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }
}
