package ders06_increment_decrement;

public class C03_02_PreIncrement_PostIncrement {
    public static void main(String[] args) {
        int a=10;

        System.out.println("a beinhaltet den Wert : " + ++a);  // a:11

        int b= a++;  // a:12  b:11

        System.out.println("b beinhaltet den Wert : " + b);  // b:11

        int c= b++ + a;  // a:12  b:11

        System.out.println("c beinhaltet den Wert : " + c);  // c:23

        System.out.println("gesamt Ergebnis : " +(a+b+c));  // a:12  b:12  c:23
    }
}
