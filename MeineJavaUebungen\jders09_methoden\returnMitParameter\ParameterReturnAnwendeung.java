package jders09_methoden.returnMitParameter;

public class ParameterReturnAnwendeung {
    public static void main(String[] args) {

        // Wir können Methoden initialisieren und so oft wir wollen sie nutzen.
        double d = multipliziere(2.7f, 5.3f);
        System.out.println("Multipliziertes Ergebnis : " + d);

        d = multipliziere(2.8f, 12.7f);
        System.out.println("2. Multipliziertes Ergebnis : " + d);

        double t = teile(5, 3);
        System.out.println("Dividiertes Ergebnis : " + t);

        t = teile(52, 27);
        System.out.println("2. Dividiertes Ergebnis : " + t);

        int a = addiere(10, 25);
        System.out.println("Addiertes Ergebnis : " + a);

        a = addiere(12, 5);
        System.out.println("2. addiertes Ergebnis : " + a);

    }

    // Da wir float Werte Bekommen müssen wir diese in int umwandeln
    public static int addiere(float a, float b) {

        //int gesamt = (int) a+b wandelt nur a in ein int und b nicht
        int gesamt = (int) (a + b);

        //return (int) ((int) a+b);
        return (int) (a + b);
    }

    // da float in double psst können wir dies so return
    public static double multipliziere(float a, float b) {

        return a * b;
    }

    /*
    * Die Division in der teile-Methode wird als Integer-Division ausgeführt,
    * da beide Operanden vom Typ int sind. Wenn das Ergebnis in einen double umgewandelt wird,
    * ist es bereits zu spät, weil die Division bereits als Integer durchgeführt wurde.
    * Um eine Fließkomma-Division zu erreichen, können Sie entweder einen der Operanden
    * zu einem Fließkomma-Typ wie float oder double umwandeln oder Sie können
    * den Divisor als double umwandeln und die Division durchführen.
    * */
    public static double teile(int a, int b) {

        return (double) a / b;
    }

}
