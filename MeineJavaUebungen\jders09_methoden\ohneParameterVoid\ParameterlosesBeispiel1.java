package jders09_methoden.ohneParameterVoid;

public class ParameterlosesBeispiel1 {
    public static void main(String[] args) {
    // Folgender Vorgang wird 5 mal ausgegeben.
        System.out.println("Hallo ich Grüße dich!");
        System.out.println("Developer");
        System.out.println("Wie Geht es dir?");

        System.out.println();               // Leerzeile

        ausgeben();

        System.out.println();

        for (int i = 0;i<3;i++) {
            ausgeben();
            System.out.println();
        }

    }

    public static void ausgeben() {


        System.out.println("Hallo ich Grüße dich!");
        System.out.println("Developer");
        System.out.println("Wie Geht es dir?");
    }

}
