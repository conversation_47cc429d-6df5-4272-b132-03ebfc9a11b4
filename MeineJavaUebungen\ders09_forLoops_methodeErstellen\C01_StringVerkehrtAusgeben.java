package ders09_forLoops_methodeErstellen;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert die Verwendung von for-Schleifen und Methoden in Java.
 *
 * Hauptfunktionen:
 * 1. Einlesen eines Strings vom Benutzer und Ausgabe in umgekehrter Reihenfolge
 * 2. Demonstration des Unterschieds zwischen Pre-Dekrement (--i) und Post-Dekrement (i--)
 *
 * Die Klasse zeigt auch, wie man auf einzelne Zeichen eines Strings mit der charAt()-Methode zugreift
 * und wie man die Länge eines Strings mit der length()-Methode ermittelt.
 */
public class C01_StringVerkehrtAusgeben {

    /**
     * Die Hauptmethode liest einen String vom Benutzer ein und gibt ihn in umgekehrter Reihenfolge aus.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Vom Nutzer einen String einlesen und verkehrt (rückwärts) ausgeben.

        // Scanner-Objekt zur Eingabe erstellen
        Scanner sc = new Scanner(System.in);

        // Benutzer zur Eingabe auffordern
        System.out.println("Geben Sie einen Satz ein für eine rückwärts-Ausgabe:");

        // Eingabe einlesen
        String schriftStueck = sc.nextLine();

        System.out.print("Der Satz rückwärts lautet: ");

        // For-Schleife zum Durchlaufen des Strings von hinten nach vorne
        // 1. Initialisierung: i = schriftStueck.length()-1 (letzter Index des Strings)
        // 2. Bedingung: i >= 0 (solange i größer oder gleich 0 ist)
        // 3. Aktualisierung: --i (Pre-Dekrement, verringert i vor der nächsten Iteration)
        for (int i = schriftStueck.length()-1; i >= 0; --i) {
            // Ausgabe des Zeichens an Position i
            // charAt(i) gibt das Zeichen an der Position i im String zurück
            System.out.print(schriftStueck.charAt(i));
        }

        // Zeilenumbruch nach der Ausgabe
        System.out.println();

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        sc.close();
    }

    /**
     * Diese Methode demonstriert den Unterschied zwischen Pre-Dekrement (--i) und Post-Dekrement (i--).
     *
     * Der Unterschied zwischen i-- und --i wird besonders wichtig, wenn der Wert der Operation
     * in demselben Ausdruck verwendet wird, in dem sie auftritt.
     *
     * In anderen Kontexten, wie beim einfachen Durchlaufen eines Arrays, macht es oft keinen
     * funktionalen Unterschied, ob wir i-- oder --i verwenden, solange wir die Logik entsprechend anpassen.
     */
    public void iteration() {
        // Array mit Testwerten
        int[] array = {1, 2, 3, 4, 5};

        // Beispiel 1: Verwendung von Post-Dekrement (i--)
        System.out.println("Mit Post-Dekrement (i--):");
        // Bei Post-Dekrement wird der Wert von i NACH seiner Verwendung verringert
        // Daher müssen wir array[i-1] verwenden, um auf das richtige Element zuzugreifen
        for (int i = array.length; i > 0; i--) {
            System.out.println(array[i - 1]);
        }

        // Beispiel 2: Verwendung von Pre-Dekrement (--i)
        System.out.println("\nMit Pre-Dekrement (--i):");
        // Bei Pre-Dekrement wird der Wert von i VOR seiner Verwendung verringert
        // Daher können wir direkt array[i] verwenden, nachdem i verringert wurde
        for (int i = array.length; --i >= 0; ) {
            System.out.println(array[i]);
        }

        /*
         * WICHTIG: Unterschied zwischen Post-Dekrement und Pre-Dekrement
         *
         * Post-Dekrement (i--):
         * - Erst wird der aktuelle Wert von i verwendet
         * - Dann wird i um 1 verringert
         *
         * Pre-Dekrement (--i):
         * - Erst wird i um 1 verringert
         * - Dann wird der neue Wert von i verwendet
         *
         * Beide Schleifen in dieser Methode geben die gleichen Werte aus (5, 4, 3, 2, 1),
         * aber sie verwenden unterschiedliche Techniken, um auf die Array-Elemente zuzugreifen.
         */
    }
}
