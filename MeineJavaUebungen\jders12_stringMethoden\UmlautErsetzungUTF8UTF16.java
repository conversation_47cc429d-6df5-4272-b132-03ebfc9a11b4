package jders12_stringMethoden;

import java.nio.charset.StandardCharsets;

public class UmlautErsetzungUTF8UTF16 {
    private static String[][] UMLAUT_REPLACEMENTS = {
            {"Ä", "Ae"},
            {"Ü", "Ue"},
            {"Ö", "Oe"},
            {"ä", "ae"},
            {"ü", "ue"},
            {"ö", "oe"},
            {"ß", "ss"}
    };

    // Methode zum Ersetzen der Umlaute unter Berücksichtigung der String-Kodierung
    public static String replaceUmlaute(String input) {
        String result = input;

        // Durchlaufe alle Einträge im UMLAUT_REPLACEMENTS-Array
        for (int i = 0; i < UMLAUT_REPLACEMENTS.length; i++) {
            String umlaut = UMLAUT_REPLACEMENTS[i][0];
            String ersatz = UMLAUT_REPLACEMENTS[i][1];

            // Ersetze Umlaut durch entsprechenden Ersatz
            result = result.replace(umlaut, ersatz);
        }

        return result;
    }

    public static void main(String[] args) {
        String input = "Özer Özözbek"; // Originaler String

        // Überprüfe die Kodierung des originalen Strings
        checkEncoding(input);

        String umlauteErsetzt = replaceUmlaute(input); // Umlaute ersetzen

        // Überprüfe die Kodierung des ersetzten Strings
        checkEncoding(umlauteErsetzt);

        System.out.println("Original: " + input);
        System.out.println("Ersetzt: " + umlauteErsetzt);
    }

    // Methode zum Überprüfen der String-Kodierung
    private static void checkEncoding(String input) {
        System.out.println("Byte-Repräsentation für '" + input + "' in verschiedenen Kodierungen:");

        // Überprüfe UTF-8 Kodierung
        byte[] utf8Bytes = input.getBytes(StandardCharsets.UTF_8);
        System.out.println("UTF-8: " + byteArrayToString(utf8Bytes));

        // Überprüfe UTF-32 Kodierung
        byte[] utf16Bytes = input.getBytes(StandardCharsets.UTF_16);
        System.out.println("UTF-16: " + byteArrayToString(utf16Bytes));

        // Weitere Kodierungen können hier hinzugefügt werden

        System.out.println();
    }

    // Hilfsmethode zur Darstellung eines Byte-Arrays als String
    private static String byteArrayToString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(b).append(" ");
        }
        return sb.toString().trim();
    }
    /*In dieser Version der UmlautErsetzung-Klasse wird die checkEncoding-Methode hinzugefügt,
     * um die Byte-Repräsentation des Eingabe-Strings in verschiedenen Kodierungen zu überprüfen.
     * Sie können weitere Kodierungen hinzufügen, indem Sie die entsprechenden Zeilen ergänzen.
     *
     * Die checkEncoding-Methode wird sowohl vor als auch nach dem Umlaut-Ersetzungsvorgang aufgerufen,
     * um die Kodierung des ursprünglichen Strings und des ersetzten Strings zu überprüfen.
     * Dadurch erhalten Sie Informationen über die Kodierung und können mögliche Probleme
     * mit unterschiedlichen Kodierungen identifizieren.
     *
     * Bitte beachten Sie, dass die Überprüfung der Byte-Repräsentation und der Kodierung
     * zusätzlichen Aufwand erfordert und nicht in allen Situationen erforderlich oder sinnvoll ist.
     * Sie können die checkEncoding-Methode entsprechend Ihren Anforderungen anpassen oder ganz entfernen,
     * wenn Sie keine detaillierten Informationen zur Kodierung benötigen.*/
}

