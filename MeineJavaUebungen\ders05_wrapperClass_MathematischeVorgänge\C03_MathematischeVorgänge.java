package ders05_wrapperClass_MathematischeVorgänge;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert mathematische Operationen in Java, insbesondere
 * die Berechnung der Quersumme einer dreistelligen Zahl.
 *
 * Wichtige Konzepte:
 * - Ganzzahlige Division (/) in Java: Nachkommastellen werden abgeschnitten
 * - Modulo-Operation (%) zur Extraktion der letzten Ziffer einer Zahl
 * - Schrittweise Berechnung der Quersumme durch wiederholte Anwendung dieser Operationen
 *
 * Die Klasse zeigt zunächst grundlegende Beispiele für Division und Modulo,
 * bevor sie die Quersumme einer vom Benutzer eingegebenen dreistelligen Zahl berechnet.
 */
public class C03_MathematischeVorgänge {
    /**
     * Die Hauptmethode demonstriert grundlegende mathematische Operationen und
     * berechnet die Quersumme einer dreistelligen Zahl.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // TEIL 1: Grundlegende mathematische Operationen
        System.out.println("TEIL 1: GRUNDLEGENDE MATHEMATISCHE OPERATIONEN");

        // Beispiel 1: Ganzzahlige Division
        System.out.println("\nBeispiel 1: Ganzzahlige Division");
        System.out.println("23 / 5 = " + (23 / 5) + " (mathematisch wäre es 4,6)");
        System.out.println("Erklärung: Bei der Division zweier int-Werte werden Nachkommastellen abgeschnitten.");

        // Beispiel 2: Modulo-Operation
        System.out.println("\nBeispiel 2: Modulo-Operation");
        System.out.println("123 % 10 = " + (123 % 10));
        System.out.println("Erklärung: Der Modulo-Operator (%) gibt den Rest der Division zurück.");
        System.out.println("123 / 10 = 12 Rest 3, daher ist 123 % 10 = 3");

        // Beispiel 3: Kombination von Division und Modulo
        System.out.println("\nBeispiel 3: Kombination von Division und Modulo");
        System.out.println("123 / 10 = " + (123 / 10) + " (mathematisch wäre es 12,3)");
        System.out.println("Erklärung: Die ganzzahlige Division entfernt die letzte Ziffer einer Zahl.");

        // TEIL 2: Berechnung der Quersumme einer dreistelligen Zahl
        System.out.println("\n\nTEIL 2: BERECHNUNG DER QUERSUMME EINER DREISTELLIGEN ZAHL");
        System.out.println("(Wiederholung von C02_GesamtZahlAllerZiffern mit detaillierteren Erklärungen)");

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scan = new Scanner(System.in);

        // Benutzer zur Eingabe auffordern
        System.out.println("\nBitte geben Sie eine dreistellige positive ganze Zahl ein (z.B. 123):");

        // Zahl einlesen
        int zahl = scan.nextInt();

        // Kopie der Eingabe für die Ausgabe am Ende
        int originalZahl = zahl;

        // Variablen zur Berechnung der Quersumme initialisieren
        int einerZiffer = 0;    // Speichert die aktuell extrahierte Ziffer
        int gesamtZiffer = 0;   // Speichert die Summe aller Ziffern (Quersumme)

        System.out.println("\nBerechnung der Quersumme für " + originalZahl + ":");

        // SCHRITT 1: Erste Ziffer extrahieren (Einer-Stelle)
        System.out.println("\nSCHRITT 1: Erste Ziffer extrahieren (Einer-Stelle)");

        // Modulo 10 gibt die letzte Ziffer (Einer-Stelle) zurück
        einerZiffer = zahl % 10;
        System.out.println("einerZiffer = zahl % 10 = " + zahl + " % 10 = " + einerZiffer);

        // Die extrahierte Ziffer zur Gesamtsumme addieren
        gesamtZiffer = gesamtZiffer + einerZiffer;
        System.out.println("gesamtZiffer = gesamtZiffer + einerZiffer = " + (gesamtZiffer - einerZiffer) +
                          " + " + einerZiffer + " = " + gesamtZiffer);

        // Die letzte Ziffer entfernen durch ganzzahlige Division durch 10
        zahl = zahl / 10;
        System.out.println("zahl = zahl / 10 = " + (zahl * 10) + " / 10 = " + zahl);

        // SCHRITT 2: Zweite Ziffer extrahieren (Zehner-Stelle)
        System.out.println("\nSCHRITT 2: Zweite Ziffer extrahieren (Zehner-Stelle)");

        einerZiffer = zahl % 10;
        System.out.println("einerZiffer = zahl % 10 = " + zahl + " % 10 = " + einerZiffer);

        gesamtZiffer = gesamtZiffer + einerZiffer;
        System.out.println("gesamtZiffer = gesamtZiffer + einerZiffer = " + (gesamtZiffer - einerZiffer) +
                          " + " + einerZiffer + " = " + gesamtZiffer);

        zahl = zahl / 10;
        System.out.println("zahl = zahl / 10 = " + (zahl * 10) + " / 10 = " + zahl);

        // SCHRITT 3: Dritte Ziffer extrahieren (Hunderter-Stelle)
        System.out.println("\nSCHRITT 3: Dritte Ziffer extrahieren (Hunderter-Stelle)");

        einerZiffer = zahl % 10;
        System.out.println("einerZiffer = zahl % 10 = " + zahl + " % 10 = " + einerZiffer);

        gesamtZiffer = gesamtZiffer + einerZiffer;
        System.out.println("gesamtZiffer = gesamtZiffer + einerZiffer = " + (gesamtZiffer - einerZiffer) +
                          " + " + einerZiffer + " = " + gesamtZiffer);

        zahl = zahl / 10;
        System.out.println("zahl = zahl / 10 = " + (zahl * 10) + " / 10 = " + zahl);
        System.out.println("Hinweis: zahl ist jetzt " + zahl + ". Bei einer dreistelligen Zahl ist dieser Wert 0.");
        System.out.println("Dies könnte als Abbruchbedingung in einer Schleife verwendet werden.");

        // Ergebnis ausgeben
        System.out.println("\nERGEBNIS:");
        System.out.println("Die Quersumme von " + originalZahl + " ist: " + gesamtZiffer);

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scan.close();

        /*
         * HINWEIS: Für eine allgemeinere Lösung, die mit Zahlen beliebiger Länge funktioniert,
         * könnte man eine Schleife verwenden:
         *
         * int zahl = 12345;
         * int quersumme = 0;
         *
         * while (zahl > 0) {
         *     quersumme += zahl % 10;  // Letzte Ziffer addieren
         *     zahl /= 10;              // Letzte Ziffer entfernen
         * }
         *
         * System.out.println("Quersumme: " + quersumme);  // Ausgabe: 15 (1+2+3+4+5)
         */
    }
}
