package jders17_polymorphie.anwendung2InstanceOfWiederholung;

public class Viereck extends Form {

    private double seite;

    public Viereck() {

    }

    public Viereck(double seite) {
        this.seite = seite;
    }

    public void setSeite(double seite) {
        this.seite = seite;
    }

    @Override
    public double getInhalt() {
        // return super.getBereich() = 0
        return seite * seite;
    }

    @Override
    public double getUmfang() {
        return 4 * seite;
    }
}
