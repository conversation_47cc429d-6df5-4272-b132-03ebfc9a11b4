package designPatterns.creational.prototype.prototypeWiederholungCopy;

import java.util.Arrays;

public class Test1ShallowCopy {
    public static void main(String[] args) {

        // Shallow-Copy

        final int NUM = 8;
        int[] old = new int[NUM];
        int[] new1;

        for (int i = 0; i < NUM; i++) {
            old[i] = i;
        }

        // In dieser Zeile wird die Referenz new1 auf dasselbe Arrayobjekt wie old gesetzt.
        // Dies bedeutet, dass beide Arrays, new1 und old, auf dasselbe Speicherobjekt verweisen.
        // Daher handelt es sich um eine Shallow Copy, und Änderungen an einem Array (new1 oder old)
        // wirken sich auf beide aus, da sie auf dasselbe Arrayobjekt zeigen.
        new1 = old;

        System.out.println(Arrays.toString(old));
        System.out.println(Arrays.toString(new1));

        System.out.println(old); //[I@6bc7c054
        System.out.println(new1);  //[I@6bc7c054
    }
}

