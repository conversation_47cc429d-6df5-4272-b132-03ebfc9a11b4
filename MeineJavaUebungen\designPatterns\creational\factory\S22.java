package designPatterns.creational.factory;

public class S22 implements Handy {

    /*In der Klasse S22 so wohl auch S23 werden die Eigenschaften eines bestimmten Handy-Modells
      (marke, model, groesse, gewicht) gespeichert und über die Implementierung
      der Methoden aus dem Handy-Interface zugänglich gemacht.
    */
    private String marke;  // Hier wird die Marke des Handys gespeichert
    private String model;  // Hier wird das Modell des Handys gespeichert
    private int groesse;    // Hier wird die Größe des Handys gespeichert
    private int gewicht;    // Hier wird das Gewicht des Handys gespeichert

    public S22(String marke, String model, int groesse, int gewicht) {
        this.marke = marke;
        this.model = model;
        this.groesse = groesse;
        this.gewicht = gewicht;
    }

    @Override
    public String getMarke() {
        return marke;       // Gibt die gespeicherte Marke des Handys zurück
    }

    @Override
    public String getModel() {
        return model;       // Gibt das gespeicherte Modell des Handys zurück
    }

    @Override
    public int getGroesse() {
        return groesse;     // Gibt die gespeicherte Größe des Handys zurück
    }

    @Override
    public int getGewicht() {
        return gewicht;     // Gibt das gespeicherte Gewicht des Handys zurück
    }

    @Override
    public String toString() {
        return "S22{" +
                "marke='" + marke + '\'' +
                ", model='" + model + '\'' +
                ", groesse=" + groesse +
                ", gewicht=" + gewicht +
                '}';
    }
}

