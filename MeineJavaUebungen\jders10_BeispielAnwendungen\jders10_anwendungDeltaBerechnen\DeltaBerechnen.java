package jders10_BeispielAnwendungen.jders10_anwendungDeltaBerechnen;

import java.util.Scanner;

public class DeltaBerechnen {
    public static void main(String[] args) {

        Scanner sc = new Scanner(System.in);

        int a;
        int b;
        int c;

        System.out.print("geben Sie den Wert von A an : ");
        a = sc.nextInt();

        System.out.print("geben Sie den Wert von B an : ");
        b = sc.nextInt();

        System.out.print("geben Sie den Wert von C an : ");
        c = sc.nextInt();

        double delta = deltaBerechnen(a, b, c);

        deltaKontrolle(delta);

        System.out.println("Delta " + delta);

    }

    public static double deltaBerechnen(int a, int b, int c) {

        double delta = Math.pow(b, 2) - 4 * a * c;

        return delta;
    }

    public static void deltaKontrolle(double delta) {

        if (delta > 0) {

            System.out.println("2 Wurzel vorhanden.");

        }else if (delta == 0){

            System.out.println("2 gleiche Wurzel vorhanden");

        }else {

            System.out.println("Keine Delta Wurzel vorhanden");
        }
    }

}
