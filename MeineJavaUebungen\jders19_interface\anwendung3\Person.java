package jders19_interface.anwendung3;

// Wir erstellen die Klasse in abstract, weil eine nie eine ungebundene Person
// als Objekt erstellt werden soll, sondern nur Student, Dozent, Doktor usw. gebundene Klassen
public abstract class Person {

    private String vorname;

    private String nachname;

    private int geburtsjahr;

    private Adresse adresse;

    public Person() {

    }

    public Person(String vorname, String nachname, int geburtsjahr, Adresse adresse) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
        this.adresse = adresse;
    }

    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    public Adresse getAdresse() {
        return adresse;
    }

    public void setAdresse(Adresse adresse) {
        this.adresse = adresse;
    }

    @Override
    public String toString() {
        return "Person{" +
                "vorname='" + vorname + '\'' +
                ", nachname='" + nachname + '\'' +
                ", geburtsjahr=" + geburtsjahr +
                ", adresse='" + adresse + '\'' +
                '}';
    }
}
