package ders10_nestedIfElseStatements;

import java.util.Scanner;

public class C01_ifElseSolution {
    public static void main(String[] args) {
        /*
            Beispiel: Nutzer Eingabe Geschlecht und Alter verarbeiten wo,
            Fr<PERSON>en, 60 Jahre und über, Männer 65 und über können in Rente.
            Mit Bezug auf Geschlecht und Alter "Du kannst in Rente" Oder "Um Rente zu Beziehen, musst du ..Jahre noch Arbeiten"
         */

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie ihr Geschlecht an");
        String geschlecht = scan.nextLine();
        System.out.println("Bitte geben sie ihr Alter an");
        Double alter = scan.nextDouble();

        if (alter < 0 || alter > 90) {
            System.out.println("Angegebenes Alter ist ungültig");
        } else if (!(geschlecht.equalsIgnoreCase("Weiblich") ||
                geschlecht.equalsIgnoreCase("Männlich"))) {
            System.out.println("Eingabe ist nicht Weiblich oder Männlich, ungültige Eingabe");
        } else if (geschlecht.equalsIgnoreCase("Weiblich") && alter >= 60) {
            System.out.println("Sie können Rente Beziehen");
        } else if (geschlecht.equalsIgnoreCase("Weiblich") && alter < 60) {
            System.out.println("Um Rente zu Beziehen, musst du " + (60 - alter) + " Jahre noch Arbeiten");
        } else if (geschlecht.equalsIgnoreCase("Männlich") && alter >= 65) {
            System.out.println("Sie können Rente Beziehen");
        } else if (geschlecht.equalsIgnoreCase("Männlich") && alter < 65) {
            System.out.println("Um Rente zu Beziehen, musst du " + (65 - alter) + " Jahre noch Arbeiten");
        }
    }
}
