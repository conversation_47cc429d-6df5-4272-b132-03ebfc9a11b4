package designPatterns.creational.singleton;

public class Singleton {

    // Einmaliger new Aufruf an unseren Singleton Konstruktor
    private static Singleton singleton;

    // Zähler für die Anzahl der Aufrufe von `getSingleton()`
    private static int getSingletonZahl = 0;

    // Unsere getSingleton-Methode, die das Singleton-Objekt zurückgibt
    public static Singleton getSingleton() {

        /* Für eine per formierte Struktur des Singleton-Patterns mit doppelter Überprüfung und Synchronisierung ermöglicht
           effizientes und thread-sicheres Erstellen der Singleton-Instanz.*/
        // Überprüfung, ob die Singleton-Instanz bereits existiert
        if (singleton == null) {

            // Synchronisierter Block, um gleichzeitigen Zugriff mehrerer Threads zu verhindern, es wird das Singleton-Klassenobjekt als
            // Sperrobjekt verwendet, um die Synchronisation durchzuführen. Dadurch wird verhindert, dass mehrere Threads gleichzeitig den Singleton initialisieren.
            synchronized (Singleton.class) {

                // Erneute Überprüfung innerhalb des synchronisierten Blocks
                if (singleton == null) {

                    // Erstellung der Singleton-Instanz, falls sie noch nicht existiert wird 'singleton' auf die neu erstellte Instanz gesetzt.
                    singleton = new Singleton();
                }
            }
        }

        /* Da Lazy Initialization (nicht thread-sicher ist denn es wären in beiden threads singleton == null): Dies ist die einfachste Form der Singleton-Implementierung,
           jedoch nicht thread sicher und kann zu Problemen führen, wenn mehrere Threads gleichzeitig auf die Instanz zugreifen.
           */

        // Zähler erhöhen
        getSingletonZahl++;

        // Zähler ausgeben
        System.out.println(getSingletonZahl);

        // Rückgabe der Singleton-Instanz
        return singleton;
    }

    /* Im Singleton-Muster wird sicherstellt, dass nur eine einzige Instanz einer Klasse existiert
       und diese Instanz global zugänglich ist. Alles, was im Konstruktor der Singleton-Klasse initialisiert wird,
       betrifft diese einzige Instanz. Wenn Sie zusätzliche Variablen im Konstruktor hinzufügen,
       werden sie nur einmal beim Erstellen der Singleton-Instanz initialisiert.
       Jedes Mal, wenn auf die Singleton-Instanz zugegriffen wird, erhalten Sie
       dieselbe Referenz auf diese Instanz, und daher bleiben die initialisierten Werte und
       Variablen konsistent über alle Aufrufe hinweg. Dies ist ein wichtiger Aspekt des Singleton-Musters,
       der sicherstellt, dass die Singleton-Instanz in der Anwendung eindeutig bleibt. */

    // Singleton Objekt Konstruktor
    public Singleton() {

        // Ausgabe, dass das Singleton-Objekt erstellt wurde
        System.out.println("Singleton wurde erstellt.");
    }
}
