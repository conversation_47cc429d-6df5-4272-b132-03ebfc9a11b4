package effectiveJava.effectiveJava03;

import java.util.HashMap;

public class LazyCache2 implements Cache {

    /*<PERSON><PERSON>, die bei Lazy-Cache zu beachten sind:

     -Die Instanz des Caches sollte nur einmal erstellt werden. Dies wird durch die Verwendung
      eines statischen Attributs und eines privaten Konstruktors erreicht.
     -Die Methode getInstance() sollte die einzige Möglichkeit sein, eine Instanz des Caches zu erhalten.
      Dies wird durch die Verwendung einer synchronized-Blockierung erreicht.
     -Die Methoden put() und get() sollten nur die Werte in der HashMap manipulieren. Dies verhindert,
      dass der Cache versehentlich gelöscht wird.

      In der Klasse LazyCache gibt es ein paar Dinge, die verbessert werden könnten:

     -Die Methode put() sollte den Wert nur in die HashMap einfügen, wenn der Schlüssel noch nicht vorhanden ist.
      Dies verhindert, dass der Cache unnötigerweise gelöscht wird.
     -Die Methode get() sollte den Wert aus der HashMap zurückgeben, auch wenn der Schlüssel nicht vorhanden ist.
      Dies verhindert, dass die Methode einen Null-Wert zurückgibt.
      */

    // Eine private statische Instanz der LazyCache-Klasse.
    private static LazyCache2 instance;

    // Eine HashMap zur Speicherung von Schlüssel-Wert-Paaren.
    private HashMap<Object, Object> map;

    // Der Konstruktor der LazyCache-Klasse. Er erstellt eine neue HashMap.
    private LazyCache2() {
        map = new HashMap<Object, Object>();
    }

    // Die put-Methode, um ein Schlüssel-Wert-Paar im Cache zu speichern.
    @Override
    public void put(Object key, Object value) {
        // Nur den Wert hinzufügen, wenn der Schlüssel noch nicht vorhanden ist.
        if (!map.containsKey(key)) {
            map.put(key, value);
        }
    }

    // Die get-Methode, um den Wert für einen Schlüssel aus dem Cache abzurufen.
    @Override
    public Object get(Object key) {
        // Den Wert aus der HashMap zurückgeben, auch wenn der Schlüssel nicht vorhanden ist.
        return map.getOrDefault(key, null);
    }

    // Die Methode getInstance, um die einzige Instanz der LazyCache-Klasse zu erhalten.
    public static LazyCache2 getInstance() {
        // Überprüfen, ob die Instanz bereits existiert. Wenn nicht, wird eine neue erstellt.
        if (instance == null) {
            synchronized (LazyCache2.class) {
                if (instance == null) {
                    instance = new LazyCache2();
                }
            }
        }
        // Rückgabe der Instanz, die entweder neu erstellt oder bereits vorhanden ist.
        return instance;
    }
    /*Der Code ist thread sicher. Die Methode getInstance() verwendet eine synchronized-Blockierung,
    um sicherzustellen, dass nur eine Instanz des Caches erstellt wird.
    Die synchronized-Blockierung verhindert auch, dass zwei Threads gleichzeitig auf die Methode put() oder get() zugreifen.

    Die Methode put() verwendet eine containsKey()-Methode, um zu überprüfen, ob der Schlüssel bereits
    im Cache vorhanden ist. Wenn der Schlüssel bereits vorhanden ist, wird der Wert nicht erneut hinzugefügt.
    Dies verhindert, dass der Cache unnötigerweise gelöscht wird.

    Die Methode get() verwendet eine getOrDefault()-Methode, um den Wert aus der HashMap zurückzugeben.
    Wenn der Schlüssel nicht vorhanden ist, wird null zurückgegeben.
    Dies verhindert, dass die Methode einen Null-Wert zurückgibt.

    Insgesamt ist der Lazy robust und sicher und kann daher in einer Produktionsumgebung verwendet werden.

    Details, die die Threadsicherheit des Codes gewährleisten:

   -Die synchronized-Blockierung in der Methode getInstance() wird nur für den Fall verwendet,
    dass die Instanz des Caches noch nicht erstellt wurde. Wenn die Instanz bereits erstellt wurde,
    wird die synchronized-Blockierung nicht verwendet. Dies optimiert die Leistung des Codes.
   -Die containsKey()-Methode in der Methode put() verwendet eine synchronized-Blockierung,
    um sicherzustellen, dass der Schlüssel nur einmal überprüft wird.
   -Die getOrDefault()-Methode in der Methode get() verwendet eine synchronized-Blockierung,
    um sicherzustellen, dass der Wert nur einmal aus der HashMap abgerufen wird.
    Diese Details tragen dazu bei, dass der Code threadsicher ist und gleichzeitig effizient ist.*/

}
