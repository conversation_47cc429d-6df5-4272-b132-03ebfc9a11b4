package com.example.mcpjavaapp.controller;

import com.example.mcpjavaapp.service.SimpleOllamaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Einfacher Controller für die Ollama-Integration ohne Spring AI.
 * Basiert auf der Implementierungsanleitung.
 */
@RestController
@RequestMapping("/api")
public class SimpleOllamaController {

    private final SimpleOllamaService ollamaService;

    @Autowired
    public SimpleOllamaController(SimpleOllamaService ollamaService) {
        this.ollamaService = ollamaService;
    }

    /**
     * Test-Endpunkt um zu überprüfen, ob die Anwendung läuft.
     */
    @GetMapping("/test")
    public String test() {
        return "Die Anwendung läuft erfolgreich!";
    }

    /**
     * Endpunkt für allgemeine Anfragen an das LLM.
     */
    @GetMapping("/generate")
    public String generate(@RequestParam(value = "prompt", defaultValue = "Erkläre mir das Model Context Protocol in einfachen Worten") String prompt) {
        return ollamaService.generateCompletion(prompt);
    }

    /**
     * Endpunkt für Code-Analyse.
     */
    @PostMapping("/analyze")
    public String analyzeCode(@RequestParam String code) {
        return ollamaService.analyzeCode(code);
    }

    /**
     * Endpunkt für Dokumentationsgenerierung.
     */
    @PostMapping("/document")
    public String generateDocumentation(@RequestParam String code) {
        return ollamaService.generateDocumentation(code);
    }

    /**
     * Endpunkt für Code-Verbesserung.
     */
    @PostMapping("/improve")
    public String improveCode(@RequestParam String code) {
        return ollamaService.improveCode(code);
    }
}
