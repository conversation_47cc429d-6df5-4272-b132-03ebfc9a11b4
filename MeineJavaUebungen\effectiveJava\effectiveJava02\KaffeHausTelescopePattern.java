package effectiveJava.effectiveJava02;

public class KaffeHausTelescopePattern {

    // Nötiger Pflichtparameter, natürlich könnten wir mehr an nötigen
    // Parametern wie: Kaffee Typ usw. und diverses mehr als obligatorischer Parameter geben.
    private String kaffeeGroesse;


    // gegen Auswahl ändern
    private String laktoseFreiMilch;
    private String kakao;
    private String vanille;


    // Primärer Konstruktor mit dem obligatorischen Parameter "kaffeeGroesse"
    public KaffeHausTelescopePattern(String kaffeeGroesse) {
        this.kaffeeGroesse = kaffeeGroesse;
    }

    // Konstruktor, der laktoseFreiMilch hinzufügt, nutzt den primären Konstruktor
    public KaffeHausTelescopePattern(String kaffeeGroesse, String laktoseFreiMilch) {
        this(kaffeeGroesse); // Nutzt den primären Konstruktor
        this.laktoseFreiMilch = laktoseFreiMilch;
    }

    // Konstruktor, der kakao hinzufügt, nutzt den vorherigen Konstruktor
    public KaffeHausTelescopePattern(String kaffeeGroesse, String laktoseFreiMilch, String kakao) {
        this(kaffeeGroesse, laktoseFreiMilch); // Nutzt den vorherigen Konstruktor
        this.kakao = kakao;
    }

    // Konstruktor, der vanille hinzufügt, nutzt den vorherigen Konstruktor
    public KaffeHausTelescopePattern(String kaffeeGroesse, String laktoseFreiMilch, String kakao, String vanille) {
        this(kaffeeGroesse, laktoseFreiMilch, kakao); // Nutzt den vorherigen Konstruktor
        this.vanille = vanille;
    }




}
