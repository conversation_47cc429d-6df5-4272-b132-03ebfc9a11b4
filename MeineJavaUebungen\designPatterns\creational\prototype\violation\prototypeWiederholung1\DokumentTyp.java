package designPatterns.creational.prototype.violation.prototypeWiederholung1;

public class DokumentTyp {

    private long id;
    private String name;

    public DokumentTyp() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }
}
