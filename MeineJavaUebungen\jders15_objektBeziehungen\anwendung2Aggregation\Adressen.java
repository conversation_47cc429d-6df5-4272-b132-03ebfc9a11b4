package jders15_objektBeziehungen.anwendung2Aggregation;

public class Adressen {

    private String hausnummer;

    private String strasse;

    private String postleitzahl;

    private String stadt;

    private String land;

    public Adressen() {

    }

    public Adressen(String hausnummer, String strasse, String postleitzahl, String stadt, String land) {
        this.hausnummer = hausnummer;
        this.strasse = strasse;
        this.postleitzahl = postleitzahl;
        this.stadt = stadt;
        this.land = land;
    }

    public String getHausnummer() {
        return hausnummer;
    }

    public void setHausnummer(String hausnummer) {
        this.hausnummer = hausnummer;
    }

    public String getStrasse() {
        return strasse;
    }

    public void setStrasse(String strasse) {
        this.strasse = strasse;
    }

    public String getPostleitzahl() {
        return postleitzahl;
    }

    public void setPostleitzahl(String postleitzahl) {
        this.postleitzahl = postleitzahl;
    }

    public String getStadt() {
        return stadt;
    }

    public void setStadt(String stadt) {
        this.stadt = stadt;
    }

    public String getLand() {
        return land;
    }

    public void setLand(String land) {
        this.land = land;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Adressen{");
        sb.append("Hausnummer='").append(hausnummer).append('\'');
        sb.append(", Straße='").append(strasse).append('\'');
        sb.append(", Postleitzahl='").append(postleitzahl).append('\'');
        sb.append(", Stadt='").append(stadt).append('\'');
        sb.append(", Land='").append(land).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
