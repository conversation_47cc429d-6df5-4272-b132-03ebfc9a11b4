package jders13_objekte._07_anwendungFinal;

public class Test {
    public static void main(String[] args) {

        Student student1 = new Student();

        // da final attribute vom Objekt für alle aus der Klasse geltend sind, wird der Aufruf über die Klasse erfolgen

        // Aufruf erfolgt ohne Static über die erstellte Instanz
        // System.out.println(student1.schule);

        System.out.println(Student.SCHULE);

    }
}
