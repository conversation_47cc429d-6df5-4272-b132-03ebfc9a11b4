package jders21_ordnerVerarbeitungen.anwendung2ScannerWiederholung;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Scanner;

public class DateiLesen {
    public static void main(String[] args) {

        /* In unserem Fall können wir den Scanner im try-Block öffnen, und wenn der Block verlassen wird
        (egal ob aufgrund einer normalen Beendigung oder einer Ausnahme), wird der Scanner automatisch geschlossen.*/

        File datei = new File("C:/Users/<USER>/Desktop/Projeler/Dokument2.txt");

        try {

            Scanner sc = new Scanner(datei);

            String testDokument = null;

            while (sc.hasNextLine()) { // Boolean = true

                //testDokument = sc.nextLine();

                // Print sollte in der Schleife Stehen wegen nextLine wo durch die erste Zeile ansonsten mit der zweiten Zeile
                // überschrieben wird für die Verarbeitung, da wir sonst auch wegen sc.hasNext im fall: false einen Fehler erhalten würden.
                // Hinzu ist noch der wert null zugewiesen mit ausgegeben werden würde, natürlich könnten wir den Print
                // auch außerhalb nutzen jedoch müsste der code dem entsprechend modifiziert werden, was auch im Nachhinein wir tun.
                // testDokument = testDokument + sc.nextLine();
                // testDokument = testDokument + " ";
                // abgekürzt
                testDokument += sc.nextLine();
                testDokument += " ";

            }

            System.out.println(testDokument);

        } catch (FileNotFoundException e) {
            // Ausgabe der ToString Methode von FileNotFoundException
            System.err.println("Fehler : " + e);
        } /*finally {
            if (sc != null) {
                sc.close();
            }
            Sie sollten sich für finally entscheiden, wenn Sie feinere Kontrolle über die Ressourcenfreigabe benötigen
            oder wenn Sie zusätzlichen Code ausführen müssen, der nach dem try-Block
            unabhängig vom Auftreten einer Ausnahme ausgeführt werden soll.
              Hier sind einige Situationen, in denen die Verwendung von finally möglicherweise geeigneter ist als try-mit-Ressourcen:
              -Benutzerdefinierte Ressourcenfreigabe: Wenn Sie benutzerdefinierte Ressourcen oder komplexe Freigabelogik haben,
               die nicht direkt von den in Java bereitgestellten AutoCloseable-Klassen abgedeckt wird, kann es sinnvoll sein,
               dies im finally-Block zu verwalten.

              -Mehrere Ressourcen: Wenn Sie mehrere Ressourcen geöffnet haben und sicherstellen müssen, dass alle ordnungsgemäß
               geschlossen werden, können Sie dies im finally-Block tun.

              -Zusätzlicher Code: Wenn Sie zusätzlichen Code ausführen müssen, der nach dem try-Block unabhängig vom Auftreten
               einer Ausnahme ausgeführt werden soll, ist finally eine bessere Wahl.

              -Spezielle Aktionen vor dem Verlassen: Wenn Sie vor dem Verlassen des Blocks spezielle Aktionen durchführen müssen,
              die nicht direkt mit der Freigabe von Ressourcen zu tun haben, kann der finally-Block nützlich sein.
              
              Denken Sie daran, dass die Wahl zwischen finally und try-mit-Ressourcen von der spezifischen Logik und
              Anforderungen Ihres Codes abhängt. Wenn die Ressourcenfreigabe einfach und standardmäßig ist,
              ist try-mit-Ressourcen oft die bessere Wahl. Wenn Sie jedoch mehr Kontrolle
              oder zusätzliche Aktionen benötigen, kann finally geeigneter sein. */
    }
}
