package jders21_ordnerVerarbeitungen.anwendung3;

public class Test {
    public static void main(String[] args) {

        // Date<PERSON>, Schreibvorgang
/*
        // Instanzen erstellen
        Student student1 = new Student("<PERSON><PERSON><PERSON>", "Butter", 1989, "2389");
        Student student2 = new Student("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", 1990, "2390");
        Student student3 = new Student("<PERSON>", "Butter", 1991, "2391");

        // Obje<PERSON> von der DateiSchreiben-Klasse erstellen
        DateiSchreiben dateiSchreiben = new DateiSchreiben();

        // Wir erhalten boolean Rückgabe bei dateiSchreiben.dateiErstellen("studenten1");
        // Diesem Objekt werden wir später unsere Instanzen zuweisen und ihre Rückgabewerte speichern
        boolean dateiErstellenZustand = dateiSchreiben.dateiErstellen("studenten");

        if (dateiErstellenZustand){
            System.out.println("Datei wurde erfolgreich erstellt.");
        }
        else {
            System.out.println("Fehler beim Erstellen der Datei festgestellt!");
        }

        // wenn nicht erfolgreich die Datei geschrieben wird, werden wir wahrscheinlich unerwartete Fehler erhalten!
        dateiSchreiben.studentSpeichern(student1);
        dateiSchreiben.studentSpeichern(student2);
        dateiSchreiben.studentSpeichern(student3);

        boolean dateiErstellenSchliessenZustand = dateiSchreiben.prnitWriterSchliessen();

        if (dateiErstellenSchliessenZustand){
            System.out.println("Datei wurde erfolgreich geschlossen.");
        }
        else {
            System.out.println("Fehler beim schließen der Datei festgestellt!");
        }
*/

        DateiLesen dateiLesen = new DateiLesen();

        dateiLesen.zuLesendeDatei("studenten1");
        //dateiLesen.leseDateiZeilen();
        dateiLesen.leseDateiWoerter();
        dateiLesen.dateiLesenSchliessen();
    }
}
