package jders12_stringMethoden;

import java.nio.charset.Charset;
import java.util.Arrays;

public class UmlautErsetzung2 {

    private static String[][] UMLAUT_REPLACEMENTS = {
            { "Ä", "Ae" },
            { "Ü", "Ue" },
            { "Ö", "Oe" },
            { "ä", "ae" },
            { "ü", "ue" },
            { "ö", "oe" },
            { "ß", "ss" }
    };

    public static void main(String[] args) {
        String input = "Özer Özözbek"; // Originaler String

        String umlauteErsetzt = replaceUmlaute(input); // Umlaute ersetzen

        System.out.println("Original: " + input);
        System.out.println("Ersetzt: " + umlauteErsetzt);
    }

    /* In dieser Version wurde die Methode replaceUmlaute um den Code zur Überprüfung der Zeichenkodierung erweitert.
     * Dabei wird die Standardkodierung (defaultCharset()) verwendet,
     * um die Byte-Repräsentationen des Originals und der Umlaute zu vergleichen.
     * Bei Bedarf wird die Umlaut-Ersatzliste angepasst und die Umlaute im Eingabestring ersetzt.*/
    /* Die Byte-Repräsentationen werden mit Arrays.toString() ausgegeben,
     * damit Sie die Ergebnisse überprüfen können. Beachten Sie, dass in diesem Beispiel die Standardkodierung
     * des Systems verwendet wird. Je nach Umgebung und Datenquelle müssen Sie
     * möglicherweise die Kodierung entsprechend anpassen.*/
    public static String replaceUmlaute(String input) {
        String result = input;

        // Überprüfen und Anpassen der Zeichenkodierung
        try {
            String originalEncoding = Charset.defaultCharset().name();

            // Überprüfen der Byte-Repräsentation des Originals
            byte[] originalBytes = input.getBytes(originalEncoding);
            System.out.println("Original Bytes: " + Arrays.toString(originalBytes));

            // Überprüfen und Anpassen der Umlaut-Ersatzliste
            for (int i = 0; i < UMLAUT_REPLACEMENTS.length; i++) {
                String umlaut = UMLAUT_REPLACEMENTS[i][0];
                String ersatz = UMLAUT_REPLACEMENTS[i][1];

                // Überprüfen der Byte-Repräsentation des Umlauts
                byte[] umlautBytes = umlaut.getBytes(originalEncoding);
                System.out.println("Umlaut Bytes: " + Arrays.toString(umlautBytes));

                // Vergleichen der Byte-Repräsentationen und Anpassen bei Bedarf
                if (!Arrays.equals(originalBytes, umlautBytes)) {
                    result = result.replace(umlaut, ersatz);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

}

