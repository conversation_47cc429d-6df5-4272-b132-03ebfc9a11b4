package designPatterns.creational.abstractFactory;

public class S23 implements Handy {

    private String marke;  // Hier wird die Marke des Handys gespeichert
    private String model;  // Hier wird das Modell des Handys gespeichert
    private int groesse;    // Hier wird die Größe des Handys gespeichert
    private int gewicht;    // Hier wird das Gewicht des Handys gespeichert

    public S23(String marke, String model, int groesse, int gewicht) {
        this.marke = marke;
        this.model = model;
        this.groesse = groesse;
        this.gewicht = gewicht;
    }

    @Override
    public String getMarke() {
        return null;
    }

    @Override
    public String getModel() {
        return null;
    }

    @Override
    public int getGroesse() {
        return 0;
    }

    @Override
    public int getGewicht() {
        return 0;
    }

    @Override
    public String toString() {
        return "S23{" +
                "marke='" + marke + '\'' +
                ", model='" + model + '\'' +
                ", groesse=" + groesse +
                ", gewicht=" + gewicht +
                '}';
    }
}
