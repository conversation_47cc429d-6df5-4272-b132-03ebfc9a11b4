package designPatterns.creational.abstractFactory;

public class Test {
    public static void main(String[] args) {

        // Da in static Methoden unsere Factory-Methode erst erstellt werden muss
        S23Factory s23Factory = new S23Factory();

        Handy s23 = s23Factory.getHandy("s23", "S", 122, 76);

        S22Factory s22Factory = new S22Factory();

        Handy s22 = s22Factory.getHandy("s22", "S", 118, 89);

        System.out.println(s23);
        System.out.println(s22);
    }
}
