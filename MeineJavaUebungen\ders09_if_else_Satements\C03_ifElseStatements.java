package ders09_if_else_Satements;

import java.util.Scanner;

public class C03_ifElseStatements {
    public static void main(String[] args) {
        // Die Note vom Nutzer Einlesen,
        // die Note einstufen und ausgeben

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte Note Eingeben");
        double note = scan.nextDouble();


        if (note > 100 || note < 0) {       // false||false -> false
            System.out.println("Ungültige Note!");
        } else if (note >= 85) {
            System.out.println("Note AA");
        } else if (note >= 65) {
            System.out.println("Note BB");
        } else if (note >= 50) {
            System.out.println("Note CC");
        } else {
            System.out.println("Note DD");
        }
    }
}
