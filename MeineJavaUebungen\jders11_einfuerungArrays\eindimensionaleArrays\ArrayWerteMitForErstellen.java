package jders11_einfuerungArrays.eindimensionaleArrays;

import java.util.Scanner;

public class ArrayWerteMitForErstellen {
    public static void main(String[] args) {

        Scanner sc = new Scanner(System.in);
        int arrayLaenge;

        System.out.print("Geben sie die Länge ihres Arrays ein : ");
        arrayLaenge = sc.nextInt();  // Wird als Angabe des Arrays genutzt

        int[] zahlen = new int[arrayLaenge];  // Größe des Arrays muss angegeben werden
        /*
        Ein Leeren Array nachträglich anpassen
        zahlen = new int [10];
        */

        // (int i = 0; i < arrayLaenge; i++
        for (int i = 0; i < zahlen.length; i++) {

            System.out.print((i + 1) + ". Geben sie eine Zahl ins Array ein : ");  // Gibt immer den i wert mit aus, (i+1) nur Einfluss auf die Ausgabe von i
            zahlen[i] = sc.nextInt();
        }

        System.out.println();

        for (int i = 0; i < zahlen.length; i++) {

            System.out.println("Im " + (i + 1) + ". befindet sich der Wert " + zahlen[i]);
        }

        System.out.println();

        for (int i = 0; i < zahlen.length; i++) {

            System.out.print(zahlen[i] + " "); // 15 25 4 0
        }
    }


}
