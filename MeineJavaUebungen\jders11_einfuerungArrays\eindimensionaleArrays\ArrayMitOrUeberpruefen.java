package jders11_einfuerungArrays.eindimensionaleArrays;

import java.util.Scanner;

public class ArrayMitOrUeberpruefen {
    public static void main(String[] args) {

        Scanner sc = new Scanner(System.in);
        int tag;

        System.out.print("geben sie die Zahl vom Tag ein : ");
        tag = sc.nextInt();

        String[] tage = {"Montag", "Dienstag", "Mittwoch", "Donnerstag", "Freitag", "Samstag", "Sonntag"};  // Index 0 - 6

        if (tag >= 1 || tag <= tage.length) { // Überprüfung der Eingabe

            System.out.println("kein über eintreffen der tag mit dieser Zahl");

        }else {

            System.out.println(tage[tag - 1]); // zieht -1 ab von der Nutzereingabe
        }


    }


}
