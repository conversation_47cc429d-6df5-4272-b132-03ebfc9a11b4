package ders14_scope;

public class C02_ClassLevelVariables {

    static boolean bls;
    boolean bli;

    static String strs = "Java";
    String stri;

    static int zahls;
    int zahli = 23;

    static char chrs = 'a';
    char chri;


    public static void main(String[] args) {

        // static variablen
        System.out.println(bls);  // false
        System.out.println(strs);  // Java
        System.out.println(zahls);  // 0
        System.out.println(chrs);  // a

        // Von der Klasse ein Objekt erstellen
        C02_ClassLevelVariables obj = new C02_ClassLevelVariables();

        // instance variablen
        System.out.println(obj.bli);  // false
        System.out.println(obj.stri);  // null
        System.out.println(obj.zahli);  // 23
        System.out.println(obj.chri);  //  

    }

    /* Klass Level Regeln
     * 1- class level variablen können ohne Wert-Zuweisung erstellt und genutzt werden,
     * wenn kein Wert zugewiesen ist, wird java seine default werte zuweisen
     * boolen ==> false
     * zahlen variablen ==> 0
     * non-primitive variablen ==> null
     * char ==>'' als char leer '(kein Zeichen)' nichts in der console zu sehen nur, 
     *
     * 2- instance variablen sind in static Methoden nicht direkt zu erreichen,
     * wenn wir innerhalb einer static Methode eine instance variable nutzen wollen,
     * müssen wir von der Klasse das Objekt erstellen, und über dieses Objekt können
     * wir unsere instance variablen erreichen
     *
     * 3- wenn wir in anderen klassen class variablen erreichen wollen,
     * können wir static variablen über className.classLevelStaticVariable erreichen
     * für das ereichen der instance variable, müssen wir die klasse wo sich
     * die variablen befinden von der klasse selbst ein ojekt erstellen
     *
     * wenn wir eine static variable über das objekt erreichen wollen,
     * bingt java dies nicht automatisch, aber aktzeptiert es per hand eingabe
     * in der ide ist der code in gelber farbe die warnung das die static variable
     * sich zur einer instance variable referenziert
     * */

}
