package com.example.mcpjavaapp.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.http.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * Einfacher Service für die Kommunikation mit Ollama ohne Spring AI.
 * Basiert auf der Implementierungsanleitung für direkte API-Kommunikation.
 */
@Service
public class SimpleOllamaService {

    private static final Logger log = LoggerFactory.getLogger(SimpleOllamaService.class);
    
    private final WebClient webClient;
    private final String modelName;

    public SimpleOllamaService(@Value("${ollama.base-url:http://localhost:11434}") String baseUrl,
                              @Value("${ollama.model:phi4-mini-reasoning:3.8b}") String modelName) {
        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .build();
        this.modelName = modelName;
        log.info("SimpleOllamaService initialisiert mit Base URL: {} und Modell: {}", baseUrl, modelName);
    }

    /**
     * Analysiert Java-Code auf Bugs, Performance-Probleme und Verbesserungsmöglichkeiten.
     */
    public String analyzeCode(String code) {
        String prompt = "Du bist ein erfahrener Java-Entwickler und Code-Reviewer. " +
                       "Analysiere den folgenden Java-Code auf Bugs, Performance-Probleme und Verbesserungsmöglichkeiten. " +
                       "Gib konkrete Verbesserungsvorschläge:\n\n```java\n" + code + "\n```";
        
        return sendChatRequest(prompt);
    }

    /**
     * Generiert Dokumentation für Java-Code.
     */
    public String generateDocumentation(String code) {
        String prompt = "Du bist ein erfahrener Java-Entwickler, der ausführliche Dokumentation erstellt. " +
                       "Erstelle eine detaillierte Dokumentation für den folgenden Java-Code im Markdown-Format:\n\n```java\n" + 
                       code + "\n```";
        
        return sendChatRequest(prompt);
    }

    /**
     * Verbessert Java-Code für bessere Lesbarkeit und Performance.
     */
    public String improveCode(String code) {
        String prompt = "Du bist ein erfahrener Java-Entwickler. " +
                       "Verbessere den folgenden Java-Code für bessere Lesbarkeit und Performance. " +
                       "Gib nur den verbesserten Code zurück:\n\n```java\n" + code + "\n```";
        
        return sendChatRequest(prompt);
    }

    /**
     * Sendet eine allgemeine Anfrage an das Modell.
     */
    public String generateCompletion(String userPrompt) {
        return sendChatRequest(userPrompt);
    }

    /**
     * Sendet eine Chat-Anfrage an die Ollama-API.
     */
    private String sendChatRequest(String prompt) {
        log.info("Sende Anfrage an Ollama mit Modell: {}", modelName);
        log.debug("Prompt: {}", prompt);
        
        try {
            // Erstelle die Anfrage im Ollama-Chat-Format
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", prompt);

            List<Map<String, Object>> messages = new ArrayList<>();
            messages.add(message);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", modelName);
            requestBody.put("messages", messages);
            requestBody.put("stream", false); // Keine Streaming-Antwort

            // Sende die Anfrage
            String response = webClient.post()
                    .uri("/api/chat")
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            log.info("Erfolgreiche Antwort von Ollama erhalten");
            log.debug("Rohe Antwort: {}", response);

            // Extrahiere den Inhalt aus der Antwort
            return extractContentFromResponse(response);
            
        } catch (Exception e) {
            log.error("Fehler bei der Kommunikation mit Ollama: ", e);
            return "Fehler bei der Kommunikation mit Ollama: " + e.getMessage();
        }
    }

    /**
     * Extrahiert den Inhalt aus der Ollama-Antwort.
     * Da Ollama JSON zurückgibt, müssen wir den message.content extrahieren.
     */
    private String extractContentFromResponse(String response) {
        try {
            // Einfache JSON-Parsing für die message.content
            // In einer produktiven Anwendung sollten Sie eine JSON-Bibliothek verwenden
            if (response.contains("\"content\":")) {
                int startIndex = response.indexOf("\"content\":\"") + 11;
                int endIndex = response.indexOf("\"", startIndex);
                if (endIndex > startIndex) {
                    return response.substring(startIndex, endIndex)
                            .replace("\\n", "\n")
                            .replace("\\\"", "\"")
                            .replace("\\\\", "\\");
                }
            }
            return response; // Fallback: ganze Antwort zurückgeben
        } catch (Exception e) {
            log.warn("Fehler beim Extrahieren des Inhalts, gebe ganze Antwort zurück: ", e);
            return response;
        }
    }
}
