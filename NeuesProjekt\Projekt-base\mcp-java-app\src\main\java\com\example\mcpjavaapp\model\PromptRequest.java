package com.example.mcpjavaapp.model;

// Lombok kann optional verwendet werden, um Getter/Setter/Konstruktoren zu generieren
// import lombok.Data;
// import lombok.NoArgsConstructor;
// import lombok.AllArgsConstructor;

// @Data
// @NoArgsConstructor
// @AllArgsConstructor
public class PromptRequest {

    private String prompt;

    // Standardkonstruktor (wird von <PERSON> für die Deserialisierung benötigt, falls kein @NoArgsConstructor von Lombok verwendet wird)
    public PromptRequest() {
    }

    public PromptRequest(String prompt) {
        this.prompt = prompt;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
}
