package effectiveJava.effectiveJava03;

import java.util.concurrent.ConcurrentHashMap;

public class LazyCache3<F, S> implements Cache2<F, S> {

    /* Hier sind die Hauptunterschiede und Verbesserungen von LazyCache3:

       Verwendung von ConcurrentHashMap: Statt einer normalen HashMap verwendet LazyCache3 ConcurrentHashMap.
       Dies ermöglicht eine sicherere und performantere Verwendung in einem Multi-Threaded-Umfeld.

       Expiration Time: LazyCache3 fügt die Idee der Ablaufzeit (Expiration Time) hinzu.
       Jeder Eintrag im Cache wird mit einem Zeitstempel versehen, und wenn die Ablaufzeit überschritten ist,
       wird der Eintrag aus dem Cache entfernt.

       Vereinfachte Methode get: In LazyCache2 gibt die get-Methode null zurück,
       wenn der Schlüssel nicht im Cache vorhanden ist.
       In LazyCache3 wird stattdessen null zurückgegeben, wenn die Ablaufzeit überschritten ist,
       und der Eintrag aus dem Cache entfernt wird. Dies ist ein besseres Verhalten, da es anzeigt,
       dass der Eintrag im Cache war, aber nicht mehr gültig ist.

       Keine Doppelte Überprüfung für Instanz: LazyCache2 verwendet das Doppelte-Überprüfungs-Idiom in der getInstance-Methode,
       um sicherzustellen, dass nur eine Instanz erstellt wird.
       In LazyCache3 verwenden wir einfach die Initialisierung von Instanzen in einem statischen Initialisierungsblock,
       was weniger komplex ist und dieselben Ergebnisse liefert.

       Thread-Sicherheit: LazyCache3 verwendet ConcurrentHashMap, um die Thread-Sicherheit beim Hinzufügen und Entfernen von Einträgen sicherzustellen. Dies macht den Code insgesamt sicherer für den Einsatz in einem Multi-Threaded-Umfeld.*/

    private final ConcurrentHashMap<F, S> map = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<F, Long> lastAccessTimes = new ConcurrentHashMap<>();
    private final long expirationTime;

    // Konstruktor, der die Ablaufzeit für die Einträge im Cache festlegt
    public LazyCache3(long expirationTime) {
        this.expirationTime = expirationTime;
    }

    // Methode zum Hinzufügen eines Werts zum Cache
    @Override
    public void put(F key, S value) {
        map.put(key, value);
        lastAccessTimes.put(key, System.currentTimeMillis());
    }

    // Methode zum Abrufen eines Werts aus dem Cache
    @Override
    public S get(F key) {
        long lastAccessTime = lastAccessTimes.get(key);
        if (lastAccessTime == 0 || System.currentTimeMillis() - lastAccessTime > expirationTime) {
            map.remove(key); // Wenn der Eintrag abgelaufen ist, wird er aus dem Cache entfernt
            return null;
        }
        return map.get(key);
    }

    // Methode zur Überprüfung, ob ein Schlüssel im Cache vorhanden ist
    @Override
    public boolean containsKey(F key) {
        return map.containsKey(key);
    }

    // Methode zur Rückgabe der Größe des Caches
    @Override
    public int size() {
        return map.size();
    }

    // Methode zum Löschen aller Einträge im Cache
    @Override
    public void clear() {
        map.clear();
        lastAccessTimes.clear();
    }

    // Statische Methode zum Erstellen einer Singleton-Instanz des LazyCache3
    public static LazyCache3 getInstance(long expirationTime) {
        synchronized (LazyCache3.class) {
            if (instance == null) {
                instance = new LazyCache3(expirationTime);
            }
        }
        return instance;
    }

    private static LazyCache3 instance;
}
