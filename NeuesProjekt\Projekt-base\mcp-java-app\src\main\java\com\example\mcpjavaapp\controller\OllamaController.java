package com.example.mcpjavaapp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.example.mcpjavaapp.service.OllamaService;

@RestController
public class OllamaController {

    private final OllamaService ollamaService;

    @Autowired
    public OllamaController(OllamaService ollamaService) {
        this.ollamaService = ollamaService;
    }

    @GetMapping("/ai/generate")
    public String generate(@RequestParam(value = "prompt", defaultValue = "<PERSON>rzähle mir einen Witz") String prompt) {
        return ollamaService.generateCompletion(prompt);
    }

    @GetMapping("/ai/analyze")
    public String analyzeCode(@RequestParam(value = "code") String code) {
        return ollamaService.analyzeCode(code);
    }

    @GetMapping("/ai/document")
    public String documentCode(@RequestParam(value = "code") String code) {
        return ollamaService.generateDocumentation(code);
    }

    @GetMapping("/ai/improve")
    public String improveCode(@RequestParam(value = "code") String code) {
        return ollamaService.improveCode(code);
    }
}
