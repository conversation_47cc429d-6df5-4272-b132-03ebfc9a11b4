package probecode03_concurrency.threads;

public class Counter {

    /* Dies dient als Beispiel wie die Daten unbrauchbar währen, in unserem Beispiel ist eine Datenbank Technologie
       dargestellt wie ein Datensatz    */

    private int count;

    /* Durch das Hinzufügen des synchronized Schlüsselworts zur increment() Methode wird sichergestellt,
       dass immer nur ein Thread gleichzeitig darauf zugreifen kann. Dies verhindert Race Conditions
       und stellt sicher, dass Ihr Counter korrekt funktioniert, wenn er von mehreren Threads verwendet wird.
       Dringend zu beachten ist, dass die Synchronisation Overhead verursacht und die Leistung beeinträchtigen kann,
       wenn sie nicht richtig verwendet wird. Es ist wichtig, sie nur dort einzusetzen, wo sie benötigt wird.*/
    public synchronized void increment(){
        this.count++;
    }

    public int getCount(){
        return this.count;
    }



}
