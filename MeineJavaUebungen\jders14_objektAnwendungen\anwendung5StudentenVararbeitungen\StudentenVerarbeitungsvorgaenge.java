package jders14_objektAnwendungen.anwendung5StudentenVararbeitungen;

import java.util.ArrayList;

public class StudentenVerarbeitungsvorgaenge {

    /*Wir erstellen eine statische Liste, die von allen Objekten dieser Klasse gemeinsam genutzt wird.
    Hier können die Studenten ihre Informationen zur Speicherung übergeben.
    In unserem Beispiel erstellen wir eine leere Liste.
    Da die einzige Instanzvariable studenten eine ArrayList ist,
    wird sie automatisch mit einer leeren ArrayList initialisiert.
    tatsächlich haben wir keinen expliziten Konstruktor. Das ist in Ordnung,
    solange wir keinen speziellen Initialisierungsbedarf haben.

    Gesendet wird Student aus der main Klasse in unserem Beispiel
    private static ArrayList<Student>() Dient auch als unsere Methode für alle Studenten.
    Unsere Liste wird beim Beenden und Starten des Programms neu erstellt!!.*/
    private static ArrayList<Student> studenten = new ArrayList<>();


    public void studentEinfuegen(Student stdEinfg) {
        // Dieses mal haben wir auch Auswahl für ein boolean.
        // Fügt den übergebenen Studenten der Liste hinzu und gibt den Erfolgsstatus zurück.
        boolean zustand = studenten.add(stdEinfg);

        if (zustand) { // true

            System.out.println("Der Student : " + stdEinfg.getVorname() + " wurde erfolgreich in die Liste Hinzugefügt.");
        } else {
            System.out.println("Bei Student : " + stdEinfg.getVorname() + " wurde ein Fehler beim Hinzufügen festgestellt!");
        }

    }

    public void studentEntfernen(Student stdEntf) {
        // Entfernt den übergebenen Studenten aus der Liste und gibt den Erfolgsstatus zurück.
        boolean zustand = studenten.remove(stdEntf);

        if (zustand) { // true
            // Wer hinzugefügt wurde Ausgeben
            System.out.println("Der Student : " + stdEntf.getVorname() + " Student wurde erfolgreich entfernt.");
        } else {
            // Bei welcher Instant ein Fehler besteht
            System.out.println("Bei Student : " + stdEntf.getVorname() +" wurde beim entfernen ein Fehler festgestellt ");
        }
    }

    public void studentenAuflisten() {
        // Wir nutzen unsere Liste in der for schleife und geben ihre Instanzen einzeln auszugeben
        for (Student stud : studenten) {

            System.out.println(stud);
        }
    }

    public static ArrayList<Student> getStudenten() {
        return studenten;
    }

    /*
    // Da wir bereits eine leere Liste haben, wollen wir keine Werte einfügen,
    // sondern direkt das Objekt mit den Einträgen vom Studenten senden, zum Speichern in die liste
    public static void setStudenten(ArrayList<Student> studenten) {
        studenten = studenten;
    }
    */

}
