package jders13_objekte._01_objekteEinfuerung.anwendung1ObjektAuto;

public class AutoTest {
    public static void main(String[] args) {

        Auto auto1;
        auto1 = new Auto();

        auto1.marke = "Mercedes";
        auto1.model = "E 390";
        auto1.farbe = "Schwarz";
        auto1.jahr = 2002;

        Auto auto2 = new Auto();

        auto2.marke = "BMW";
        auto2.model = "520d";
        auto2.farbe = "Rot";
        auto2.jahr = 2004;

        System.out.println("Marke : " + auto1.marke);
        System.out.println("Model : " + auto1.model);
        System.out.println("Farbe : " + auto1.farbe);

        /*
        Mit dem Import wird die LocalDate.now()-Methode verwendet, um das aktuelle Datum abzurufen.
        Anschließend wird das aktuelle Jahr extrahiert und vom Baujahr des Autos abgezogen,
        um das Alter zu berechnen. Das Alter wird dann ausgegeben. Beachte, dass das aktuelle Jahr
        auf Basis des Systems ermittelt wird, auf dem der Code ausgeführt wird.

        int aktuellesJahr = LocalDate.now().getYear();
        int alter = aktuellesJahr - auto1.jahr;
        System.out.println("Alter: " + alter + " Jahre");
        */
        System.out.println("Baujahr : " + (2023 - auto1.jahr));

        System.out.println("Marke : " + auto2.marke);
        System.out.println("Model : " + auto2.model);
        System.out.println("Farbe : " + auto2.farbe);
        System.out.println("Baujahr : " + (2023 - auto2.jahr));
    }
}
