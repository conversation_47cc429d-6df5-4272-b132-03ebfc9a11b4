package jders13_objekte._01_objekteEinfuerung.anwendung2ObjektAutomitConstructor;

public class AutoTest {
    public static void main(String[] args) {

        Auto auto1 = new Auto("Mercedes","S500",2010,"Schwarz");

        System.out.println("Erstes Auto");
        System.out.println("Marke : " + auto1.marke);
        System.out.println("Model : " + auto1.model);
        System.out.println("Farbe : " + auto1.farbe);
        System.out.println("Jahr : " + auto1.jahr);

        System.out.println();
        Auto auto2 = new Auto("Mercedes","E300",1999,"Blau");

        System.out.println("Zweites Auto");
        System.out.println("Marke : " + auto2.marke);
        System.out.println("Model : " + auto2.model);
        System.out.println("Farbe : " + auto2.farbe);
        System.out.println("Jahr : " + auto2.jahr);

        System.out.println();
        Auto auto3 = new Auto("Audi", "A8");

        //Vom Nutzer frei gelassene stellen oder noch nichts Eingetragenes vom Nutzer kann im Nachhinein so zugewiesen werden
        auto3.farbe = "Weiß";
        auto3.jahr = 2018;

        System.out.println("Drittes Auto");
        System.out.println("Marke : " + auto3.marke);
        System.out.println("Model : " + auto3.model);
        System.out.println("Farbe : " + auto3.farbe);
        System.out.println("Jahr : " + auto3.jahr);
    }
}
