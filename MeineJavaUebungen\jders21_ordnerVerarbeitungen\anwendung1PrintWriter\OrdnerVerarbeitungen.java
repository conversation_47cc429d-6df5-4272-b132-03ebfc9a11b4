package jders21_ordnerVerarbeitungen.anwendung1PrintWriter;

import java.io.FileNotFoundException;
import java.io.PrintWriter;

public class OrdnerVerarbeitungen {
    public static void main(String[] args) {

        /*Txt Erstellen mit Unicode-Unterstützung: PrintWriter unterstützt die Ausgabe von Unicode-Zeichen */

        /* Gültigkeit einer Variable ist auf den Block beschränkt, in dem sie deklariert wurde.
        Das bedeutet, dass die Variable printWriter nur innerhalb des try-Blocks existiert
        und verwendet werden kann. Sobald der try-Block verlassen wird, endet die Gültigkeit der Variablen.
        In diesem Fall bedeutet dies, dass die Variable printWriter nach dem try-Block nicht mehr
        verfügbar ist und nicht im catch-Block verwendet werden kann. Wenn Sie versuchen,
        auf die Variable printWriter im catch-Block zuzugreifen, wird ein Kompilierungsfehler auftreten,
        da die Variable in diesem Kontext nicht bekannt ist. */

        System.out.println("Der Schreibvorgang beginnt. ");
        try {
            // nur im try block gültig
            PrintWriter printWriter = new PrintWriter("C:/Users/<USER>/Desktop/Projeler/Dokument2.txt");

            printWriter.println("Guten Tag!");

            printWriter.close();
        } catch (FileNotFoundException e) {
            /* Gib eine Fehlermeldung aus, indem wir den Fehler in den Standardfehlerstrom (stderr) schreiben.
               Die Zeile beginnt mit "System.err", was auf den Standardfehlerstrom verweist,
               anstatt den Standardausgabestrom (stdout) zu verwenden, der bei "System.out" verwendet wird.
               Dadurch wird die Fehlermeldung in der Regel in der Konsole rot oder in einer anderen auffälligen Farbe angezeigt,
               um sie von normalen Ausgaben zu unterscheiden und sie als Fehler zu kennzeichnen.
               Die Fehlermeldung wird im folgenden Format ausgegeben: "Fehler : <Fehlermeldung>"
               Dabei wird der Text "Fehler : " als statischer Teil der Ausgabe hinzugefügt,
               gefolgt von der tatsächlichen Fehlermeldung, die in der Variablen "e" enthalten ist.
               Die Variable "e" enthält Informationen über die aufgetretene Exception, wie die Fehlermeldung und den Stack-Trace.
               Indem die Fehlermeldung über den Standardfehlerstrom ausgegeben wird,
               kann sie leichter von anderen Ausgaben im Standardausgabestrom (stdout) getrennt und erkannt werden.*/
            System.err.println("Fehler : " + e);
        }
        System.out.println("Der Schreibvorgang endet. ");
        /*
        //Zweite Möglichkeit
        try (PrintWriter printWriter = new PrintWriter("C:/Users/<USER>/Desktop/Projeler/Dokument.txt")) {
        } catch (FileNotFoundException e) {

            e.printStackTrace();
        }
        */

    }
}
