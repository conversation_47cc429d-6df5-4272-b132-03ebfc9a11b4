package designPatterns.creational.prototype.prototypeWiederholung4DeepCopy;

public class AllgemeinEntitaetsService {

    // Methode zum Suchen eines Dokumenttyps anhand seiner ID.
    public DokumentTyp findDokumentTypById(Long id) {

        // Die Klasse AllgemeinEntitaetsService bietet Methoden zum Abrufen von Dokumenttypen, Kategorien und Dokumenten.

        // für das hin und her zur Database lassen wir für Testzwecke eine sek. warten
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            System.err.println(e);
        }

        // Da wir keine Datenbank nutzen werden erstellen wir an der stelle
        DokumentTyp dokumentTyp = new DokumentTyp();
        dokumentTyp.setId(id);

        String name;
        if (id.compareTo(1L) == 0) {
            name = "Persönlich";
        } else if (id.compareTo(2L) == 0) {
            name = "Betrieblich";
        } else {
            name = "<PERSON><PERSON>";
        }

        dokumentTyp.setName(name);

        return dokumentTyp;
    }

    // Methode zum Suchen einer Kategorie anhand ihrer ID.
    public Kategorie findKategorieById(Long id) {

        // Dies ist ein Beispiel für eine künstliche Verzögerung, die in
        // einer realen Anwendung verwendet werden könnte, um die Leistung zu simulieren
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            System.err.println(e);
        }

        // Da wir keine Datenbank nutzen werden erstellen wir an der stelle
        Kategorie kategorie = new Kategorie();
        kategorie.setId(id);

        String name;
        if (id.compareTo(1L) == 0) {
            name = "Privat";
        } else if (id.compareTo(2L) == 0) {
            name = "Arbeit";
        } else {
            name = "Sonstiges";
        }

        kategorie.setName(name);

        return kategorie;
    }

    // Methode zum Suchen eines Dokuments anhand seiner ID.
    public Dokument findDokumentById(Long id) {
        // Simuliert den Zugriff auf eine Datenbank mit Verzögerung.
        // Erstellt einen DokumentTyp und setzt ID und Name basierend auf der ID.
        // Gibt den erstellten DokumentTyp zurück.

        // für das hin und her zur Database lassen wir eine sek. warten
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            System.err.println(e);
        }

        Dokument dokument = new Dokument();
        dokument.setId(id);
        dokument.setDokumentTyp(findDokumentTypById(id));
        dokument.setKategorie(findKategorieById(id));

        String name;
        String datei;
        if (id.compareTo(1L) == 0) {
            name = "Brief";
            datei = "Glückwünsche, Lieber Coder";
        } else if (id.compareTo(2L) == 0) {
            name = "Bericht";
            datei = "Regressionstest Ergebnisse";
        } else {
            name = "Hausordnung";
            datei = "Ordnungsvereinbarung";
        }

        dokument.setName(name);
        dokument.setDatei(datei);

        return dokument;
    }
}
