package ders08_einfache_if_Saetze;

import java.util.Scanner;

public class C03_ifStatements {
    public static void main(String[] args) {
        // eine Zahl der Nutzer Eingabe wird überprüft,
        // ob sie durch 5 oder durch 3 teilbar ist, ausgegeben wird dass, die zahl durch 5 oder durch 3 teilbar ist.

        // Ohne Geschweifte klammern setzt sich die Ausführung fort bis zu nächsten Semikolon

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie eine positive ganze Zahl ein!");
        int eingabeZahl = scan.nextInt();

        if (eingabeZahl % 3 == 0) {
            System.out.println("Die Eingabe ist eine durch 3 teilbare Zahl");
        }

        if (eingabeZahl % 5 == 0) {
            System.out.println("Die Eingabe ist eine durch 5 teilbare Zahl");
        }
        // nur wenn Bedingung stimmt, wird alles innerhalb der Geschweiften klammern ausgeführt
        if (eingabeZahl % 5 == 0 && eingabeZahl % 3 == 0) {
            System.out.println("Somit ist Die Eingegebene Zahl eine durch 5 und durch 3 teilbare Zahl");
            System.out.println("Schönen Feierabend noch!!!");
        }
        // if ohne Geschweifte klammern
        if (eingabeZahl % 2 == 0) System.out.println("Du hast eine gerade Zahl gewählt");
        System.out.println("Frohes schaffen!");
    }
}
