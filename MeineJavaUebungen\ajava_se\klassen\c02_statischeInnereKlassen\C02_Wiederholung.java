package ajava_se.klassen.c02_statischeInnereKlassen;

public class C02_Wiederholung {

    // Hinweis: Diese statische innere Klasse hat keinen direkten Zugriff auf nicht-statische Member der äußeren Klasse.
    // Um dennoch auf Instanzvariablen wie 'zahl' zuzugreifen, muss explizit eine neue Instanz von 'Wiederholung2' erzeugt werden.

    /*
     * Fortgeschrittene Info:
     * Im Bytecode wird diese Klasse als separate Datei mit Namen 'Wiederholung2$StatischeInnere.class' gespeichert.
     * Dadurch ist sie auch technisch klar von ihrer äußeren Klasse getrennt, obwohl sie logisch zu ihr gehört.
     */

    /*
     * Didaktische Ergänzung:
     * Diese Klasse demonstriert, dass der Zugriff auf Instanzdaten der äußeren Klasse nur über eine manuell erzeugte
     * Instanz funktioniert. Dies liegt daran, dass statische Klassen keinen Bezug (kein implizites this) zur äußeren Instanz haben.
     */
    private static int zahl = 8;
    private int instanzZahl = 25;

    public static class StatischeInnereKlasse {
        public void ausgeben() {
            // Zugriff auf die private Variable der äußeren Klasse
            System.out.println("Zahl aus statischer innerer Klasse: " + zahl);
            
            // Der Zugriff auf nicht-statische Member erfordert eine eigene Instanz der äußeren Klasse,
            // da statische Klassen keinen direkten Zugriff auf Instanzvariablen haben.
            C02_Wiederholung aussen = new C02_Wiederholung();
            System.out.println("Instanz-Zahl über neue äußeräe Instanz: " + aussen.instanzZahl);
        }
        
    }
    public static void main(String[] args) {
        C02_Wiederholung.StatischeInnereKlasse staticInner = new C02_Wiederholung.StatischeInnereKlasse();
        staticInner.ausgeben(); // Direkter Zugriff auf die innere Klasse
    }
}

