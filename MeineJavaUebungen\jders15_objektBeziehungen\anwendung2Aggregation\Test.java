package jders15_objektBeziehungen.anwendung2Aggregation;

public class Test {
    public static void main(String[] args) {

        /*Die Beziehung zwischen den Klassen "Student" und "Adressen" kann als Aggregation betrachtet werden.
        Aggregation ist eine spezielle Form der Assoziation, bei der ein Objekt (in diesem Fall ein "Student")
        ein anderes Objekt (die "Adressen") enthält. Die enthaltene Klasse ("Adressen") kann jedoch auch
        unabhängig vom enthaltenden Objekt existieren. Mit anderen Worten, die "Adressen" sind Teil des "Student",
        aber sie sind nicht direkt von ihm abhängig. Wenn der "Student" gelöscht wird, bleiben
        die "Adressen" normalerweise weiterhin bestehen.
        In diesem Beispiel ist der "Student" die enthaltende Klasse, und die "Adressen" sind die enthaltene Klasse.
        Ein "Student" kann eine Adresse haben (und hat sogar ein Attribut vom Typ "Adressen"), aber eine "Adresse" kann auch unabhängig von einem "Student" existieren.*/

        Student student1 = new Student("Ben", "Hanse", 1996, "2360", null);

        Adressen adresse1 = new Adressen("20i", "Bürgerplatz", "10600", "Berlin", "Deutschland");

        // Wenn wir keine Adresse dem Studenten zuweisen erhalten wir ein Fehler mit der Ausgabe NullPointerException.
        // weil wenn keine Werte zugewiesen sind, wird in der Methode ein null Wert übergeben
        // und es können keine nicht existierenden Instanz werte ausgegeben werden
        student1.setAdressen(adresse1);

        // 2. Student und neue Adresse erstellen, mit werten zu beiden Klassen füllen. Am Ende weißen wir student2 adresse1 zu.
        Student student2 = new Student("Simon", "Trick", 1998, "2361", null);
        Adressen adresse2 = new Adressen("106b", "Platz der Luftbrücke", "10115", "Berlin", "Deutschland");
        student2.setAdressen(adresse2);

        // Wir können auch 3. Student und neue Adresse so erstellen und mit werten zu beiden Klassen befüllen. Am Ende student2 zu adresse1 zuweisen.
        // Weil Wir es Oben schon geben können wir es unten setzen aber nicht anders herum.
        Adressen adresse3 = new Adressen("18A", "Schiller Straße", "10005", "Berlin", "Deutschland");
        Student student3 = new Student("Fredy", "Brown", 1997, "2362", adresse3);

        // Hier tun wir Objekt der eigenen Klasse erstellen, um Methoden die nicht static sind zu erreichen
        Test test = new Test();
        // Senden zur Methode ohne static infosAusgeben
        test.infosAusgeben(student1);
        System.out.println("-----------NUN FOLGT DIE STATISCHE AUSGABE METHODE--------------");

        // Senden zur static void infosAusgabeStatisch Methode
        infosAusgabeStatisch(student1);
        System.out.println();
        infosAusgabeStatisch(student2);
        System.out.println();
        infosAusgabeStatisch(student3);
    }

    public static void infosAusgabeStatisch(Student stu) {

        System.out.println("Name : " + stu.getVorname());
        System.out.println("Nachname : " + stu.getNachname());
        System.out.println("Geburtsjahr : " + stu.getGeburtsjahr());
        System.out.println("Studenten Nummer : " + stu.getStudentenNummer());

        // Wir nutzen das Adressen Objekt um die Instanzen und deren werte zu holen,
        // da neue Adresse bereits erstellt wurde und hir übergeben wird.
        Adressen a = stu.getAdressen();

        // Ausgabe mit der toString  Methode des Adressen Objekt
        System.out.println("Land : " + a.getLand());
        System.out.println("Stadt : " + a.getStadt());
        System.out.println("Postleitzahl : " + a.getPostleitzahl());
        System.out.println("Straße : " + a.getStrasse());
        System.out.println("Hausnummer : " + a.getHausnummer());


    }

    // ohne static müsste in der Objektklasse ein Objekt, der eigenen klasse erstellt werden
    public void infosAusgeben(Student std) {

        System.out.println("Name : " + std.getVorname());
        System.out.println("Nachname : " + std.getNachname());
        System.out.println("Geburtsjahr : " + std.getGeburtsjahr());
        System.out.println("Studenten Nummer : " + std.getStudentenNummer());

        // Gibt die Adresse des in die Methode gesendeten Studenten wieder
        System.out.println(std.getAdressen());

    }

}
