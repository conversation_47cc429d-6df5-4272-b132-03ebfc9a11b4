package ders07_concatenation_operators;

/**
 * Diese Klasse demonstriert die String-Konkatenation (Verkettung) in Java.
 *
 * Die String-Konkatenation ist eine Operation, die Strings miteinander verbindet.
 * In Java wird dafür der +-Operator verwendet, der je nach Kontext unterschiedlich interpretiert wird:
 * - Bei numerischen Werten: Addition
 * - Bei mindestens einem String: Konkatenation (Verkettung)
 *
 * Wichtig ist die Reihenfolge der Auswertung und die Verwendung von Klammern,
 * um die gewünschte Priorität der Operationen zu steuern.
 */
public class C01_Concatenation {
    /**
     * Die Hauptmethode demonstriert verschiedene Beispiele der String-Konkatenation.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Beispiel 1: Grundlegende Konkatenation mit Zahlen
        System.out.println("Beispiel 1: Grundlegende Konkatenation mit Zahlen");
        int a = 10;
        int b = 20;

        // Mit Klammern: Erst wird die Addition durchgeführt, dann die Konkatenation
        System.out.println("Gesamt Ergebnis der beiden Werte " + (a + b));  // Ausgabe: Gesamt Ergebnis der beiden Werte 30

        // Komplexere Verkettung mit mehreren Strings und Zahlen
        System.out.println("input " + a + " und " + b + " ergeben gesamt : " + (a + b));
        // Ausgabe: input 10 und 20 ergeben gesamt : 30

        // ACHTUNG: Ohne Klammern werden die Zahlen als Strings aneinandergehängt
        System.out.println("Ergebnis der zahlen : " + a + b); // Ausgabe: Ergebnis der zahlen : 1020
        // Hier wird a (10) als String an "Ergebnis der zahlen : " angehängt, dann b (20) als String
        System.out.println();

        // Beispiel 2: Komplexere Konkatenationsbeispiele
        System.out.println("Beispiel 2: Komplexere Konkatenationsbeispiele");
        int e = 3;
        int f = 4;

        String s1 = "Java";
        String s2 = " ";  // Ein Leerzeichen
        String s3 = "kolay";
        String s4 = "";   // Leerer String

        // Beispiel 2.1: Multiplikation vor Konkatenation
        System.out.println("e * f + s2 + s1 + s2 + s3: " + (e * f + s2 + s1 + s2 + s3));
        // Ausgabe: 12 Java kolay
        // Erst wird e * f berechnet (12), dann als String mit den anderen Strings verkettet

        // Beispiel 2.2: Addition vor Konkatenation
        System.out.println("e + f + s2 + s1 + s2 + s3: " + (e + f + s2 + s1 + s2 + s3));
        // Ausgabe: 7 Java kolay
        // Erst wird e + f berechnet (7), dann als String mit den anderen Strings verkettet

        // Beispiel 2.3: Konkatenation von Anfang an (mit leerem String am Anfang)
        System.out.println("s4 + e + f + s1 + s2 + s3: " + (s4 + e + f + s1 + s2 + s3));
        // Ausgabe: 34Java kolay
        // Da s4 ein String ist (wenn auch leer), werden e und f als Strings behandelt und aneinandergehängt

        // Beispiel 2.4: Ähnlich wie 2.3, aber mit e am Anfang
        System.out.println("e + s4 + f + s1 + s2 + s3: " + (e + s4 + f + s1 + s2 + s3));
        // Ausgabe: 34Java kolay
        // e wird mit s4 (leerem String) verkettet, wodurch e zu einem String wird, dann folgt die weitere Verkettung

        // Beispiel 2.5: Verwendung von Klammern für Multiplikation
        System.out.println("s1 + (e * f) + s3: " + (s1 + (e * f) + s3));
        // Ausgabe: Java12kolay
        // Erst wird e * f berechnet (12), dann mit den Strings verkettet

        // Beispiel 2.6: Ohne Klammern bei Zahlen
        System.out.println("s1 + e + f + s3: " + (s1 + e + f + s3));
        // Ausgabe: Java34kolay
        // Da s1 ein String ist, werden e und f als Strings behandelt und aneinandergehängt

        // Beispiel 2.7: Mit Klammern für Addition
        System.out.println("s1 + (e + f) + s3: " + (s1 + (e + f) + s3));
        // Ausgabe: Java7kolay
        // Erst wird e + f berechnet (7), dann mit den Strings verkettet

        /*
         * WICHTIGE REGELN FÜR DIE KONKATENATION:
         *
         * 1. Der +-Operator wird von links nach rechts ausgewertet.
         * 2. Wenn einer der Operanden ein String ist, wird der andere automatisch in einen String umgewandelt.
         * 3. Arithmetische Operationen in Klammern werden vor der Konkatenation ausgeführt.
         * 4. Ohne Klammern werden numerische Operanden als Strings behandelt, sobald ein String in der Kette erscheint.
         */
    }
}
