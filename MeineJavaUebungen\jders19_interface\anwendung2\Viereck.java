package jders19_interface.anwendung2;

public class Viereck implements Form {

    private double seite;

    public Viereck() {

    }

    public Viereck(double seite) {
        this.seite = seite;
    }

    public double getSeite() {
        return seite;
    }

    public void setSeite(double seite) {
        this.seite = seite;
    }

    @Override
    public double umfangBerechnen() {
        return Math.pow(seite, 2);
    }

    @Override
    public double inhaltBerchnen() {
        return seite * 4;
    }
}
