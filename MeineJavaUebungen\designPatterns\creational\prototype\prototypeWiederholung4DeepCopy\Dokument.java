package designPatterns.creational.prototype.prototypeWiederholung4DeepCopy;

public class Dokument implements Cloneable {

    private long id;
    private String name;
    private DokumentTyp dokumentTyp;
    private Kategorie kategorie;
    private String datei;

    public Dokument() {
    }

    public Dokument(long id, String name, DokumentTyp dokumentTyp, Kategorie kategorie, String datei) {
        this.id = id;
        this.name = name;
        this.dokumentTyp = dokumentTyp;
        this.kategorie = kategorie;
        this.datei = datei;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public DokumentTyp getDokumentTyp() {
        return dokumentTyp;
    }

    public void setDokumentTyp(DokumentTyp dokumentTyp) {
        this.dokumentTyp = dokumentTyp;
    }

    public Kategorie getKategorie() {
        return kategorie;
    }

    public void setKategorie(Kategorie kategorie) {
        this.kategorie = kategorie;
    }

    public String getDatei() {
        return datei;
    }

    public void setDatei(String datei) {
        this.datei = datei;
    }


//     /**
//      * Shallow Copy
//      *
//      * @return
//      * @throws CloneNotSupportedException
//      */
//
//
//     @Override
//     protected Dokument clone() throws CloneNotSupportedException {  // An der Stelle können wir unser Objekt weiter geben
//         // um anderseits nicht den Cast-Vorgang bearbeiten zu müssen, tun wir dies hier.
//         return (Dokument) super.clone();
//    }


    @Override
    public String toString() {
        return "Dokument{" +
                "\nid=" + id +
                "\n, name='" + name + '\'' +
                "\n, dokumentTyp=" + dokumentTyp +
                "\n, kategorie=" + kategorie +
                "\n, datei='" + datei + '\'' +
                '}';
    }


    /**
     * Deep-Copy
     *
     * @return
     * @throws CloneNotSupportedException
     */
    @Override
    protected Dokument clone() throws CloneNotSupportedException {

        // Um die unter Instanzen zu clonen, die nicht als selbe Referenz gehalten werden sollen, hier für müssen die Objekte
        // die Klasse Clonable implementieren. Für eine direkte Kopie gilt nur, wenn die Klasse keine anderen Objekte mit enthält
        Dokument dokument = (Dokument) super.clone();
        // Nun erstellen wir tiefe Kopien der enthaltenen Objekte: DokumentTyp und Kategorie.
        DokumentTyp dokumentTyp = dokument.getDokumentTyp().clone();
        Kategorie kategorie = dokument.getKategorie().clone();

        // Setzen Sie die geklonten DokumentTyp und Kategorie zurück in das geklonte Dokument.
        dokument.setDokumentTyp(dokumentTyp);
        dokument.setKategorie(kategorie);

        return dokument;
    }


}
