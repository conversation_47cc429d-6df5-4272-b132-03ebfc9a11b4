# Implementierungsanleitung: Java-Anwendung mit LLM-Integration und MCP
*Aktualisiert für Spring AI 1.0 GA und MCP SDK 0.9.0*

Diese Anleitung beschreibt die Einrichtung und Entwicklung einer Java-basierten Anwendung, die lokale LLMs (Large Language Models) nutzt und das Model-Context-Protokoll (MCP) integriert. Die Anleitung ist speziell für Computer mit begrenzter GPU-Leistung (16 GB RAM, 4 Kerne, 8 Threads) optimiert und verwendet die neuesten **produktionsreifen** Versionen.

## Inhaltsverzeichnis

1. [Überblick der Technologiekombination](#überblick-der-technologiekombination)
2. [Einrichtung der Entwicklungsumgebung](#einrichtung-der-entwicklungsumgebung)
3. [Installation und Konfiguration von Cline in VS Code](#installation-und-konfiguration-von-cline-in-vs-code)
4. [Einrichtung eines lokalen LLM](#einrichtung-eines-lokalen-llm)
5. [Integration des MCP in die Java-Anwendung](#integration-des-mcp-in-die-java-anwendung)
6. [Beispielimplementierung](#beispielimplementierung)
7. [Optimierung für Hardware mit begrenzter GPU-Leistung](#optimierung-für-hardware-mit-begrenzter-gpu-leistung)
8. [Fehlerbehebung und häufige Probleme](#fehlerbehebung-und-häufige-probleme)

## Überblick der Technologiekombination

Für eine Java-basierte Anwendung mit LLM-Integration und MCP auf einem Computer mit begrenzter GPU-Leistung empfehlen wir folgende **aktualisierte** Technologiekombination:

- **Entwicklungsumgebung**: VS Code mit Cline-Erweiterung
- **Backend-Framework**: Spring Boot mit Spring AI 1.0 GA (produktionsreif!)
- **Lokales LLM**: Ollama mit einem kompakten Modell (z.B. Llama 3 8B-Q4, Mistral 7B-Q4, oder phi4-mini-reasoning:3.8b)
- **Modell-Optimierung**: ONNX Runtime für CPU-Inferenz
- **MCP-Integration**: MCP Java SDK 0.9.0 mit neuen @Tool-Annotationen
- **Datenverarbeitung**: TinyML für leichtgewichtige ML-Operationen
- **Dokumentation**: Automatische Dokumentationsgenerierung mit Cline

Diese Kombination bietet eine gute Balance zwischen Leistung und Ressourcenverbrauch und ist speziell für Computer ohne leistungsstarke GPU optimiert. **Neu**: Vereinfachte Entwicklung durch @Tool-Annotationen und produktionsreife APIs.

## Einrichtung der Entwicklungsumgebung

### 1. Installation von VS Code

1. Besuchen Sie die [VS Code-Website](https://code.visualstudio.com/) und laden Sie die neueste Version herunter.
2. Führen Sie die Installationsdatei aus und folgen Sie den Anweisungen.

### 2. Installation von Java Development Kit (JDK)

1. Installieren Sie das OpenJDK 17 (LTS-Version):

   ```bash
   sudo apt update
   sudo apt install openjdk-17-jdk
   ```

2. Überprüfen Sie die Installation:

   ```bash
   java -version
   ```

### 3. Installation von Maven

1. Installieren Sie Apache Maven:

   ```bash
   sudo apt install maven
   ```

2. Überprüfen Sie die Installation:

   ```bash
   mvn -version
   ```

### 4. Installation von Git

1. Installieren Sie Git:

   ```bash
   sudo apt install git
   ```

2. Überprüfen Sie die Installation:

   ```bash
   git --version
   ```

## Installation und Konfiguration von Cline in VS Code

### 1. Installation der Cline-Erweiterung

1. Öffnen Sie VS Code.
2. Navigieren Sie zum Extensions-Marketplace (Tastenkombination: `Ctrl+Shift+X`).
3. Suchen Sie nach "Cline".
4. Klicken Sie auf "Install", um die Erweiterung zu installieren.

### 2. Konfiguration von Cline

1. Nach der Installation öffnen Sie die Einstellungen von VS Code (Tastenkombination: `Ctrl+,`).
2. Suchen Sie nach "Cline" in den Einstellungen.
3. Konfigurieren Sie die folgenden Einstellungen:
   - Aktivieren Sie "Cline: Enable Local LLM"
   - Setzen Sie "Cline: Local LLM URL" auf die URL Ihres lokalen LLM-Servers (z.B. `http://localhost:11434/v1` für Ollama)
   - Aktivieren Sie "Cline: MCP Integration"

### 3. Einrichtung der Cline-Berechtigungen

1. Öffnen Sie die Befehlspalette in VS Code (Tastenkombination: `Ctrl+Shift+P`).
2. Suchen Sie nach "Cline: Configure Permissions".
3. Aktivieren Sie die folgenden Berechtigungen:
   - Dateien lesen und schreiben
   - Terminal-Befehle ausführen
   - Browser-Zugriff

## Einrichtung eines lokalen LLM

Für Computer mit begrenzter GPU-Leistung empfehlen wir die Verwendung von Ollama mit einem kompakten Modell, das für CPU-Inferenz optimiert ist.

### 1. Installation von Ollama

1. Laden Sie Ollama herunter und installieren Sie es:

   ```bash
   curl -fsSL https://ollama.com/install.sh | sh
   ```

2. Starten Sie den Ollama-Dienst:

   ```bash
   ollama serve
   ```

### 2. Herunterladen eines kompakten Modells

1. Laden Sie ein kompaktes Modell herunter, das für CPU-Inferenz geeignet ist:

   ```bash
   # Für Llama 3 8B (optimiert für CPU)
   ollama pull llama3:8b-q4

   # ODER für Mistral 7B (optimiert für CPU)
   ollama pull mistral:7b-q4
   ```

   Die Quantisierung (q4) reduziert die Modellgröße und verbessert die Leistung auf CPUs.

### 3. Testen des Modells

1. Testen Sie das Modell mit einem einfachen Prompt:

   ```bash
   ollama run llama3:8b-q4 "Erkläre mir das Model Context Protocol in einem Satz."
   ```

### 4. Konfiguration für optimale CPU-Nutzung

1. Erstellen Sie eine benutzerdefinierte Modelldefinition für optimale CPU-Nutzung:

   ```bash
   cat > llama3-cpu.modelfile << EOF
   FROM llama3:8b-q4

   # Optimierung für CPU
   PARAMETER num_ctx 2048
   PARAMETER num_thread 8
   PARAMETER num_batch 512
   EOF
   ```

2. Erstellen Sie das optimierte Modell:

   ```bash
   ollama create llama3-cpu -f llama3-cpu.modelfile
   ```

## Integration des MCP in die Java-Anwendung

### 1. Erstellung eines neuen Spring Boot-Projekts

1. Erstellen Sie ein neues Spring Boot-Projekt mit Spring Initializr:

   ```bash
   mkdir mcp-java-app
   cd mcp-java-app
   ```

2. Erstellen Sie eine `pom.xml`-Datei mit den folgenden Abhängigkeiten:

   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
       <modelVersion>4.0.0</modelVersion>
       <parent>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-parent</artifactId>
           <version>3.2.0</version>
           <relativePath/>
       </parent>
       <groupId>com.example</groupId>
       <artifactId>mcp-java-app</artifactId>
       <version>0.0.1-SNAPSHOT</version>
       <name>mcp-java-app</name>
       <description>Java App with LLM and MCP integration</description>
       
       <properties>
           <java.version>17</java.version>
           <spring-ai.version>1.0.0</spring-ai.version> <!-- Updated to GA! -->
           <mcp.version>0.9.0</mcp.version> <!-- Updated from 0.8.0 -->
       </properties>
       
       <dependencies>
           <!-- Spring Boot -->
           <dependency>
               <groupId>org.springframework.boot</groupId>
               <artifactId>spring-boot-starter-web</artifactId>
           </dependency>
           
           <!-- Spring AI -->
           <dependency>
               <groupId>org.springframework.ai</groupId>
               <artifactId>spring-ai-ollama</artifactId>
               <version>${spring-ai.version}</version>
           </dependency>
           
           <!-- MCP Java SDK -->
           <dependency>
               <groupId>io.modelcontextprotocol.sdk</groupId>
               <artifactId>mcp</artifactId>
               <version>${mcp.version}</version>
           </dependency>
           
           <!-- MCP Spring Integration (optional) -->
           <dependency>
               <groupId>io.modelcontextprotocol.sdk</groupId>
               <artifactId>mcp-spring-webmvc</artifactId>
               <version>${mcp.version}</version>
           </dependency>
           
           <!-- ONNX Runtime -->
           <dependency>
               <groupId>com.microsoft.onnxruntime</groupId>
               <artifactId>onnxruntime</artifactId>
               <version>1.16.3</version>
           </dependency>
           
           <!-- Test -->
           <dependency>
               <groupId>org.springframework.boot</groupId>
               <artifactId>spring-boot-starter-test</artifactId>
               <scope>test</scope>
           </dependency>
       </dependencies>
       
       <build>
           <plugins>
               <plugin>
                   <groupId>org.springframework.boot</groupId>
                   <artifactId>spring-boot-maven-plugin</artifactId>
               </plugin>
           </plugins>
       </build>
   </project>
   ```

### 2. Aktualisierte MCP-Konfiguration mit SDK 0.9.0

1. Erstellen Sie eine **aktualisierte** Konfigurationsklasse für den MCP-Client:

   ```java
   package com.example.mcpjavaapp.config;

   import io.modelcontextprotocol.sdk.McpSession;
   import io.modelcontextprotocol.sdk.transport.StdioTransport;
   import org.springframework.context.annotation.Bean;
   import org.springframework.context.annotation.Configuration;

   @Configuration
   public class McpConfig {

       @Bean
       public McpSession mcpSession() {
           return McpSession.builder()
               .withTransport(StdioTransport.create())
               .withProtocolVersion("0.9.0")
               .withLogging(true) // Neues Logging-System
               .build();
       }
   }
   ```

### 3. Vereinfachte Tool-Erstellung mit @Tool-Annotationen (NEU!)

Mit MCP SDK 0.9.0 können Sie jetzt Tools viel einfacher erstellen:

   ```java
   package com.example.mcpjavaapp.tools;

   import org.springframework.ai.mcp.Tool;
   import org.springframework.ai.mcp.Parameter;
   import org.springframework.stereotype.Component;

   @Component
   public class DocumentationTools {

       @Tool("documentation_generator")
       public String generateDocumentation(
           @Parameter("code") String code,
           @Parameter("format") String format) {

           // Ihre Implementierung hier
           return "Generated documentation for: " + code;
       }

       @Tool("code_analyzer")
       public String analyzeCode(@Parameter("code") String code) {
           // Code-Analyse Implementierung
           return "Analysis results for: " + code;
       }
   }
   ```

### 3. Konfiguration des Ollama-Clients mit Spring AI

1. Erstellen Sie eine Konfigurationsklasse für den Ollama-Client:

   ```java
   package com.example.mcpjavaapp.config;

   import org.springframework.ai.ollama.OllamaClient;
   import org.springframework.ai.ollama.OllamaProperties;
   import org.springframework.context.annotation.Bean;
   import org.springframework.context.annotation.Configuration;

   @Configuration
   public class OllamaConfig {

       @Bean
       public OllamaClient ollamaClient() {
           OllamaProperties properties = new OllamaProperties();
           properties.setBaseUrl("http://localhost:11434");
           properties.setModel("llama3-cpu"); // Unser optimiertes Modell
           
           return new OllamaClient(properties);
       }
   }
   ```

### 4. Erstellung eines einfachen MCP-Servers

1. Erstellen Sie eine Klasse für einen einfachen MCP-Server:

   ```java
   package com.example.mcpjavaapp.mcp;

   import io.modelcontextprotocol.sdk.McpServer;
   import io.modelcontextprotocol.sdk.McpSession;
   import io.modelcontextprotocol.sdk.Tool;
   import io.modelcontextprotocol.sdk.ToolExecutionRequest;
   import io.modelcontextprotocol.sdk.ToolExecutionResponse;
   import io.modelcontextprotocol.sdk.transport.StdioTransport;
   import org.springframework.stereotype.Component;

   import java.util.List;
   import java.util.Map;
   import java.util.concurrent.CompletableFuture;

   @Component
   public class SimpleMcpServer {

       private final McpServer mcpServer;

       public SimpleMcpServer() {
           // Definieren eines einfachen Tools
           Tool documentationTool = new Tool.Builder()
               .withName("documentation_generator")
               .withDescription("Generiert Dokumentation für Java-Code")
               .withParameters(Map.of(
                   "code", Map.of("type", "string", "description", "Der Java-Code, für den Dokumentation generiert werden soll"),
                   "format", Map.of("type", "string", "description", "Das Format der Dokumentation (html, markdown, text)")
               ))
               .withExecutor(this::executeDocumentationTool)
               .build();

           // Konfiguration des MCP-Servers
           StdioTransport transport = new StdioTransport();
           McpSession session = new McpSession.Builder()
               .withTransport(transport)
               .build();
           
           mcpServer = new McpServer.Builder()
               .withSession(session)
               .withTools(List.of(documentationTool))
               .build();
       }

       private CompletableFuture<ToolExecutionResponse> executeDocumentationTool(ToolExecutionRequest request) {
           String code = (String) request.getParameters().get("code");
           String format = (String) request.getParameters().get("format");
           
           // Hier würde die eigentliche Dokumentationsgenerierung stattfinden
           String documentation = "Dokumentation für:\n\n" + code;
           
           return CompletableFuture.completedFuture(
               new ToolExecutionResponse.Builder()
                   .withContent(documentation)
                   .build()
           );
       }

       public void start() {
           mcpServer.start();
       }

       public void stop() {
           mcpServer.stop();
       }
   }
   ```

### 5. Erstellung eines Controllers für die LLM-Integration

1. Erstellen Sie einen Controller für die LLM-Integration:

   ```java
   package com.example.mcpjavaapp.controller;

   import org.springframework.ai.chat.ChatClient;
   import org.springframework.ai.chat.ChatResponse;
   import org.springframework.ai.chat.prompt.Prompt;
   import org.springframework.ai.chat.prompt.SystemPromptTemplate;
   import org.springframework.ai.chat.prompt.UserPrompt;
   import org.springframework.beans.factory.annotation.Autowired;
   import org.springframework.web.bind.annotation.PostMapping;
   import org.springframework.web.bind.annotation.RequestBody;
   import org.springframework.web.bind.annotation.RestController;

   import java.util.Map;

   @RestController
   public class LlmController {

       private final ChatClient chatClient;

       @Autowired
       public LlmController(ChatClient chatClient) {
           this.chatClient = chatClient;
       }

       @PostMapping("/generate")
       public String generateResponse(@RequestBody Map<String, String> request) {
           String userPrompt = request.get("prompt");
           
           // Systemanweisung für die Codegenerierung
           String systemPromptText = "Du bist ein hilfreicher Assistent, der Java-Code generiert. " +
                                    "Generiere sauberen, gut dokumentierten Code.";
           
           SystemPromptTemplate systemPrompt = new SystemPromptTemplate(systemPromptText);
           UserPrompt userPromptObj = new UserPrompt(userPrompt);
           
           Prompt prompt = new Prompt(systemPrompt, userPromptObj);
           ChatResponse response = chatClient.call(prompt);
           
           return response.getResult().getOutput().getContent();
       }
   }
   ```

### 6. Hauptanwendungsklasse

1. Erstellen Sie die Hauptanwendungsklasse:

   ```java
   package com.example.mcpjavaapp;

   import com.example.mcpjavaapp.mcp.SimpleMcpServer;
   import org.springframework.boot.SpringApplication;
   import org.springframework.boot.autoconfigure.SpringBootApplication;
   import org.springframework.context.ConfigurableApplicationContext;

   @SpringBootApplication
   public class McpJavaAppApplication {

       public static void main(String[] args) {
           ConfigurableApplicationContext context = SpringApplication.run(McpJavaAppApplication.class, args);
           
           // Starten des MCP-Servers
           SimpleMcpServer mcpServer = context.getBean(SimpleMcpServer.class);
           mcpServer.start();
           
           // Shutdown-Hook zum Stoppen des MCP-Servers
           Runtime.getRuntime().addShutdownHook(new Thread(() -> {
               mcpServer.stop();
           }));
       }
   }
   ```

### 7. Anwendungskonfiguration

1. Erstellen Sie eine `application.properties`-Datei:

   ```properties
   # Server-Konfiguration
   server.port=8080

   # Ollama-Konfiguration
   spring.ai.ollama.base-url=http://localhost:11434
   spring.ai.ollama.model=llama3-cpu

   # Logging-Konfiguration
   logging.level.root=INFO
   logging.level.com.example.mcpjavaapp=DEBUG
   ```

## Beispielimplementierung

Hier ist ein vollständiges Beispiel für eine einfache Java-Anwendung, die lokale LLMs und MCP integriert:

### 1. Dokumentationsgenerator-Tool

```java
package com.example.mcpjavaapp.tools;

import io.modelcontextprotocol.sdk.Tool;
import io.modelcontextprotocol.sdk.ToolExecutionRequest;
import io.modelcontextprotocol.sdk.ToolExecutionResponse;
import org.springframework.ai.ollama.OllamaClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.chat.prompt.UserPrompt;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
public class DocumentationGeneratorTool {

    private final OllamaClient ollamaClient;

    public DocumentationGeneratorTool(OllamaClient ollamaClient) {
        this.ollamaClient = ollamaClient;
    }

    public Tool createTool() {
        return new Tool.Builder()
            .withName("documentation_generator")
            .withDescription("Generiert Dokumentation für Java-Code")
            .withParameters(Map.of(
                "code", Map.of("type", "string", "description", "Der Java-Code, für den Dokumentation generiert werden soll"),
                "format", Map.of("type", "string", "description", "Das Format der Dokumentation (html, markdown, text)")
            ))
            .withExecutor(this::executeDocumentationTool)
            .build();
    }

    private CompletableFuture<ToolExecutionResponse> executeDocumentationTool(ToolExecutionRequest request) {
        String code = (String) request.getParameters().get("code");
        String format = (String) request.getParameters().getOrDefault("format", "markdown");
        
        // Prompt für das LLM erstellen
        String systemPromptText = "Du bist ein erfahrener Java-Entwickler, der ausführliche Dokumentation erstellt. " +
                                 "Erstelle eine detaillierte Dokumentation für den gegebenen Java-Code im " + format + "-Format.";
        
        SystemPromptTemplate systemPrompt = new SystemPromptTemplate(systemPromptText);
        UserPrompt userPrompt = new UserPrompt("Hier ist der Java-Code, für den Dokumentation erstellt werden soll:\n\n```java\n" + code + "\n```");
        
        Prompt prompt = new Prompt(systemPrompt, userPrompt);
        
        // LLM-Anfrage senden
        String documentation = ollamaClient.call(prompt).getResult().getOutput().getContent();
        
        // Antwort zurückgeben
        return CompletableFuture.completedFuture(
            new ToolExecutionResponse.Builder()
                .withContent(documentation)
                .build()
        );
    }
}
```

### 2. Code-Analyse-Tool

```java
package com.example.mcpjavaapp.tools;

import io.modelcontextprotocol.sdk.Tool;
import io.modelcontextprotocol.sdk.ToolExecutionRequest;
import io.modelcontextprotocol.sdk.ToolExecutionResponse;
import org.springframework.ai.ollama.OllamaClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.chat.prompt.UserPrompt;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
public class CodeAnalysisTool {

    private final OllamaClient ollamaClient;

    public CodeAnalysisTool(OllamaClient ollamaClient) {
        this.ollamaClient = ollamaClient;
    }

    public Tool createTool() {
        return new Tool.Builder()
            .withName("code_analyzer")
            .withDescription("Analysiert Java-Code auf Bugs, Performance-Probleme und Verbesserungsmöglichkeiten")
            .withParameters(Map.of(
                "code", Map.of("type", "string", "description", "Der Java-Code, der analysiert werden soll")
            ))
            .withExecutor(this::executeCodeAnalysis)
            .build();
    }

    private CompletableFuture<ToolExecutionResponse> executeCodeAnalysis(ToolExecutionRequest request) {
        String code = (String) request.getParameters().get("code");
        
        // Prompt für das LLM erstellen
        String systemPromptText = "Du bist ein erfahrener Java-Entwickler und Code-Reviewer. " +
                                 "Analysiere den gegebenen Java-Code auf Bugs, Performance-Probleme und Verbesserungsmöglichkeiten. " +
                                 "Gib konkrete Verbesserungsvorschläge.";
        
        SystemPromptTemplate systemPrompt = new SystemPromptTemplate(systemPromptText);
        UserPrompt userPrompt = new UserPrompt("Hier ist der Java-Code, der analysiert werden soll:\n\n```java\n" + code + "\n```");
        
        Prompt prompt = new Prompt(systemPrompt, userPrompt);
        
        // LLM-Anfrage senden
        String analysis = ollamaClient.call(prompt).getResult().getOutput().getContent();
        
        // Antwort zurückgeben
        return CompletableFuture.completedFuture(
            new ToolExecutionResponse.Builder()
                .withContent(analysis)
                .build()
        );
    }
}
```

### 3. Verbesserte MCP-Server-Implementierung

```java
package com.example.mcpjavaapp.mcp;

import com.example.mcpjavaapp.tools.CodeAnalysisTool;
import com.example.mcpjavaapp.tools.DocumentationGeneratorTool;
import io.modelcontextprotocol.sdk.McpServer;
import io.modelcontextprotocol.sdk.McpSession;
import io.modelcontextprotocol.sdk.transport.StdioTransport;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
public class EnhancedMcpServer {

    private final McpServer mcpServer;

    public EnhancedMcpServer(DocumentationGeneratorTool documentationGeneratorTool,
                            CodeAnalysisTool codeAnalysisTool) {
        // Konfiguration des MCP-Servers mit mehreren Tools
        StdioTransport transport = new StdioTransport();
        McpSession session = new McpSession.Builder()
            .withTransport(transport)
            .build();
        
        mcpServer = new McpServer.Builder()
            .withSession(session)
            .withTools(Arrays.asList(
                documentationGeneratorTool.createTool(),
                codeAnalysisTool.createTool()
            ))
            .build();
    }

    public void start() {
        mcpServer.start();
    }

    public void stop() {
        mcpServer.stop();
    }
}
```

## Optimierung für Hardware mit begrenzter GPU-Leistung

### 1. Modellquantisierung mit ONNX Runtime

Für eine optimale Leistung auf CPUs können Sie Ihr LLM mit ONNX Runtime quantisieren:

```java
package com.example.mcpjavaapp.optimization;

import ai.onnxruntime.*;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Component
public class OnnxModelOptimizer {

    private OrtEnvironment env;
    private OrtSession session;

    public void initializeModel(String modelPath) {
        try {
            // ONNX Runtime-Umgebung erstellen
            env = OrtEnvironment.getEnvironment();
            
            // Optimierungsoptionen für CPU
            SessionOptions sessionOptions = new SessionOptions();
            sessionOptions.setOptimizationLevel(OptLevel.ALL_OPT);
            sessionOptions.setExecutionMode(ExecutionMode.SEQUENTIAL);
            
            // Anzahl der zu verwendenden Threads festlegen (an Ihre CPU anpassen)
            sessionOptions.setInterOpNumThreads(4);
            sessionOptions.setIntraOpNumThreads(8);
            
            // Modell laden
            Path path = Paths.get(modelPath);
            session = env.createSession(path.toString(), sessionOptions);
            
            System.out.println("ONNX-Modell erfolgreich geladen und optimiert für CPU-Inferenz");
        } catch (OrtException e) {
            System.err.println("Fehler beim Laden des ONNX-Modells: " + e.getMessage());
        }
    }

    public Map<String, OnnxTensor> runInference(Map<String, OnnxTensor> inputs) {
        try {
            // Inferenz durchführen
            OrtSession.Result result = session.run(inputs);
            
            // Ergebnisse in eine Map umwandeln
            Map<String, OnnxTensor> outputs = new HashMap<>();
            result.forEach(output -> {
                try {
                    outputs.put(output.getName(), (OnnxTensor) output.getValue());
                } catch (OrtException e) {
                    System.err.println("Fehler beim Verarbeiten der Ausgabe: " + e.getMessage());
                }
            });
            
            return outputs;
        } catch (OrtException e) {
            System.err.println("Fehler bei der Inferenz: " + e.getMessage());
            return new HashMap<>();
        }
    }

    public void close() {
        try {
            if (session != null) {
                session.close();
            }
            if (env != null) {
                env.close();
            }
        } catch (OrtException e) {
            System.err.println("Fehler beim Schließen der ONNX-Ressourcen: " + e.getMessage());
        }
    }
}
```

### 2. Speichermanagement

Für Computer mit begrenzter RAM-Kapazität ist ein effizientes Speichermanagement wichtig:

```java
package com.example.mcpjavaapp.optimization;

import org.springframework.stereotype.Component;

@Component
public class MemoryManager {

    private final Runtime runtime = Runtime.getRuntime();
    private final long maxMemory = runtime.maxMemory();
    private final double memoryThreshold = 0.8; // 80% Speicherauslastung als Schwellenwert

    public boolean isMemoryAvailable() {
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        double memoryUsage = (double) usedMemory / maxMemory;
        
        return memoryUsage < memoryThreshold;
    }

    public void runGarbageCollection() {
        System.gc();
    }

    public String getMemoryStatus() {
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        double memoryUsage = (double) usedMemory / maxMemory * 100;
        
        return String.format("Speichernutzung: %.2f%% (%.2f MB / %.2f MB)",
                memoryUsage,
                usedMemory / (1024.0 * 1024.0),
                maxMemory / (1024.0 * 1024.0));
    }
}
```

### 3. Batch-Verarbeitung für große Anfragen

Für große Anfragen ist eine Batch-Verarbeitung sinnvoll:

```java
package com.example.mcpjavaapp.optimization;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;

@Component
public class BatchProcessor<T, R> {

    private final ExecutorService executorService;
    private final int batchSize;

    public BatchProcessor(int threadCount, int batchSize) {
        this.executorService = Executors.newFixedThreadPool(threadCount);
        this.batchSize = batchSize;
    }

    public List<R> processBatch(List<T> items, Function<T, R> processor) {
        List<R> results = new ArrayList<>(items.size());
        
        for (int i = 0; i < items.size(); i += batchSize) {
            int end = Math.min(i + batchSize, items.size());
            List<T> batch = items.subList(i, end);
            
            List<CompletableFuture<R>> futures = batch.stream()
                .map(item -> CompletableFuture.supplyAsync(() -> processor.apply(item), executorService))
                .toList();
            
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            futures.forEach(future -> results.add(future.join()));
        }
        
        return results;
    }

    public void shutdown() {
        executorService.shutdown();
    }
}
```

## Fehlerbehebung und häufige Probleme

### 1. Ollama-Verbindungsprobleme

Wenn Sie Probleme mit der Verbindung zu Ollama haben:

1. Stellen Sie sicher, dass der Ollama-Dienst läuft:
   ```bash
   ps aux | grep ollama
   ```

2. Überprüfen Sie, ob Ollama auf dem richtigen Port lauscht:
   ```bash
   netstat -tuln | grep 11434
   ```

3. Testen Sie die Verbindung manuell:
   ```bash
   curl -X POST http://localhost:11434/api/generate -d '{"model": "llama3-cpu", "prompt": "Hello"}'
   ```

### 2. Speicherprobleme

Wenn Ihre Anwendung zu viel Speicher verbraucht:

1. Reduzieren Sie die Kontextgröße des Modells:
   ```bash
   cat > llama3-small.modelfile << EOF
   FROM llama3:8b-q4

   # Kleinere Kontextgröße für weniger Speicherverbrauch
   PARAMETER num_ctx 1024
   PARAMETER num_thread 4
   PARAMETER num_batch 256
   EOF

   ollama create llama3-small -f llama3-small.modelfile
   ```

2. Verwenden Sie ein noch kleineres Modell:
   ```bash
   ollama pull tinyllama:1.1b-q4
   ```

3. Erhöhen Sie die JVM-Heap-Größe in Ihrer `application.properties`:
   ```properties
   # JVM-Optionen
   spring.jvm.options=-Xmx4g -XX:+UseG1GC -XX:+UseStringDeduplication
   ```

### 3. MCP-Integrationsprobleme

Wenn Sie Probleme mit der MCP-Integration haben:

1. Überprüfen Sie die Protokollversion:
   ```java
   // Überprüfen Sie die unterstützte Protokollversion
   McpSession session = new McpSession.Builder()
       .withTransport(transport)
       .withProtocolVersion("0.8.0") // Explizite Angabe der Version
       .build();
   ```

2. Aktivieren Sie das Debug-Logging für MCP:
   ```properties
   # In application.properties
   logging.level.io.modelcontextprotocol=DEBUG
   ```

3. Implementieren Sie einen einfachen MCP-Client für Tests:
   ```java
   McpClient testClient = new McpClient.Builder()
       .withSession(new McpSession.Builder()
           .withTransport(new StdioTransport())
           .build())
       .build();
   
   testClient.start();
   
   // Testen Sie die Verbindung
   boolean connected = testClient.isConnected();
   System.out.println("MCP-Client verbunden: " + connected);
   ```

---

Diese Anleitung bietet einen umfassenden Überblick über die Einrichtung und Entwicklung einer Java-basierten Anwendung mit LLM-Integration und MCP auf Hardware mit begrenzter GPU-Leistung. Durch die Verwendung der empfohlenen Technologien und Optimierungen können Sie eine leistungsfähige KI-Anwendung auch auf Computern ohne dedizierte GPU betreiben.
