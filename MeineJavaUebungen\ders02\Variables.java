package ders02;

/**
 * Diese Klasse demonstriert die Grundlagen von Variablen in Java:
 * - Deklaration einer Variablen
 * - <PERSON><PERSON><PERSON>sun<PERSON> von Werten
 * - Änderung von Variablenwerten
 * - Verwendung von Variablen in Berechnungen
 *
 * Hinweis: Nach Java-Konventionen sollten Klassennamen mit einem Großbuchstaben beginnen.
 * Der korrekte Name wäre "Variables" statt "variables".
 */
public class Variables {
    /**
     * Die Hauptmethode demonstriert die Verwendung von Variablen.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {

        // 1. Deklaration einer Variablen vom Typ int (Ganzzahl)
        int zahl;       // Deklaration erfolgt nur einmal

        // 2. Erste Wertzuweisung (Initialisierung)
        zahl = 15;      // Wertzuweisung kann beliebig oft erfolgen
        System.out.println("Wert nach erster Zuweisung: " + zahl);   // Ausgabe: 15

        // 3. Änderung des Variablenwerts durch eine neue Zuweisung
        zahl = 20;
        System.out.println("Wert nach zweiter Zuweisung: " + zahl);   // Ausgabe: 20

        // 4. Verwendung der Variablen in einer Berechnung
        zahl = zahl + 10;  // Der aktuelle Wert (20) wird um 10 erhöht
        // Die Berechnung läuft wie folgt ab:
        // 1. Der aktuelle Wert von zahl (20) wird gelesen
        // 2. 10 wird hinzuaddiert, ergibt 30
        // 3. Das Ergebnis (30) wird wieder in zahl gespeichert
        System.out.println("Wert nach Addition: " + zahl);   // Ausgabe: 30
    }
}
