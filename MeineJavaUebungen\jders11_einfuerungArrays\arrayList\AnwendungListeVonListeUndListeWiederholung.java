package jders11_einfuerungArrays.arrayList;

import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

public class AnwendungListeVonListeUndListeWiederholung {
    public static void main(String[] args) {
        /* Der vorliegende Code demonstriert die Verwendung der ArrayList-Klasse und verschiedene Operationen,
         * die auf einer ArrayList durchgeführt werden können. Hier ist eine Erklärung der einzelnen Schritte:
         *  1. Es wird ein Scanner-Objekt erstellt, um die Eingabe des Benutzers zu lesen.
         *  2. Es wird eine ArrayList namens "namensListe" erstellt und einige Namen hinzugefügt.
         *  3. Mit der Methode add() wird ein weiterer Name ("ali") zur Liste hinzugefügt und
         *     der Zustand des Hinzufügens in der Variablen addZustand gespeichert.
         *  4. Der Benutzer wird aufgefordert, einen Namen einzugeben, der in der Liste gesucht werden soll.
         *  5. Die Methode contains() wird verwendet, um zu überprüfen, ob der eingegebene Name in der Liste vorhanden ist.
         *     Der Zustand wird in der Variablen Zustand gespeichert.
         *  6. Wenn der Name vorhanden ist, wird der Index des Namens in der Liste mit indexOf() abgerufen und ausgegeben.
         *     Andernfalls wird die Meldung "Gesuchter Name nicht vorhanden!" ausgegeben.
         *  7. Mit den Methoden remove() wird das erste Element der Liste und das Element "diego" entfernt.
         *  8. Eine foreach-Schleife wird verwendet, um alle verbleibenden Namen in der Liste auszugeben.
         *  9. Mit der Methode subList() wird eine neue Liste neueListe erstellt, die nur die
         *     ersten beiden Elemente der ursprünglichen Liste enthält.
         * 10. Eine weitere foreach-Schleife wird verwendet, um alle Namen in neueListe auszugeben.
         * 11. Mit der Methode indexOf() wird erneut der Index des eingegebenen Namens in der Liste gesucht und ausgegeben.
         *     Wenn der Name nicht gefunden wird, wird -1 zurückgegeben.
         * 12. Mit dem ternären Operator wird eine alternative Möglichkeit gezeigt, den Index des
         *     Namens auszugeben oder eine Meldung auszugeben, wenn der Name nicht vorhanden ist.
         * Der Code demonstriert grundlegende Operationen wie Hinzufügen, Entfernen, Überprüfen der Existenz
         * von Elementen und Abrufen des Indexes eines Elements in einer ArrayList.*/

        Scanner sc = new Scanner(System.in);

        String name;

        // Erstellen einer ArrayList für die Namen
        ArrayList<String> namensListe = new ArrayList<>(); // Erstellt eine neue ArrayList namensListe

        namensListe.add("pele"); // Fügt "pele" zur Liste hinzu
        namensListe.add("messi"); // Fügt "messi" zur Liste hinzu
        namensListe.add("tito"); // Fügt "tito" zur Liste hinzu
        namensListe.add("diego"); // Fügt "diego" zur Liste hinzu
        namensListe.add("silva"); // Fügt "silva" zur Liste hinzu

        // Hinzufügen eines weiteren Namens zur Liste und Speichern des Zustands
        boolean addZustand = namensListe.add("ali"); // Fügt "ali" zur Liste hinzu und speichert den Rückgabewert in addZustand

        System.out.print("Gesuchter name in der Liste : ");
        name = sc.next(); // Liest den eingegebenen Namen vom Benutzer ein

        // Überprüfung, ob der Name in der Liste vorhanden ist
        boolean zustand = namensListe.contains(name); // Überprüft, ob der eingegebene Name in der Liste enthalten ist und speichert den Zustand in zustand

        if (zustand) {
            // Wenn der Name vorhanden ist, wird der Index des Namens in der Liste ausgegeben
            System.out.println("Index ihrer suche lautet: " + namensListe.indexOf(name));
        } else {
            // Andernfalls wird eine Meldung ausgegeben, dass der gesuchte Name nicht vorhanden ist
            System.out.println("Gesuchte name nicht vorhanden!");
        }

        namensListe.remove(0); // Entfernt das Element an Index 0 aus der Liste

        namensListe.remove("diego"); // Entfernt das Element "diego" aus der Liste

        // Iteration über alle Elemente der Liste und Ausgabe jedes Elements
        for (String ganzeListe : namensListe) {
            System.out.println(ganzeListe);
        }

        System.out.println();

        List<String> neueListe = (List<String>) namensListe.subList(0, 2); // Erstellt eine neue Liste neueListe, die die ersten beiden Elemente der ursprünglichen Liste enthält

        // Iteration über alle Elemente in neueListe und Ausgabe jedes Elements
        for (String string : neueListe) {
            /* Wenn man den Inhalt der neueListe ausgibt, anstatt den splitListe zu verwenden.
             * Durch die Verwendung von splitListe in der System.out.println()-Anweisung wird
             * jeder einzelne Name aus der neueListe separat ausgegeben.
             * Die eckigen Klammern "[messi, tito]" zeigen an, dass es sich um eine Liste handelt.
             * In Java wird die toString()-Methode aufgerufen, um den Inhalt einer Liste auszugeben.
             * Standardmäßig wird der Inhalt der Liste von eckigen Klammern umgeben und die Elemente durch Kommas getrennt.
             * Das ist der Grund, warum [messi, tito] in eckigen Klammern ausgegeben wird.*/
            System.out.println(string);
        }

        // Bei Eingabe eines nicht existierenden Namens in der Liste wird -1 zurückgegeben
        System.out.println("Index ihrer suche lautet nun  am ende jetzt : " + namensListe.indexOf(name));
        System.out.println();
        // Ausgabe des Indexes des Namens oder einer Meldung, dass der Name nicht vorhanden ist, mithilfe des ternären Operators
        System.out.println(namensListe.indexOf(name) == -1 ? "Ausgabe durch ternary Operator: Gesuchte name nicht vorhanden!" : "Ausgabe durch ternary Operator: Index ihrer suche lautet nun  am ende jetzt : " + namensListe.indexOf(name));

        System.out.println();
        System.out.println(neueListe);
    }
}