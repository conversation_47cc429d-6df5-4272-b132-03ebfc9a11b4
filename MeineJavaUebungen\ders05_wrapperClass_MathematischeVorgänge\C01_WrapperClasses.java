package ders05_wrapperClass_MathematischeVorgänge;

/**
 * Diese Klasse demonstriert die Verwendung von Wrapper-Klassen in Java.
 *
 * Wrapper-Klassen sind Objekt-Äquivalente zu den primitiven Datentypen und bieten:
 * - Methoden zur Manipulation und Konvertierung von Werten
 * - Konstanten wie MIN_VALUE und MAX_VALUE
 * - Möglichkeiten zur Konvertierung zwischen Strings und primitiven Typen
 *
 * Die wichtigsten Wrapper-Klassen sind:
 * - Integer (für int)
 * - Double (für double)
 * - Character (für char)
 * - Boolean (für boolean)
 * - Byte (für byte)
 * - Short (für short)
 * - Long (für long)
 * - Float (für float)
 */
public class C01_WrapperClasses {
    /**
     * Die Hauptmethode demonstriert verschiedene Aspekte der Wrapper-Klassen.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {

        // 1. Vergleich: String-Klasse hat Methoden, primitive Datentypen nicht
        System.out.println("1. Vergleich: String-Klasse vs. primitive Datentypen");
        String str = "Java";
        System.out.println("Original-String: " + str);
        System.out.println("Mit toUpperCase(): " + str.toUpperCase()); // JAVA
        System.out.println();

        // 2. Primitive Datentypen vs. Wrapper-Klassen
        System.out.println("2. Primitive Datentypen vs. Wrapper-Klassen");

        // Primitiver Datentyp (int)
        int zahlP = 20;
        System.out.println("Primitiver int-Wert: " + zahlP);
        // Primitive Datentypen besitzen keine Methoden, sie enthalten nur einen Wert

        // Entsprechende Wrapper-Klasse (Integer)
        Integer zahlW = 30;
        System.out.println("Integer-Wrapper-Wert: " + zahlW);

        // Wrapper-Klassen bieten nützliche Konstanten
        System.out.println("Integer.MAX_VALUE: " + Integer.MAX_VALUE); // 2147483647
        System.out.println("Integer.MIN_VALUE: " + Integer.MIN_VALUE); // -2147483648
        System.out.println();

        // 3. Beispiel mit Character
        System.out.println("3. Beispiel mit Character");

        // Primitiver Datentyp (char)
        char charaktP = 'a';
        System.out.println("Primitiver char-Wert: " + charaktP);

        // Entsprechende Wrapper-Klasse (Character)
        Character charaktW = 'b';
        System.out.println("Character-Wrapper-Wert: " + charaktW);

        // Character-Klasse bietet nützliche Methoden zur Zeichenanalyse
        System.out.println("Character.isDigit('" + charaktP + "'): " + Character.isDigit(charaktP)); // false
        System.out.println("Character.isLowerCase('" + charaktP + "'): " + Character.isLowerCase(charaktP)); // true
        System.out.println("Character.isDigit('" + charaktW + "'): " + Character.isDigit(charaktW)); // false
        System.out.println("Character.isLowerCase('" + charaktW + "'): " + Character.isLowerCase(charaktW)); // true
        System.out.println();

        // 4. Konvertierung zwischen String und primitiven Typen
        System.out.println("4. Konvertierung zwischen String und primitiven Typen");
        String str2 = "1234";
        String str3 = "1000";

        // Bei String-Konkatenation werden die Strings einfach aneinandergehängt
        System.out.println("str2 + str3 als String-Konkatenation: " + str2 + str3); // 12341000

        // Mit valueOf() können wir Strings in numerische Werte umwandeln
        System.out.println("Integer.valueOf(str2) + Integer.valueOf(str3): " +
                          (Integer.valueOf(str2) + Integer.valueOf(str3))); // 2234
        System.out.println();

        // 5. Autoboxing und Unboxing
        System.out.println("5. Autoboxing und Unboxing");

        // Autoboxing: Automatische Umwandlung von primitiven Typen zu Wrapper-Objekten
        Integer autoboxedInt = 42;  // int wird automatisch zu Integer
        System.out.println("Autoboxing (int -> Integer): " + autoboxedInt);

        // Unboxing: Automatische Umwandlung von Wrapper-Objekten zu primitiven Typen
        int unboxedInt = autoboxedInt;  // Integer wird automatisch zu int
        System.out.println("Unboxing (Integer -> int): " + unboxedInt);

        /*
         * WICHTIG: Autoboxing und Unboxing
         * - Wrapper-Klassen und primitive Datentypen können mit einem Wert einander zugewiesen werden
         * - Die Umwandlung erfolgt automatisch in beide Richtungen (Autoboxing und Unboxing)
         * - Dies erleichtert die Arbeit mit Sammlungen (Collections), die nur Objekte speichern können
         */
    }
}
