package jders02_primitiveDatentypen;

public class BooleanLogischeOperation {
    public static void main(String[] args) {
        /*      DEFAULT VALUES of Primitive Data Types
                Data Type	            Default Value (for fields)
                byte	                0
                short	                0
                int	                    0
                long	                0L
                float	                0.0f
                double	                0.0d
                char	                '\u0000'
                String (or any object)  null
                boolean	                false

        Boolean kann einen Wert von "true" oder "false" darstellen.
        Wird verwendet für:
        - Bedingungen in if-Anweisungen
        - Logische Operationen (UND, ODER, NICHT)
        - Steuerung von Programmabläufen

        Wichtige logische Operationen:
        && (logisches UND) - true nur wenn beide true
        || (logisches ODER) - true wenn mind. einer true
        ! (logisches NICHT) - invertiert den Wert
         */

        // Direkte Initialisierung
        boolean bZustand1 = true;
        boolean bZustand2 = false;

        // Grundausgabe
        System.out.println("b Zustand 1 : " + bZustand1);  // true
        System.out.println("b Zustand 2 : " + bZustand2);  // false

        // Logische Operationen demonstrieren
        System.out.println("UND-Operation: " + (bZustand1 && bZustand2));  // false
        System.out.println("ODER-Operation: " + (bZustand1 || bZustand2)); // true
        System.out.println("NICHT-Operation: " + !bZustand1);              // false

        // Praktisches Beispiel mit if
        if(bZustand1) {
            System.out.println("Zustand1 ist wahr - dieser Block wird ausgeführt");
        }
    }
}
