// Gemergter Code aus dem Verzeichnis: /home/<USER>/JDBC-Tutorial/09-com.jdbc.projekt/src/main/java/com/projekt/repository

// package repository;

// --- BEGIN DES GEMERGTEN CODES AUS DEM VERZEICHNIS ---

// UserRepository nutzen wir, um einen User bei der Anmeldung in unserer Datenkank zu speichern.

public interface UserRepository {
	
	// Um die Werte des angemeldeten Users erneut ihm return zu können.
	User saveUser(User user);
	
	// Wenn unser User neues Produkt hinzufügt. User Seitig! denn das Produkt muss Gespeichert sein.
	boolean saveUserProduduct(int userId, int productId);
	
	// User Returnd seine geänderten daten Aktualisiert zurück.
	User updateUser(User user);
	
	boolean removeUser(int id);
	
	// Einzelnen User finden
	User findUserById(int id);
	
	// Einzelnen user mit Produkten finden
	User findUserProductsById(int id);
	
	// Die Liste Aller User Daten von Unserem Admin-Panel erreichen.
	List<User> findUsers();

}


public interface CategoryRepository {

	// In Zukuft könnten auch Neue Kategorien im admin-panel ersteltwerden mit saveCategory
	Category findCategoryById(int id);
	
	List<Category> findCategories();
}


public interface BrandRepository {

	// Wir werden die gespeicherten Daten dem User Return was er gespeichert hat.
	Brand saveBrand(Brand brand);
	
    // Um alle Marken aufzulisten
	List<Brand> findBrands();
}


// Ähnlich wie bei UserRepository nutzen wir ProductRepository um Produkte in unserer Datenkank zu speichern.

public interface ProductRepository {
	
	Product saveProduct(Product product);
	
	// Um Maasen-Mengen hinzufügen zu können
	boolean saveBatchProduct(List<Product> products);
	
	Product updateProduct(Product product);
	
	boolean removeProduct(int id);

	Product findProductById(int id);
	
	// Liste der aufgeführten Produkte, für andere Nutzer zum wählen aus allen Daten: um das gewählte zu findProductById zu übergeben und ins UI zu bringen.
	List<Product> findProducts();

}


// --- ENDE DES GEMERGTEN CODES ---
