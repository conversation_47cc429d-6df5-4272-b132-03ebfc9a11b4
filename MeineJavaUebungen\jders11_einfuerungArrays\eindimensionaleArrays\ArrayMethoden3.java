package jders11_einfuerungArrays.eindimensionaleArrays;

import java.util.Scanner;

public class ArrayMethoden3 {
    static Scanner sc = new Scanner(System.in);

    public static void main(String[] args) {

        int arrayLaenge; // Variable zur Speicherung der Array-Länge

        System.out.print("Wie viele Elemente soll die Array Serie enthalten : ");
        arrayLaenge = sc.nextInt(); // Benutzer gibt die gewünschte Array-Länge ein

        int[] serie = serienWerteEingeben(arrayLaenge); // Aufruf der Methode serienWerteEingeben, um das Array mit Werten zu füllen

        serienWerteAusgeben(serie); // Aufruf der Methode serienWerteAusgeben, um die Array-Werte auszugeben

        System.out.println("Der Größte Wert im Array ist : " + findeGroesstenWert(serie));

        // Da jetzt wir Methode mit Rückgabe und einer Ausgabe editiert haben
        // Aufruf der Methode findeAnzahlGemeinsamerWerte und Speicherung des Rückgabewerts in der Variable anzahlGemeinsamerWerte
        int dieAnzahlGemeinsamerWerte = findeAnzahlGemeinsamerWerte(serie, findeGroesstenWert(serie));

        // Ausgabe der Anzahl der Werte, die den größten Wert gemeinsam haben
        // System.out.println("Anzahl der Werte, die den größten Wert gemeinsam haben: " + anzahlGemeinsamerWerte);

        //System.out.println("Anzahl der Werte, die den größten Wert gemeinsam haben: " + findeAnzahlGemeinsamerWerte(serie, findeGroesstenWert(serie)));

    }

    public static void serienWerteAusgeben(int[] z) {
        // Methode zur Ausgabe der Array-Werte

        for (int i = 0; i < z.length; i++) {
            System.out.println("Im " + (i + 1) + ". befindet sich der Wert " + z[i]);
            // Ausgabe des Indexes und des Werts des aktuellen Elements im Array
        }

    }

    public static int[] serienWerteEingeben(int laenge) {
        // Methode zur Eingabe der Array-Werte
        int[] zahlenSerie = new int[laenge]; // Erstellung eines Arrays mit der übergebenen Länge

        for (int i = 0; i < zahlenSerie.length; i++) {

            System.out.print((i + 1) + ". Element eingeben : ");
            // Aufforderung zur Eingabe des Werts für das aktuelle Element

            zahlenSerie[i] = sc.nextInt(); // Eingabe des Werts und Speicherung im Array
        }

        return zahlenSerie; // Rückgabe des befüllten Arrays
    }

    public static int findeGroesstenWert(int[] zahlen) {
        // Methode zum Finden des größten Werts im Array
        int groessterWert = zahlen[0];

        for (int i = 1; i < zahlen.length; i++) {
            if (zahlen[i] > groessterWert) {
                groessterWert = zahlen[i];
            }
        }

        return groessterWert;
    }

    public static int findeAnzahlGemeinsamerWerte(int[] zahlen, int groessterWert) {
        int anzahlGemeinsamerWerte = 0;

        for (int i = 0; i < zahlen.length; i++) {
            if (zahlen[i] == groessterWert) {
                anzahlGemeinsamerWerte++;
            }
        }

        if (anzahlGemeinsamerWerte == 1) {
            anzahlGemeinsamerWerte = 0; // Keine gemeinsamen Werte gefunden
        } else if (anzahlGemeinsamerWerte == 2) {
            anzahlGemeinsamerWerte = 1; // Nur ein gemeinsamer Wert gefunden
        }
        //
        System.out.println("METHODEN AUSGABE: Anzahl der Werte, die den größten Wert gemeinsam haben: " + anzahlGemeinsamerWerte);

        return anzahlGemeinsamerWerte;
    }
}
