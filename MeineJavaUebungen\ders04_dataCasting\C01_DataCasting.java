package ders04_dataCasting;

/**
 * Diese Klasse demonstriert das Typecasting (Typumwandlung) in Java.
 *
 * Es werden zwei Arten von Typecasting gezeigt:
 * 1. Implizites Casting (Widening, Erweiterung): Automatische Umwandlung von kleineren zu größeren Datentypen
 * 2. Explizites Casting (Narrowing, Verengung): Manuelle Umwandlung von größeren zu kleineren Datentypen
 *    (kann zu Datenverlust führen)
 */
public class C01_DataCasting {
    /**
     * Die Hauptmethode demonstriert verschiedene Arten von Typumwandlungen (Casting) in Java.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {

        // Deklaration und Initialisierung von Variablen verschiedener primitiver Datentypen
        int a = 134;      // 32 Bit
        double b = 23.4;  // 64 Bit, Fließkommazahl
        short c = 34;     // 16 Bit
        byte d = 14;      // 8 Bit

        System.out.println("Ursprüngliche Werte:");
        System.out.println("a (int): " + a);
        System.out.println("b (double): " + b);
        System.out.println("c (short): " + c);
        System.out.println("d (byte): " + d);
        System.out.println();

        // 1. IMPLIZITES CASTING (Widening, Erweiterung)
        // Automatische Umwandlung von kleineren zu größeren Datentypen
        System.out.println("Implizites Casting (Widening):");

        // int -> double (32 Bit -> 64 Bit)
        double e = a;   // Datentyp des Wertes ist int, Variablen-Datentyp double ist größer
        System.out.println("int " + a + " zu double: " + e);

        // short -> int (16 Bit -> 32 Bit)
        int f = c;      // Datentyp des Wertes ist short, Variablen-Datentyp int ist größer
        System.out.println("short " + c + " zu int: " + f);

        // byte -> short (8 Bit -> 16 Bit)
        short g = d;    // byte < short, automatische Umwandlung (auto widening)
        System.out.println("byte " + d + " zu short: " + g);
        System.out.println();

        // 2. EXPLIZITES CASTING (Narrowing, Verengung)
        // Manuelle Umwandlung von größeren zu kleineren Datentypen (kann zu Datenverlust führen)
        System.out.println("Explizites Casting (Narrowing):");

        // double -> short (64 Bit -> 16 Bit)
        short h = (short) b;    // Variablen-Datentyp short ist kleiner als b's Datentyp double
        System.out.println("double " + b + " zu short: " + h); // 23 (Nachkommastellen gehen verloren)

        // int -> byte (32 Bit -> 8 Bit)
        byte l = (byte) a;      // Variablen-Datentyp byte ist kleiner als a's Datentyp int
        System.out.println("int " + a + " zu byte: " + l);  // -122 (Wertebereich-Überlauf)

        // double -> int (64 Bit -> 32 Bit)
        int m = (int) b;        // Variablen-Datentyp int ist kleiner als b's Datentyp double
        System.out.println("double " + b + " zu int: " + m);  // 23 (Nachkommastellen gehen verloren)

        /*
         * WICHTIG: Bei der Umwandlung von größeren zu kleineren Datentypen (Narrowing) muss beachtet werden:
         * 1. Nachkommastellen können verloren gehen (bei Fließkommazahlen)
         * 2. Es kann zu Wertebereich-Überläufen kommen, was zu unerwarteten Werten führt
         * 3. Java führt diese Umwandlung nicht automatisch durch, sondern erfordert explizites Casting
         */
    }
}
