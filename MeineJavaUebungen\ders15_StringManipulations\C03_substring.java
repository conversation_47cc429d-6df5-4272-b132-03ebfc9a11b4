package ders15_StringManipulations;

public class C03_substring {
    public static void main(String[] args) {

        String str = "Java ist Leben";

        /* substring() ermöglicht uns die Möglichkeit den gewollten teil eines Strings zu erhalten
        * hat zwei Einsatzgebiete:
        * 1 -wenn wir den Anfangs index geben erhalten wir von diesem index bis zum Ende den Bereich
        * 2 -Anfangs und End index gibt die Charaktere da zwischen aus  */

        System.out.println(str.substring(3));  // a ist Leben
        System.out.println(str.substring(5, 8)); // ist

        // (str.substring(15)) nur innerhalb des index einzusetzen
        // jedoch ist zu beachten, das ein weiteres als die Länge etwas wie nichts nur Leeres als Ausgabe noch hinzuzufügen tut
        // hierbei ist der erste index inbegriffen jedoch der 2. index das ende und nicht mit eingeschlossen
        System.out.println(str.substring(5, 14));
        System.out.println("Danke");
    }
}
