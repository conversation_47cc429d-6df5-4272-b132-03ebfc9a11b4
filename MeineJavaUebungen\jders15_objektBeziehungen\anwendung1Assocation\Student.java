package jders15_objektBeziehungen.anwendung1Assocation;

import java.util.ArrayList;

public class Student {

    private String vorname;

    private String nachname;

    private int geburtsjahr;

    // Ein Student kann mehr als eine Telefonnummer haben
    private ArrayList<String> telefonnummern;

    private String studentenNummer;

    private static String schule;

    // Muss nicht den Namen unterrichtsFaecher als ArrayList zugewiesen bekommen, wichtig sind nur die .add Werte die übergeben werden.
    private ArrayList<String> unterrichtsFaecher;

    private Adressen adresse;

    public Student() {

    }

    public Student(String vorname, String nachname, int geburtsjahr, ArrayList<String> telefonnummern,
                   String studentenNummer, ArrayList<String> unterrichtsFaecher, Adressen adresse) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
        this.telefonnummern = telefonnummern;
        this.studentenNummer = studentenNummer;
        this.unterrichtsFaecher = unterrichtsFaecher;
        this.adresse = adresse;
    }

    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    public ArrayList<String> getTelefonnummern() {
        return telefonnummern;
    }

    public void setTelefonnummern(ArrayList<String> telefonnummern) {
        this.telefonnummern = telefonnummern;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    public static String getSchule() {
        return schule;
    }

    public static void setSchule(String schule) {
        Student.schule = schule;
    }

    public ArrayList<String> getUnterrichtsFaecher() {
        return unterrichtsFaecher;
    }

    public void setUnterrichtsFaecher(ArrayList<String> unterrichtsFaecher) {
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    public Adressen getAdresse() {
        return adresse;
    }

    public void setAdresse(Adressen adresse) {
        this.adresse = adresse;
    }

    /*
    // Wenn die statische Variable schule in der toString()-Methode angezeigt werden soll,
    // müssen wir sie explizit hinzufügen und den Wert manuell abrufen.
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Student{");
        sb.append("vorname='").append(vorname).append('\'');
        sb.append(", nachname='").append(nachname).append('\'');
        sb.append(", geburtsjahr=").append(geburtsjahr);
        sb.append(", telefonnummern=").append(telefonnummern);
        sb.append(", studentenNummer='").append(studentenNummer).append('\'');
        sb.append(", schule='").append(schule).append('\'');
        if (schule != null) {
            sb.append("'").append(schule).append('\'');
        } else {
            sb.append("N/A");
        }
        sb.append(", unterrichtsFaecher=").append(unterrichtsFaecher);
        sb.append(", adresse=").append(adresse);
        sb.append('}');
        return sb.toString();
    }
*/

    // Statische Variablen wie private static String schule sind jedoch Klassenvariablen
    // und gehören zum Klassenkontext anstelle des Objektkontexts. Daher sind sie nicht
    // Teil des Objektzustands und werden in der automatischen
    // Erstellung der toString()-Methode nicht berücksichtigt.
    @Override
    public String toString() {
        return "Student{" +
                "vorname='" + vorname + '\'' +
                ", nachname='" + nachname + '\'' +
                ", geburtsjahr=" + geburtsjahr +
                ", telefonnummern=" + telefonnummern +
                ", studentenNummer='" + studentenNummer + '\'' +
                ", unterrichtsFaecher=" + unterrichtsFaecher +
                ", adresse=" + adresse +
                '}';
    }

}
