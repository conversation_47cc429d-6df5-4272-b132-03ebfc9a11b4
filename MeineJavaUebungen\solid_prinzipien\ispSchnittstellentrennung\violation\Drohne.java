package solid_prinzipien.ispSchnittstellentrennung.violation;

public class <PERSON><PERSON><PERSON> extends Fahrzeug {

    private boolean cameraEin;

    public boolean istCameraEin() {
        return cameraEin;
    }


    @Override
    public void radioEin() {
        // <PERSON><PERSON>, <PERSON><PERSON><PERSON> hat kein Radio
    }

    @Override
    public void radioAus() {
        // Leer
    }

    @Override
    public void cameraEin() {
        cameraEin = true;
    }

    @Override
    public void cameraAus() {
        cameraEin = false;
    }
}
