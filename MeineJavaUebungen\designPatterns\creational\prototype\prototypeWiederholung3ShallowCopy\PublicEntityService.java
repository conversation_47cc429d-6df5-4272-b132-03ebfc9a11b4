package designPatterns.creational.prototype.prototypeWiederholung3ShallowCopy;


public class PublicEntityService {

    // In der Klasse PublicEntityService wird eine prototypeWiederholung4DeepCopy-basierte Implementierung des Kopiermusters verwendet.
    // In dieser Implementierung wird eine Dokument-Instanz als Prototyp verwendet, von dem neue Instanzen erstellt werden.


    public DokumentTyp findDokumentTypById(Long id) {

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        DokumentTyp dokumentTyp = new DokumentTyp();
        dokumentTyp.setId(id);

        // Die Methode deklariert eine String-Variable namens name
        String name;

        if (id.compareTo(1L) == 0) {
            name = "Persönlich";
        } else if (id.compareTo(2L) == 0) {
            name = "Betrieblich";

        } // Wenn die ID 3 oder höher ist, wird der Wert "Generell" festgelegt.
        else {
            name = "<PERSON>rell";
        }

        dokumentTyp.setName(name);

        return dokumentTyp;
    }

    // Methode findKategorieById() verwendet, um eine Kategorie-Instanz für jede der drei zulässigen IDs zurückzugeben.
    // Die name-Eigenschaft der Kategorie-Instanz wird dann verwendet, um den Namen der Kategorie auszugeben.
    public Kategorie findKategorieById(Long id) {

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        Kategorie kategorie = new Kategorie();

        // Die Methode deklariert eine String-Variable namens name
        String name;

        if (id.compareTo(1L) == 0) {
            name = "Privat";
        } else if (id.compareTo(2L) == 0) {
            name = "Betrieblich";
        } // // Wenn die ID 3 oder höher ist, wird der Wert "Öffentlich" festgelegt
        else {
            name = "Öffentlich";
        }

        kategorie.setName(name);

        return kategorie;
    }

    public Dokument findDokumentById(Long id) {

        // Die Methode `findDokumentById()` ist eine öffentliche Methode, die eine `Dokument`-Instanz zurückgibt.
        // Die Methode beginnt damit, dass sie eine `Thread.sleep()`-Anweisung ausführt, um 2000 Millisekunden zu warten. Dies ist ein Beispiel für eine künstliche Verzögerung, die in einer realen Anwendung verwendet werden könnte, um die Leistung zu simulieren.
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            System.err.println(e);
        }

        // Die Methode erstellt eine neue `Dokument`-Instanz und setzt die ID-Eigenschaft auf den angegebenen Wert.
        Dokument dokument = new Dokument();
        dokument.setId(id);


        // kurz form
        dokument.setDokumentTyp(findDokumentTypById(id));
        dokument.setKategorie(findKategorieById(id));

        //  Die Methode deklariert zwei `String`-Variablen namens `name` und `datei`
        String name;
        String inhalt;

        //  Die Methode vergleicht die übergebene ID mit den Werten 1, 2 und 3.
        if (id.compareTo(1L) == 0) {

            //  Wenn die ID 1 ist, werden die Werte `"Brief"` und `"Glückwünsche, Lieber Coder"` festgelegt.
            name = "Brief";
            inhalt = "Glückwünsche ..., Lieber Coder";
        } else if (id.compareTo(2L) == 0) {

            //  Wenn die ID 2 ist, werden die Werte `"Bericht"` und `"Regressionstest Ergebnisse"` festgelegt.
            name = "Bericht";
            inhalt = "Regressionstest Ergebnisse 2022";
        } else {

            //  Wenn die ID 3 oder höher ist, werden die Werte `"Hausordnung"` und `"Ordnungsvereinbarung"` festgelegt.
            name = "Hausordnung";
            inhalt = "Haus-Ordnungsvereinbarung des Geländes ...";
        }

        //  Die `name`- und `datei`-Eigenschaften der `Dokument`-Instanz werden auf die berechneten Werte festgelegt.
        dokument.setName(name);
        dokument.setInhalt(inhalt);

        //  Die Methode gibt die `Dokument`-Instanz zurück.
        return dokument;
    }
}
