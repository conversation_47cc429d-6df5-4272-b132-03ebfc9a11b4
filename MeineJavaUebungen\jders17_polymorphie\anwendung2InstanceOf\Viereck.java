package jders17_polymorphie.anwendung2InstanceOf;

public class Viereck extends Form {

    private double seite;

    public Viereck() {

    }

    public Viereck(double seite) {
        this.seite = seite;
    }

    // Wir geben im Beispiel nur ein Wert für die Seite eines Vierecks
    public void setSeite(double seite) {
        this.seite = seite;
    }

    @Override
    public double getInhalt() {
        // return super.getBereich() = 0
        return seite * seite;
    }

    @Override
    public double getUmfang() {
        return 4 * seite;
    }

}
