package jders13_objekte._02_objektZugriffsmodifikatoren;

public class AutoTest {
    public static void main(String[] args) {

        /*
        * Zugriffsmodifikatoren: public, private, protected, friendly
        *
        * friendly ohne expliziten Modifikator genannt auch "Paketzugriff": <PERSON><PERSON> bedeutet, dass
        * das Element (Klasse, Methode oder Variable) innerhalb des
        * gleichen Pakets sichtbar und zugänglich ist,
        * aber von Klassen außerhalb des Pakets nicht aufgerufen werden kann.
        *
        * public: Eine öffentliche Klasse, Methode oder Variable kann von überall im Programmcode aufgerufen werden,
        * sowohl innerhalb der eigenen Klasse als auch von anderen Klassen und Paketen.
        *
        * private: Private Elemente sind nur innerhalb der Klasse zugänglich,
        * in der sie deklariert wurden. Sie können nicht von anderen Klassen aufgerufen werden, auch nicht von Unterklassen.
        *
        * protected: Innerhalb des Pakets von allen Klassen zu erreichen, verhindert den zugriff von anderen Paketen
        *
        * */

        Auto car = new Auto();


    }
}
