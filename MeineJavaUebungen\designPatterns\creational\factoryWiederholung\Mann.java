package designPatterns.creational.factoryWiederholung;

public class Mann implements Person {

    private String getGeschlecht;

    private String getVorname;

    private String getNachname;

    private int getGeburtsjahr;

    public Mann(String getGeschlecht, String getVorname, String getNachname, int getGeburtsjahr) {
        this.getGeschlecht = getGeschlecht;
        this.getVorname = getVorname;
        this.getNachname = getNachname;
        this.getGeburtsjahr = getGeburtsjahr;
    }

    @Override
    public String getGeschlecht() {
        return null;
    }

    @Override
    public String getVorname() {
        return null;
    }

    @Override
    public String getNachname() {
        return null;
    }

    @Override
    public int getGeburtsjahr() {
        return 0;
    }

    @Override
    public String toString() {
        return "Mann{" +
                "getGeschlecht='" + getGeschlecht + '\'' +
                ", getVorname='" + getVorname + '\'' +
                ", getNachname='" + getNachname + '\'' +
                ", getGeburtsjahr=" + getGeburtsjahr +
                '}';
    }
}
