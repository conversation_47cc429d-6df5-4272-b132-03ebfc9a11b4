package solid_prinzipien.ocpOpenClosePrinzip.violation;

public class Fahrzeug {

    private int leistung;
    private int federungshoehe;

    public int getLeistung() {
        return leistung;
    }

    public int getFederungshoehe() {
        return federungshoehe;
    }

    public void setLeistung(final int leistung) {
        this.leistung = leistung;
    }

    public void setFederungshoehe(final int federungshoehe) {
        this.federungshoehe = federungshoehe;
    }

}
