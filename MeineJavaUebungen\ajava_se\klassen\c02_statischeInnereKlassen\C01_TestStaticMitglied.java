package ajava_se.klassen.c02_statischeInnereKlassen;

/**
 * Diese Klasse testet die Verwendung der statischen inneren Klasse
 * aus C01_StaticMitglied.
 */
public class C01_TestStaticMitglied {

    public static void main(String[] args) {
        // Erstellt eine Instanz der äußeren Klasse (wie bei jeder normalen Klasse).
        C01_StaticMitglied aeussereKlasse = new C01_StaticMitglied();

        // Erstellt eine Instanz der statischen inneren Klasse.
        // Syntax: AeussererKlassenname.InnereKlasse variablenName = new AeussererKlassenname.InnereKlasse();
        // Wichtig: Es wird KEINE Instanz der äußeren Klasse benötigt, um die innere zu erstellen
        // (im Gegensatz zu nicht-statischen inneren Klassen, wo es z.B. aeussere.new InnereKlasse() wäre).
        C01_StaticMitglied.Innere innereKlasse = new C01_StaticMitglied.Innere();

        // Gibt die Objekte aus (ruft deren jeweilige toString()-Methoden auf).
        System.out.println("Äußeres Objekt: " + aeussereKlasse);
        System.out.println("Inneres Objekt: " + innereKlasse);
    }
}

/**
 * Vergleich von statischen und nicht-statischen inneren Klassen:
 *
 * | Merkmal                        | Nicht-statische innere Klasse         | Statische innere Klasse              |
 * |--------------------------------|--------------------------------------|---------------------------------------|
 * | Instanz der äußeren Klasse     | erforderlich                         | nicht erforderlich                    |
 * | Zugriff auf äußeren Instanz    | implizit möglich (über Outer.this)   | nur über explizite Instanz            |
 * | Zugriff auf statische Member   | möglich                              | möglich                               |
 * | Zugriff auf nicht-statische Member | direkt möglich                   | nur mit Instanz der äußeren Klasse    |
 * | Speicherort im Bytecode        | Outer$Inner.class                    | Outer$Inner.class                     |
 */
