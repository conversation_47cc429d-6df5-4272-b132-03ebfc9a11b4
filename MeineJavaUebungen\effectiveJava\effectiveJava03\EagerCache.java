package effectiveJava.effectiveJava03;

import java.util.HashMap;

/* Im Gegensatz zum LazyCache wird der EagerCache beim Laden der Klasse erstellt, unabh<PERSON><PERSON><PERSON> davon,
ob er tatsächlich benötigt wird oder nicht. Dies kann in einigen Fällen zu unnötigem Speicherverbrauch führen.*/
public class EagerCache implements Cache{

    // Eine statische Variable, um die einzige Instanz dieser Klasse zu speichern.
    // Da sie als 'final' deklariert ist, kann sie nicht überschrieben werden.
    // Da sie beim Laden der Klasse initialisiert wird, wird sie als "eager" bezeichnet.
    // selbst wenn die klasse nicht ausgerufen wird, ist der Speicherplatz im Heap belegt, weil es als static initialisiert wurde
    private static final EagerCache instance = new EagerCache();

    private HashMap<Object, Object> map;

    private EagerCache(){
        map = new HashMap<Object, Object>();
    }

    // Die put-Methode implementiert das Hinzufügen eines Schlüssel-Wert-Paares zur Map.
    @Override
    public void put(Object key, Object value) {

    }

    // Die get-Methode implementiert das Abrufen eines Wertes anhand eines Schlüssels aus der Map.
    @Override
    public Object get(Object key) {
        return null;
    }

    // Die getInstance-Methode liefert die einzige Instanz dieser Klasse.
    // Da die Instanz beim Laden der Klasse erstellt wurde, wird sie einfach zurückgegeben.
    public static EagerCache getInstance() {
        return instance;
    }
}
