package jders22_generic.anwendung3GenericInterface;

public interface Verarbeitungsvorgaenge<T> {

    // in einem Interface können wir alles nur nutzen, wenn Werte zugewiesen sind. public String name = "<PERSON>";

    // public ArrayList<T> liste = new ArrayList<>();

    public boolean speichern(T t);

    public boolean loeschen(T t);

    public void auflisten();

    // Wie ein TestDruve genutzt werden kann hatten wir in dem package jders22_generic.anwendung1GenericKlassen bereits durchgeführt deshalb lasse ich es weg.
}
