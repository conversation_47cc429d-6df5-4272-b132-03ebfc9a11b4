package jders11_einfuerungArrays.eindimensionaleArrays;

public class ForArrayErweitert {
    public static void main(String[] args) {

        int[] zahlen = {1, 4, 6, 2, 5, 777, 543, 5454, 54, 44,};  // Ein Trailing-Komma hat keine Bedeutung für den Compiler oder das Verhalten des Codes

        int gesamt = 0;

        /*
        for (int i =0;i< zahlen.length;i++){
            System.out.println(zahlen[i]);
        }
        * Der auskommentierte Code verwendet eine traditionelle for-Schleife, um über das Array zahlen
        * zu iterieren und jeden Wert auszugeben. Es wird eine Laufvariable i verwendet, die von 0 bis zur Länge
        * des Arrays zahlen läuft. In jedem Schleifendurchlauf wird der Wert zahlen[i] ausgegeben.
        */

        for (int i : zahlen) {
            System.out.println(i);
        }
        /*In der erweiterten for-Schleife steht int i für den Datentyp der Elemente im Array zahlen,
         * und zahlen ist das Array, über das iteriert wird. Der Doppelpunkt (:) trennt die Laufvariable (i)
         * von der Sammlung (zahlen). Der Code liest sich also: "Für jedes Element i in zahlen".
         */

        for (int i : zahlen) {
            /*gesamt += i;
            * In der erweiterten for-Schleife for (int i : zahlen) wird i nicht als Index verwendet,
            * sondern als direkter Wert aus dem Array zahlen. Das bedeutet, dass i bereits den Wert des Elements enthält,
            * nicht den Index. Wenn du gesamt = gesamt + zahlen[i]; verwendest, versuchst du den Wert von zahlen[i] zu gesamt hinzuzufügen,
            * aber i enthält bereits den Wert, nicht den Index. Daher tritt der Fehler ArrayIndexOutOfBoundsException auf,
            * wenn i größer als der gültige Indexbereich des Arrays ist.*/
            gesamt = gesamt + i;
        }

        System.out.println("Gesamt : " + gesamt);
    }
}
