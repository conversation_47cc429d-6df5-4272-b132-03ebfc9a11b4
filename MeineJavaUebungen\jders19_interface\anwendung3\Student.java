package jders19_interface.anwendung3;

import java.util.ArrayList;

public class Student extends Person {

    private String studentenNummer;

    private ArrayList<String> unterrichtsFaecher;

    public Student() {

    }

    public Student(String vorname, String nachname, int geburtsjahr, Adresse adresse) {
        super(vorname, nachname, geburtsjahr, adresse);
    }

    public Student(String vorname, String nachname, int geburtsjahr, <PERSON><PERSON><PERSON> adresse, String studentenNummer, ArrayList<String> unterrichtsFaecher) {
        super(vorname, nachname, geburtsjahr, adresse);
        this.studentenNummer = studentenNummer;
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    public ArrayList<String> getUnterrichtsFaecher() {
        return unterrichtsFaecher;
    }

    public void setUnterrichtsFaecher(ArrayList<String> unterrichtsFaecher) {
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    @Override
    public String toString() {
        return "Student{" +
                "studentenNummer='" + studentenNummer + '\'' +
                ", unterrichtsFaecher='" + unterrichtsFaecher + '\'' +
                ", vorname='" + getVorname() + '\'' +
                ", nachname='" + getNachname() + '\'' +
                ", geburtsjahr='" + getGeburtsjahr() + '\'' +
                ", adresse='" + getAdresse() +
                '}';
    }
}
