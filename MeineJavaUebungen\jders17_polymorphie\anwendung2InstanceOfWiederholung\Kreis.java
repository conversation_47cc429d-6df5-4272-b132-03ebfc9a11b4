package jders17_polymorphie.anwendung2InstanceOfWiederholung;

public class Kreis extends Form {

    private double radius;

    public Kreis() {

    }

    public Kreis(double radius) {
        this.radius = radius;
    }

    public void setRadius(double radius) {
        this.radius = radius;
    }

    @Override
    public double getUmfang() {

        return 2* Math.PI  * radius;
    }

    @Override
    public double getInhalt() {
        return Math.PI * radius * radius;
    }
}