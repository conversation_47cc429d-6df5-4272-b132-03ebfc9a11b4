package ders03;

import java.util.Scanner;

/**
 * Diese K<PERSON>e demonstriert die Verwendung der Scanner-Klasse zum Einlesen von Dezimalzahlen
 * und die Berechnung von Fläche und Umfang eines Kreises.
 *
 * Die Klasse zeigt:
 * - Wie man mit nextDouble() Dezimalzahlen einliest
 * - Wie man mathematische Berechnungen direkt in der Ausgabe durchführt
 * - Die Formeln zur Berechnung von Kreisfläche (π * r²) und Kreisumfang (2 * π * r)
 */
public class C02_Scanner {
    /**
     * Die Hauptmethode liest den Radius eines Kreises ein und berechnet Fläche und Umfang.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Wir wollen den Radius eines Kreises vom Benutzer einlesen und
        // daraus Fläche und Umfang des Kreises berechnen und ausgeben

        // Mathematische Konstante Pi für genauere Berechnungen
        final double PI = Math.PI;  // Genauerer Wert als 3.14

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scan = new Scanner(System.in);

        // Benutzer zur Eingabe auffordern
        System.out.println("Bitte geben Sie den Radius eines Kreises ein (in cm):");

        // Radius als Dezimalzahl einlesen
        double radius = scan.nextDouble();

        // Alternativ 1: Fläche und Umfang in Variablen speichern
        // Dies ist auskommentiert, zeigt aber eine alternative Implementierung
        // double flaeche = PI * radius * radius;  // oder: PI * Math.pow(radius, 2)
        // double umfang = 2 * PI * radius;

        // Alternativ 2: Berechnung direkt in der Ausgabe durchführen
        // Dies spart Variablen, kann aber bei komplexeren Berechnungen unübersichtlich werden
        System.out.println("Fläche des Kreises: " + (PI * radius * radius) + " cm²");
        System.out.println("Umfang des Kreises: " + (2 * PI * radius) + " cm");

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scan.close();

        /*
         * Hinweise zu den Formeln:
         * - Kreisfläche: A = π * r²
         * - Kreisumfang: U = 2 * π * r
         *
         * Für präzisere Berechnungen verwenden wir Math.PI statt des gerundeten Werts 3.14.
         */
    }
}
