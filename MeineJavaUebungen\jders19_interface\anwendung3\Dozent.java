package jders19_interface.anwendung3;

public class Dozent extends Person {

    private String branche;

    public Dozent() {

    }

    // Constructor, wo nur die Person Eigenschaften gesendet werden
    public Dozent(String vorname, String nachname, int geburtsjahr, Adresse adresse) {
        super(vorname, nachname, geburtsjahr, adresse);
    }

    // Constructor, wo vom Dozenten alle Eigenschaften. In diesem fall die Branche und die Person Eigenschaften
    public Dozent(String vorname, String nachname, int geburtsjahr, Adresse adresse, String branche) {
        super(vorname, nachname, geburtsjahr, adresse);
        this.branche = branche;
    }

    public String getBranche() {
        return branche;
    }

    public void setBranche(String branche) {
        this.branche = branche;
    }

    @Override
    public String toString() {
        return "Dozent{" +
                "branche='" + branche + '\'' +
                "vorname='" + getVorname() + '\'' +
                ", nachname='" + getNachname() + '\'' +
                ", geburtsjahr=" + getGeburtsjahr() +
                ", adresse='" + getAdresse() +
                '}';
    }
}
