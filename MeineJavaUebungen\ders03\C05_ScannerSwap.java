package ders03;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert das Vertauschen (Swap) von zwei Variablenwerten in Java.
 *
 * Das Programm:
 * 1. Liest zwei ganze Zahlen vom Benutzer ein
 * 2. Vertauscht die Werte der beiden Variablen mithilfe einer temporären Variable
 * 3. Gibt die vertauschten Werte aus
 *
 * Wichtige Konzepte:
 * - Verwendung von nextInt() zum Einlesen von ganzen Zahlen
 * - Algorithmus zum Vertauschen von Variablenwerten
 * - Verwendung einer temporären Variable als Zwischenspeicher
 */
public class C05_ScannerSwap {
    /**
     * Die Hauptmethode liest zwei Zahlen ein, vertauscht ihre Werte und gibt das Ergebnis aus.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Aufgabe: Zwei ganze Zahlen einlesen, ihre Werte vertauschen und das Ergebnis ausgeben

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scan = new Scanner(System.in);

        // Erste Zahl einlesen
        System.out.println("Bitte geben Sie die erste ganze Zahl ein:");
        int zahl1 = scan.nextInt();

        // Zweite Zahl einlesen
        System.out.println("Bitte geben Sie die zweite ganze Zahl ein:");
        int zahl2 = scan.nextInt();

        // Ausgabe der ursprünglichen Werte
        System.out.println("\nUrsprüngliche Werte:");
        System.out.println("Erste Zahl: " + zahl1);
        System.out.println("Zweite Zahl: " + zahl2);

        // Vertauschen der Werte mit einer temporären Variable
        System.out.println("\nVertauschen der Werte...");

        // Schritt 1: Temporäre Variable initialisieren und Wert von zahl2 speichern
        int temp = zahl2;   // temp = zahl2, zahl2 bleibt unverändert

        // Schritt 2: Wert von zahl1 in zahl2 kopieren
        zahl2 = zahl1;      // zahl2 hat jetzt den ursprünglichen Wert von zahl1

        // Schritt 3: Gespeicherten Wert (ursprünglich zahl2) in zahl1 kopieren
        zahl1 = temp;       // zahl1 hat jetzt den ursprünglichen Wert von zahl2

        // Ausgabe der vertauschten Werte
        System.out.println("\nVertauschte Werte:");
        System.out.println("Erste Zahl: " + zahl1);
        System.out.println("Zweite Zahl: " + zahl2);

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scan.close();

        /*
         * Hinweis zum Vertauschen von Variablenwerten:
         *
         * Das Vertauschen von zwei Variablen erfordert eine dritte temporäre Variable,
         * da sonst ein Wert überschrieben und verloren gehen würde.
         *
         * Alternative Methoden zum Vertauschen (ohne temporäre Variable):
         * 1. In Java mit arithmetischen Operationen (nur für numerische Typen):
         *    zahl1 = zahl1 + zahl2;
         *    zahl2 = zahl1 - zahl2;
         *    zahl1 = zahl1 - zahl2;
         *
         * 2. Mit XOR-Operation (nur für ganzzahlige Typen):
         *    zahl1 = zahl1 ^ zahl2;
         *    zahl2 = zahl1 ^ zahl2;
         *    zahl1 = zahl1 ^ zahl2;
         */
    }
}
