package jders21_ordnerVerarbeitungen.anwendung5Stream;

public class Test {
    public static void main(String[] args) {

        // Als Stream wird der Student gesamt gesendet

        // Student Instanz erstellen
        Student student1 = new Student("<PERSON><PERSON><PERSON>", "Butter", 1989, "2389");
        Student student2 = new Student("<PERSON>", "<PERSON><PERSON>", 1990, "2390");
        Student student3 = new Student("<PERSON>", "But<PERSON>", 1991, "2391");

        DateiSchreiben dateiSchreiben = new DateiSchreiben();

        dateiSchreiben.dateiErstellen("studenten");

        dateiSchreiben.dateiStudentSpeichern(student1);
        dateiSchreiben.dateiStudentSpeichern(student2);
        dateiSchreiben.dateiStudentSpeichern(student3);

        dateiSchreiben.dateiSchliessen();


        DateiLesen dateiLesen = new DateiLesen();

        dateiLesen.leseDatei("studenten");
        dateiLesen.dateiLesen();
        dateiLesen.dateiLesenSchliessen();

        System.out.println("<PERSON> says Bye");


    }
}
