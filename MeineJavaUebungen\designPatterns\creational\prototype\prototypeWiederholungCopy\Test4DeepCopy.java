package designPatterns.creational.prototype.prototypeWiederholungCopy;

import java.util.Arrays;

public class Test4DeepCopy {
    public static void main(String[] args) {

        // Verwendet den Ausdruck new Integer(old[i]), um für jedes Element in old ein neues
        // Integer-Objekt zu erstellen und dieses in new1 zu kopieren.
        // Dies führt zu einer echten Tiefenkopie, da separate Integer-Objekte erstellt werden.

        final int NUM = 8;
        Integer[] old = new Integer[NUM];
        Integer[] new1 = new Integer[NUM];

        for (int i = 0; i < NUM; i++) {
            old[i] = i;
        }

        for (int i = 0; i < NUM; i++) {
            new1[i] = new Integer(old[i]);
        }

        new1[3] = 30000;

        System.out.println(Arrays.toString(old));
        System.out.println(Arrays.toString(new1));


        System.out.println(old);  // [I@6bc7c054
        System.out.println(new1);  // [I@232204a1
    }
}
