package ders11_ternaryOperator;

import java.util.Scanner;

public class C01_Ternary {
    public static void main(String[] args) {
        // eine Zahl der Nutzer Eingabe wird überprüft,
        // ob Eingabe einer Zahl durch 5 teilbar ist,
        // ausgegeben wird das die "Zahl durch 5 teilbar" ist oder "nicht durch 5 teilbar" ist

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie eine Positive ganze Zahl ein");
        int eingabeZahl = scan.nextInt();

        //wenn es nur direkt als string ausgegeben werden soll
        System.out.println(eingabeZahl % 5 == 0 ? "Zahl durch 5 teilbar" : "nicht durch 5 teilbar");
    }
}
