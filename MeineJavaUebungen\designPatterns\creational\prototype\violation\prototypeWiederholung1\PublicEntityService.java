package designPatterns.creational.prototype.violation.prototypeWiederholung1;

// Der import von falschen Paketen kann zu Komplikationen führen, wenn es mehrere Klassen
// mit dem gleichen Namen in verschiedenen Paketen gibt, ist es wichtig keine falschen importe vorzudefinieren.
// in unvermeidlichem fall müssten Sie den vollen Pfad zur gewünschten Klasse angeben, um sicherzustellen, dass die richtige Klasse verwendet wird.
// import designPatterns.creational.prototype.prototypeWiederholung4DeepCopy.Kategorie;

public class PublicEntityService {

    /* Long ist eine Wrapper-Klasse in Java. Wrapper-Klassen umhüllen primitive Datentypen,
    sodass sie als Objekte behandelt werden können. Das ermöglicht die Verwendung von objektorientierten
    Funktionen und Eigenschaften mit primitiven Datentypen.

    In Java gibt es eine Wrapper-Klasse für jeden primitiven Datentyp. Die Long-Klasse ist die Wrapper-Klasse
    für den primitiven Datentyp long. Sie bietet eine Reihe von Methoden und Eigenschaften,
    die die Arbeit mit long-Werten erleichtern.*/

    // Methode findDokumentTypById() verwendet, um eine DokumentTyp-Instanz für die ID 1 zurückzugeben.
    // Die name-Eigenschaft der DokumentTyp-Instanz wird dann verwendet, um den Namen des Dokumentes auszugeben.
    public DokumentTyp findDokumentTypById(Long id) {

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        DokumentTyp dokumentTyp = new DokumentTyp();
        dokumentTyp.setId(id);

        // Die Methode deklariert eine String-Variable namens name
        String name;

        if (id.compareTo(1L) == 0) {
            name = "Persönlich";
        } else if (id.compareTo(2L) == 0) {
            name = "Betrieblich";

        } // Wenn die ID 3 oder höher ist, wird der Wert "Generell" festgelegt.
        else {
            name = "Generell";
        }

        dokumentTyp.setName(name);

        return dokumentTyp;
    }

    // Methode findKategorieById() verwendet, um eine Kategorie-Instanz für jede der drei zulässigen IDs zurückzugeben.
    // Die name-Eigenschaft der Kategorie-Instanz wird dann verwendet, um den Namen der Kategorie auszugeben.
    public Kategorie findKategorieById(Long id) {

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        Kategorie kategorie = new Kategorie();

        // Die Methode deklariert eine String-Variable namens name
        String name;

        if (id.compareTo(1L) == 0) {
            name = "Privat";
        } else if (id.compareTo(2L) == 0) {
            name = "Betrieblich";
        } // // Wenn die ID 3 oder höher ist, wird der Wert "Öffentlich" festgelegt
        else {
            name = "Öffentlich";
        }

        kategorie.setName(name);

        return kategorie;
    }

    public Dokument findDokumentById(Long id) {

        // Die Methode `findDokumentById()` ist eine öffentliche Methode, die eine `Dokument`-Instanz zurückgibt.
        // Die Methode beginnt damit, dass sie eine `Thread.sleep()`-Anweisung ausführt, um 2000 Millisekunden zu warten. Dies ist ein Beispiel für eine künstliche Verzögerung, die in einer realen Anwendung verwendet werden könnte, um die Leistung zu simulieren.
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            System.err.println(e);
        }

        // Die Methode erstellt eine neue `Dokument`-Instanz und setzt die ID-Eigenschaft auf den angegebenen Wert.
        Dokument dokument = new Dokument();
        dokument.setId(id);

        /*
        // kurz form
        dokument.setDokumentTyp(findDokumentTypById(id));
        dokument.setKategorie(findKategorieById(id));*/

        // Die Methode ruft die Methode `findDokumentTypById()` auf, um den Dokumenttyp für die angegebene ID zu finden, um dann dokumentTyp zu setzen.
        DokumentTyp dokumentTyp = findDokumentTypById(id);
        dokument.setDokumentTyp(dokumentTyp);

        // Die Methode ruft die Methode `findKategorieById()` auf, um die Kategorie für die angegebene ID zu finden, um dann kategorie zu setzen.
        Kategorie kategorie = findKategorieById(id);
        dokument.setKategorie(kategorie);

        //  Die Methode deklariert zwei `String`-Variablen namens `name` und `datei`
        String name;
        String inhalt;

        //  Die Methode vergleicht die übergebene ID mit den Werten 1, 2 und 3.
        if (id.compareTo(1L) == 0) {

            //  Wenn die ID 1 ist, werden die Werte `"Brief"` und `"Glückwünsche, Lieber Coder"` festgelegt.
            name = "Brief";
            inhalt = "Glückwünsche ..., Lieber Coder";
        } else if (id.compareTo(2L) == 0) {

            //  Wenn die ID 2 ist, werden die Werte `"Bericht"` und `"Regressionstest Ergebnisse"` festgelegt.
            name = "Bericht";
            inhalt = "Regressionstest Ergebnisse";
        } else {

            //  Wenn die ID 3 oder höher ist, werden die Werte `"Hausordnung"` und `"Ordnungsvereinbarung"` festgelegt.
            name = "Hausordnung";
            inhalt = "Haus-Ordnungsvereinbarung des Geländes ...";
        }

        //  Die `name`- und `datei`-Eigenschaften der `Dokument`-Instanz werden auf die berechneten Werte festgelegt.
        dokument.setName(name);
        dokument.setInhalt(inhalt);

        //  Die Methode gibt die `Dokument`-Instanz zurück.
        return dokument;
    }
}
