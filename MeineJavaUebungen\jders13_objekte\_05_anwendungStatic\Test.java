package jders13_objekte._05_anwendungStatic;

public class Test {
    public static void main(String[] args) {

        Student.setSchule("Dumlupinar");
        // Wenn public static dann nutzen wir Student.schule=" ";

        Student student1 = new Student();

        student1.setVorname("Hans");
        student1.setNachname("Forst");

        Student student2 = new Student();

        student2.setVorname("Gunnar");
        student2.setNachname("Teiche");

        // Bei student1 wird die Methode getSchule direkt auf dem student1-Objekt aufgerufen,
        // um den Wert der schule-Variable abzurufen
        System.out.println("Student 1. Informationen : ");
        System.out.println("Name : " + student1.getVorname()+
                ", Nachname : "+ student1.getNachname()+
                ", Studenten Nummer : " + student1.getStudentenNummer()+
                ", Geburtsjahr : "+student1.getGerburtsjahr()+ ", Schule : " + student1.getSchule());
        System.out.println();

        // Bei  Bei student2 wird die Methode getSchule auf der Klasse Student aufgerufen,
        // um den Wert der schule-Variable für alle Studentenobjekte abzurufen.
        System.out.println("Student 2. Informationen : ");
        System.out.println("Name : " + student2.getVorname()+
                ", Nachname : "+ student2.getNachname()+
                ", Studenten Nummer : " + student2.getStudentenNummer()+
                ", Geburtsjahr : "+student2.getGerburtsjahr()+
                // Für statische Gemeinsamkeiten wählen wir die klasse aus
                ", Schule : " + Student.getSchule());

    }
}
