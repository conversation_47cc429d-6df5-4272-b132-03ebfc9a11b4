package jders21_ordnerVerarbeitungen.anwendung1WriteToFile;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class WriteToFile {

    public static void main(String[] args) throws IOException {

        /* FileWriter bietet keine direkte Methode, um auf Fehler zu überprüfen, aber wir können die Methode flush() verwenden,
        um sicherzustellen, dass die Daten in die Datei geschrieben werden, und dann die Methode close() aufrufen.*/

        /*
        // FileWriter ab Java11
        // Create a new file
        Path path = Paths.get("C:/Users/<USER>/Desktop/Projeler/Dokument3.txt");

        // Write to the file
        Files.writeString(path, "Hello, world!");

        // Close the file
        Files.close(path);*/


        // Alternativ für Java8
        // Create a new file
        File file = new File("C:/Users/<USER>/Desktop/Projeler/Dokument3.txt");

        // Create a file writer
        FileWriter writer = new FileWriter(file);

        // Write to the file
        writer.write("Hello, dünya!");

        // Close the file writer
        writer.close();

    }
}
