package designPatterns.creational.factory;

public class S23 implements Handy {

    /* Wenn Sie die Zeilen in der Klasse S23 nicht deklarieren würden, würden die Methoden
    des Handy-Interfaces ohne Implementierung bleiben, was dazu führen würde, dass die Klasse
    nicht kompiliert werden kann. Die Methoden wären dann in der Klasse S23 nutzlos, da sie keine
    tatsächlichen Informationen zurückgeben könnten. Die Deklaration und Implementierung dieser Methoden
    in der Klasse S23 ist notwendig, um die gewünschten Informationen (Marke, Modell, Größe, Gewicht)
    für das spezifische Handy-Modell bereitzustellen.*/
    private String marke;  // Hier wird die Marke des Handys gespeichert
    private String model;  // Hier wird das Modell des Handys gespeichert
    private int groesse;    // Hier wird die Größe des Handys gespeichert
    private int gewicht;    // Hier wird das Gewicht des Handys gespeichert

    /**
     * Konstruktor der Klasse S23, der verwendet wird, um ein neues Handy-Objekt des Modells S23 zu erstellen.
     *
     * @param marke Die Marke des Handys (z.B. Samsung, Apple).
     * @param model Das Modell des Handys (z.B. S23).
     * @param groesse Die Größe des Handys in irgendeiner Maßeinheit (z.B. Zoll).
     * @param gewicht Das Gewicht des Handys in irgendeiner Gewichtseinheit (z.B. Gramm).
     */
    public S23(String marke, String model, int groesse, int gewicht) {
        // Weist den übergebenen Werten die entsprechenden Eigenschaften des Handy-Objekts zu.
        this.marke = marke;
        this.model = model;
        this.groesse = groesse;
        this.gewicht = gewicht;
    }

    @Override
    public String getMarke() {
        return marke;       // Gibt die gespeicherte Marke des Handys zurück
    }

    @Override
    public String getModel() {
        return model;       // Gibt das gespeicherte Modell des Handys zurück
    }

    @Override
    public int getGroesse() {
        return groesse;     // Gibt die gespeicherte Größe des Handys zurück
    }

    @Override
    public int getGewicht() {
        return gewicht;     // Gibt das gespeicherte Gewicht des Handys zurück
    }

    @Override
    public String toString() {
        return "S23{" +
                "marke='" + marke + '\'' +
                ", model='" + model + '\'' +
                ", groesse=" + groesse +
                ", gewicht=" + gewicht +
                '}';
    }
}
