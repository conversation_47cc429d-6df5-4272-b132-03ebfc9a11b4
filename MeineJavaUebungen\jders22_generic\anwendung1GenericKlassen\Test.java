package jders22_generic.anwendung1GenericKlassen;


import java.util.ArrayList;

public class Test {

    public static void main(String[] args) {

        Dozent dozent1 = new Dozent("Emir", "Oezdemir", 1984);
        dozent1.setBranche("Java");

        Dozent dozent2 = new Dozent("York", "<PERSON><PERSON>", 1980, "Physik");

        Dozent dozent3 = new Dozent("Muster", "Mann", 1999);
        dozent3.setBranche("Probe");

        Student student1 = new Student("Jörn", "Bruckner", 2002, "112200");
        Student student2 = new Student("Jen<PERSON>", "Bucher", 2003, "112211");
        Student student3 = new Student();


        Verarbeitungsvorgaenge<Dozent> verarbeitungDozent = new Verarbeitungsvorgaenge<Dozent>();

        System.out.println("Dozenten : ");
        verarbeitungDozent.speicher(dozent3);
        verarbeitungDozent.speicher(dozent2);
        verarbeitungDozent.speicher(dozent1);

        //verarbeitungDozent.auflisten();
        ArrayList<Dozent> dozenten = verarbeitungDozent.getListe();

        for (Dozent dozent : dozenten) {
            System.out.println(dozent.getVorname() + " " + dozent.getNachname());
        }

        Verarbeitungsvorgaenge<Student> verarbeitungStudent = new Verarbeitungsvorgaenge<Student>();

        System.out.println("Studenten : ");
        verarbeitungStudent.speicher(student3);
        verarbeitungStudent.speicher(student2);
        verarbeitungStudent.speicher(student1);

        verarbeitungStudent.auflisten();
    }
}
