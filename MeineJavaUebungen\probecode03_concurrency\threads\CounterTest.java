package probecode03_concurrency.threads;

/*- Das Runnable-Interface sollte von jeder Klasse implementiert werden, deren In<PERSON> von einem
    Thread ausgeführt werden sollen. Die Klasse muss eine Methode ohne Argumente namens run definieren.

   -Dieses Interface wurde entwickelt, um ein gemeinsames Protokoll für Objekte bereitzustellen,
    die Code ausführen möchten, während sie aktiv sind. Zum Beispiel wird Runnable von der Klasse Thread implementiert.

   -Aktiv zu sein bedeutet einfach, dass ein Thread gestartet wurde und noch nicht gestoppt wurde.

   -Darüber hinaus bietet Runnable die Möglichkeit für eine Klasse, aktiv zu sein, ohne die Klasse Thread zu unterklassifizieren.
    Eine Klasse, die Runnable implementiert, kann ohne Unterklassifizierung von Thread ausgeführt werden,
    indem eine Instanz von Thread instanziiert und sich selbst als Ziel übergibt.
    In den meisten Fällen sollte das Runnable-Interface verwendet werden, wenn Sie nur die Methode run() und
    keine anderen Methoden von Thread überschreiben möchten. Dies ist wichtig, da Klassen nicht unterklassifiziert werden sollten,
    es sei denn, der Programmierer beabsichtigt, das grundlegende Verhalten der Klasse zu ändern oder zu verbessern.

   -Wenn ein Objekt, das das Interface Runnable implementiert, verwendet wird, um einen Thread zu erstellen,
    führt das Starten des Threads dazu, dass die Methode run des Objekts in diesem separat ausgeführten Thread aufgerufen wird.

-   -Der allgemeine Vertrag der Methode run ist, dass sie jede beliebige Aktion ausführen kann.*/
public class CounterTest {
    public static void main(String[] args) {
        // Der Code, den Sie bereitgestellt haben, führt zu einem Problem, das als Race Condition bekannt ist.
        // Dies tritt auf, wenn zwei oder mehr Threads gleichzeitig auf dieselben Daten zugreifen
        // und diese ändern, was zu unerwarteten und fehlerhaften Ergebnissen führt.

        Counter counter = new Counter();
        Thread t1 = new Thread(() -> {
            for (int i = 0; i < 100_000; i++) {
                counter.increment();
            }
            System.out.println("Count ist " + counter.getCount() + ";Thread ist " + Thread.currentThread().getName());

            });

        Thread t2 = new Thread(() -> {
            for (int i = 0; i < 100_000; i++) {
                counter.increment();
            }
            System.out.println("Count ist " + counter.getCount() + ";Thread ist " + Thread.currentThread().getName());

        });

        t1.start();
        t2.start();
        /* Synchronized und ein klassisches Beispiel für eine Race Condition :
           Count ist 185182;Thread ist Thread-1
           Count ist 200000;Thread ist Thread-0

           Wenn Thread-1 und Thread-2 gleichzeitig auf die increment() Methode zugreifen,
           lesen sie beide den aktuellen Wert von count, erhöhen ihn um eins und speichern ihn dann zurück.
           Wenn diese Operationen genau gleichzeitig stattfinden, kann es passieren, dass beide Threads
           den gleichen Wert lesen, ihn inkrementieren und dann denselben inkrementierten Wert zurückschreiben.
           Dies bedeutet, dass obwohl increment() zweimal aufgerufen wurde
           (einmal von jedem Thread), count nur um eins erhöht wurde.

            In unserem speziellen Fall sieht es so aus, als ob der erste Thread 185182 mal erfolgreich inkrementiert hat
            und der zweite Thread den Rest (also 14818 mal) bevor der erste Thread seine Ausgabe macht.
            Danach macht der erste Thread weitere 14818 Inkrementierungen, um auf
            die Gesamtzahl von 200000 zu kommen und gibt seine Ausgabe aus.
        *
        */

        }
    }
