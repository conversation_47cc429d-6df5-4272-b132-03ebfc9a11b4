package effectiveJava.effectiveJava01.item1;

// Die Verwendung static Factory-Methoden vs Konstruktoren
public class Item1 {

    public static void main(String[] args) {

        // Generell ist es gar nicht sinnvoll Factory-Methoden einzusetzen wenn, unser Essens-Typ wie im Beispiel der Essen-Klasse klar ist

        /* Factory-Methoden sind besonders nützlich, wenn die Erstellung von Objekten komplexer ist,
        eine gewisse Logik oder Validierung erfordert oder wenn Subklassen unterschiedliche Implementierungen
        der Factory-Methode haben sollen.
        In unserem Beispiel wäre die direkte Instanziierung der Essen-Klasse angemessen, da der Typ des Objekts
        (z.B. <PERSON> oder Eis) bereits klar ist und keine spezielle Logik für die Erstellung wir benötigen.*/

        // Die Methode Boolean.valueOf(boolean) veranschaulicht diese Technik: Sie erzeugt nie ein Objekt
        //Boolean.valueOf(b)

        Essen essen = Essen.kaltEssen("Eis");

        /* -Ein Vorteil der statischen Factory-Methoden besteht darin, dass sie im Gegensatz zu
          Konstruktoren Namen haben, sie nicht wie Konstruktoren der Einschränkung unterliegen,
          dass eine Klasse nur eine solche Methode mit einer gegebenen Signatur haben kann

          Ein zweiter Vorteil statischer Factory-Methoden besteht darin, dass sie im Gegensatz
          zu Konstruktoren nicht bei jedem Aufruf ein neues Objekt erzeugen müssen.
          Dies ermöglicht es unveränderlichen Klassen (Thema 13), vorgefertigte Instanzen zu
          verwenden oder Instanzen bei ihrer Erzeugung zu cachen, denn mit einem Konstruktor haben
          wir nicht die Chance solch ein Mechanismus zu nutzen

          Der dritte Vorteil der statischen Factory-Methoden besteht darin, dass sie im Gegensatz
          zu Konstruktoren ein Objekt von jedem Untertyp ihres Rückgabetyps liefern können

          Der wichtigste Nachteil statischer Factory-Methoden ist der, dass Sie von Klassen
          ohne öffentliche oder geschützte Konstruktoren keine Unterklassen bilden können.
          Das gilt auch für nicht öffentliche Klassen, die von öffentlichen statischen Factorys
          zurückgegeben wurden

          Ein zweiter Nachteil der statischen Factory-Methoden besteht darin, dass sie sich
          nicht so leicht von anderen statischen Methoden unterscheiden lassen. Sie sind in
          der API-Dokumentation nicht in gleicher Weise wie die Konstruktoren hervorgehoben.

          Factory-Method
          + freie Benennung erreichen
          + die ganze Kontrolle für die Umkehrung aller Instanzen
          + Kontrolle über den typ des Objektes der returned wird erhalten
          + Entkopplung zwischen dem Ersteller und dem Benutzer des erstellten Objekts

          Denn darüber hinaus bietet die Verwendung von Factory-Methoden auch den Vorteil der Entkopplung
          zwischen dem Ersteller und dem Benutzer des erstellten Objekts. Dies bedeutet, dass der Benutzer
          nicht über die interne Implementierung des Erstellungsprozesses informiert werden muss und nur die
          bereitgestellte Schnittstelle verwenden kann. Dies unterstützt das Prinzip der Abstraktion und fördert
          den modularen und wartbaren Code.
          Es gibt auch weitere Designmuster, die auf den Konzepten der Factory-Methoden aufbauen, wie z.B. das abstrakte
          Fabrikmuster und das Builder-Muster. Diese Muster erweitern die Idee der Erstellung von Objekten auf verschiedene
          Weisen und bieten noch mehr Flexibilität in der Objekterstellung und -Konfiguration.
          */
    }
}
