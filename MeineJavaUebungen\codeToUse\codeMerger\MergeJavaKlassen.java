package codeToUse.codeMerger;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class MergeJavaKlassen {

    public static void main(String[] args) throws IOException {
        String packageName = "de.example.mypackage";
        String outputFile = "MergeJavaKlassenOutput.java";

        System.out.println(packageName);
        // Alle Java-Dateien im Paket ermitteln
        File[] files = new File("C:/Users/<USER>/Java-Entwicklungsübungen/src/jders22_generic/anwendung3GenericInterface").listFiles();
        //File[] files = new File("C:/Users/<USER>/Java-Entwicklungsübungen/src/jders22_generic/anwendung3GenericInterface" + packageName.replace(".", "/") + "/").listFiles();

        // FileWriter zum Schreiben der Ausgabe erstellen
        FileWriter writer = new FileWriter(outputFile);

        // Header schreiben
        writer.write("package " + packageName + ";\n\n");

        // Für jede Java-Datei
        for (File file : files) {
            if (file.getName().endsWith(".java")) {
                // Code der Datei lesen
                String code = new String(Files.readAllBytes(Paths.get(file.getPath())));

                // Importe entfernen
                code = code.replaceFirst("import .*;", "");

                // Klasse in die Ausgabe schreiben
                writer.write(code + "\n\n");
            }
        }

        // FileWriter schließen
        writer.close();

        System.out.println("Die Datei " + outputFile + " wurde erstellt.");
    }
}
