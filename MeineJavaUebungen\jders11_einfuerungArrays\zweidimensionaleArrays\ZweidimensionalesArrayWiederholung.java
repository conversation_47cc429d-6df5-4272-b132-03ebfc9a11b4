package jders11_einfuerungArrays.zweidimensionaleArrays;

public class ZweidimensionalesArrayWiederholung {
    public static void main(String[] args) {

        char[][] tabelle = new char[8][14];

        for (int i = 0; i < tabelle.length; i++) {

            for (int j = 0; j < tabelle[i].length; j++) {

                if (i == 0 || i == (tabelle.length - 1)) {
                    tabelle[i][j] = '#';  // Setzt # in die erste Zeile von links bis rechts und mit|| (length - 1) auch in die letzte Zeile: i0,j0 und i0,j13 || i7,j0 bis i7,j13 = #

                } else if (j == 0 || j == tabelle[i].length - 1) {
                    tabelle[i][j] = '1';  // Setzt 1 auf die Spalten außen i1,j0 bis i7,j0; und i1,j13 bis i7,j13;

                } else if (i == j) {
                    tabelle[i][j] = '0';  // Setzt 0 auf der Diagonalen Linie i1,j1 bis i6,j6; und || j == (tabelle[i].length - i - 1) lässt Diagonale spiegeln i1,j13 bis i7,j13;

                } else {
                    tabelle[i][j] = '-';  // Setzt - für alle anderen Bedingungen
                }

            }
        }

        for (int i = 0; i < tabelle.length; i++) {

            for (int j = 0; j < tabelle[i].length; j++) {

                System.out.print(tabelle[i][j] + " ");  // Gibt den Wert der aktuellen Zelle aus, gefolgt von einem Leerzeichen

            }
            System.out.println();  // Wechselt zur nächsten Zeile nach dem Ausgeben aller Spalten in der aktuellen Zeile
        }

    }
}

