package ders15_StringManipulations;

/**
 * Diese Klasse demonstriert die Verwendung der charAt()-Methode in Java.
 *
 * Die charAt()-Methode ist eine String-Methode, die das Zeichen an einer bestimmten Position
 * (Index) in einem String zurückgibt. Der Index beginnt bei 0 für das erste Zeichen.
 *
 * Wichtige Eigenschaften:
 * - Der Index beginnt bei 0 (nullbasiert)
 * - Rückgabetyp ist char (ein einzelnes Zeichen)
 * - Wirft eine StringIndexOutOfBoundsException, wenn der Index ungültig ist
 */
public class C01_charAt {
    /**
     * Die Hauptmethode demonstriert verschiedene Anwendungen der charAt()-Methode.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Beispiel-String für die Demonstration
        String str = "Java ist Leben";
        System.out.println("Unser Beispiel-String: \"" + str + "\"");
        System.out.println("Länge des Strings: " + str.length() + " Zeichen");
        System.out.println();

        // Beispiel 1: Zugriff auf den ersten Buchstaben (Index 0)
        System.out.println("Beispiel 1: Ersten Buchstaben ausgeben (Index 0)");
        System.out.println("str.charAt(0): " + str.charAt(0));  // Ausgabe: J

        // Beispiel 2: Zugriff auf den siebten Buchstaben (Index 6)
        // Beachte: Leerzeichen werden auch als Zeichen gezählt!
        System.out.println("\nBeispiel 2: Siebten Buchstaben ausgeben (Index 6)");
        System.out.println("str.charAt(6): " + str.charAt(6));  // Ausgabe: t

        // Beispiel 3: Zugriff auf den letzten Buchstaben
        // Die Länge des Strings ist 14, aber der Index beginnt bei 0,
        // daher ist der Index des letzten Zeichens length()-1
        System.out.println("\nBeispiel 3: Letzten Buchstaben ausgeben");
        System.out.println("str.charAt(str.length()-1): " + str.charAt(str.length()-1));  // Ausgabe: n

        // Beispiel 4: Zugriff auf den drittletzten Buchstaben
        System.out.println("\nBeispiel 4: Drittletzten Buchstaben ausgeben");
        System.out.println("str.charAt(str.length()-3): " + str.charAt(str.length()-3));  // Ausgabe: b

        // Beispiel 5: Vermeidung von StringIndexOutOfBoundsException
        System.out.println("\nBeispiel 5: Vermeidung von StringIndexOutOfBoundsException");
        System.out.println("Vor dem Zugriff auf einen Index sollte man prüfen, ob er gültig ist.");
        System.out.println("Gültige Indizes für unseren String: 0 bis " + (str.length()-1));

        // Beispiel für einen ungültigen Index (auskommentiert, um Fehler zu vermeiden)
        // System.out.println(str.charAt(20));  // Würde eine StringIndexOutOfBoundsException werfen

        // Beispiel 6: Kombination mit anderen String-Methoden
        System.out.println("\nBeispiel 6: Kombination mit anderen String-Methoden");
        System.out.println("str.toLowerCase().charAt(3): " + str.toLowerCase().charAt(3));
        System.out.println("Erklärung: Erst wird der String in Kleinbuchstaben umgewandelt,");
        System.out.println("dann wird auf das Zeichen an Index 3 zugegriffen.");

        /*
         * WICHTIG: Reihenfolge der Methodenaufrufe
         *
         * Bei Verkettung von Methodenaufrufen ist die Reihenfolge entscheidend:
         * - str.toLowerCase().charAt(3) funktioniert, weil toLowerCase() einen String zurückgibt,
         *   auf dem dann charAt() aufgerufen werden kann.
         * - str.charAt(3).toLowerCase() würde NICHT funktionieren, weil charAt() ein char zurückgibt,
         *   und char hat keine toLowerCase()-Methode.
         */
    }
}
