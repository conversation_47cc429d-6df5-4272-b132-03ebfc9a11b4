package ders09_if_else_Satements;

import java.util.Scanner;

public class C04_ifElseStatements {
    public static void main(String[] args) {
        // Vom Nutzer Gewicht und Größe einlesen
        // Body Mass Index berechnen (kilo+10000 / Größe+Größe)
        // Body Index über 30 "Index im Übergewicht" ausgeben
        // 25-30 "Index mehr als normal" ausgeben
        // 20-25 "Index ist Normal" ausgeben
        // unter 20 "Index Bereich im Untergewicht" ausgeben

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie ihr Gewicht als Kilo an");
        double gewicht = scan.nextDouble();

        System.out.println("Bitte geben sie ihre Größe in cm an");
        double groesse = scan.nextDouble();

        double bmi = gewicht * 10000 / (groesse * groesse);

        if (bmi >= 30) System.out.println("Index " + bmi + " im Übergewicht");
        else if (bmi >= 25) System.out.println("Index " + bmi + " mehr als normal");
        else if (bmi >= 20) System.out.println("Index " + bmi + " ist normal");
        else if (bmi >= 25) System.out.println("Index " + bmi + " Bereich im Untergewicht");
    }
}
