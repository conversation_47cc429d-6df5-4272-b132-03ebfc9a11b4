package designPatterns.creational.prototype.prototypeWiederholungClone;

public class TestClone implements Cloneable {

    // Statischer Initialisierungsblock wird bei der Klasseninitialisierung ausgeführt.
    static {
        System.out.println("Ich bin ein statischer Initialisierungsblock ");
    }

    // Instanzinitialisierungsblock wird bei der Erstellung einer Instanz ausgeführt.
    {
        System.out.println("Ich bin ein Instanzinitialisierungsblock ");
    }

    // Konstruktor der Klasse
    public TestClone() {
        super(); // Aufruf des Konstruktors der Oberklasse (Object)
        System.out.println("Ich bin der Unterklassenkonstruktor ");
        setInstanceVariable("Ich bin die Instanzvariable ");
        System.out.println(getInstanceVariable() + getInstanceVariable().hashCode());
    }

    // Private Instanzvariable
    private String instanceVariable;

    // Getter-Methode für die Instanzvariable
    public String getInstanceVariable() {
        return instanceVariable;
    }

    // Setter-Methode für die Instanzvariable
    public void setInstanceVariable(String instanceVariable) {
        this.instanceVariable = instanceVariable;
    }

    // Überschriebene Methode zum Klonen des Objekts
    @Override
    public TestClone clone() {
        try {
            return (TestClone) super.clone(); // Tiefes Kopieren des Objekts
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return null;
    }
}
