package ajava_se.klassen.c01_innereMitgliedsklassen;

public class C04_Computer {
    /* Member Class das ist vergleichbar mit einem attribut was aber nicht statisch ist.
       Die innere Klasse kann zusätzlich auf alle Attribute (auch private!) der Instanz der äußeren Klasse zugreifen,
       zu der sie geh<PERSON> (hier z.B. auf 'hersteller').
       Diese Design-Entscheidung ist umstritten: Sie erleichtert die Modellierung enger "Teil-von"-Beziehungen
       (Komponenten, die intime Kenntnis des Ganzen brauchen), weicht aber die strikte Kapselung der äußeren Klasse auf,
       da die innere Klasse direkten Zugriff auf private Details erhält.
    */
    /* Nicht-statische innere Klassen, wie die Hauptspeicher-Klasse in diesem Beispiel, haben spezielle Eigenschaften:

       -Zugriff auf äußere Klasse: Eine Instanz der nicht-statischen inneren Klasse (z<PERSON> <PERSON><PERSON>iche<PERSON>)
        kann auf alle Eigenschaften der äußeren Klasse (z. B. <PERSON>) zugreifen!

       -Abhängigkeit von äußerer Klasse: Um eine Instanz der nicht-statischen inneren Klasse zu erstellen,
        muss eine Instanz der äußeren Klasse existieren. Dies unterscheidet sich von statischen inneren Klassen, #
        die unabhängig von Instanzen der äußeren Klasse sind.

       -Keine statischen Eigenschaften: Nicht-statische innere Elementklassen
        dürfen keine statischen Eigenschaften definieren, da ihre Existenz
        von einer Instanz der äußeren Klasse abhängt.

       -Verwendung in "besteht aus"-Beziehungen: Solche Elementklassen sind besonders nützlich,
        um "besteht aus"-Beziehungen zu realisieren, beispielsweise zur Umsetzung von Kompositionen.*/

    private String hersteller = "HP";
    private Hauptspeicher speicher = new Hauptspeicher();

    class Hauptspeicher {
        int groesse = 1024;

        void printHersteller(){
            System.out.println(C04_Computer.this.hersteller);
        }
    }


    public static void main(String[] args) {
        // Erstellen einer Instanz der äußeren Klasse C04_Computer.
        // Diese Instanz ('computer') ist notwendig, um:
        // 1. Auf nicht-statische Felder des Computers zuzugreifen (z.B. 'computer.speicher').
        // 2. Instanzen der nicht-statischen inneren Klasse 'Hauptspeicher' zu erstellen.
        C04_Computer computer = new C04_Computer();

        // --- Nutzung des 'eingebauten' Speicher-Feldes (Standard-RAM) ---
        System.out.println("--- Zugriff auf das 'speicher'-Feld (Standard-RAM) des Computers ---");
        // Das Feld 'speicher' (Typ Hauptspeicher) wurde bei der Erstellung von 'computer' automatisch initialisiert.
        // Wir greifen direkt darauf zu, um das Standard-RAM-Modul zu repräsentieren:
        System.out.println("Standard-Speicher Größe: " + computer.speicher.groesse);
        computer.speicher.printHersteller(); // Ruft die Methode des 'eingebauten' Speicherobjekts auf
        System.out.println("----------------------------------------------------");


        // --- Erstellung und Nutzung zusätzlicher Hauptspeicher-Objekte (zusätzliche RAM-Module) ---
        System.out.println("\n--- Erstellung zusätzlicher Hauptspeicher-Objekte (RAM-Module) ---");
        // Erstellen von zwei zusätzlichen, separaten RAM-Modulen für diesen Computer.
        // WICHTIG: Die Syntax 'computer.new Hauptspeicher()' wird verwendet, weil 'Hauptspeicher'
        // eine nicht-statische innere Klasse ist. Jede Instanz von 'Hauptspeicher' (wie h1, h2 oder das Objekt in 'speicher') muss
        // explizit mit einer Instanz der äußeren Klasse ('computer') verbunden sein.
        C04_Computer.Hauptspeicher h1 = computer.new Hauptspeicher();
        C04_Computer.Hauptspeicher h2 = computer.new Hauptspeicher();
        h2.groesse = 512;

        // Rufe die Methode printHersteller für die neuen Module auf
        h1.printHersteller();
        h2.printHersteller();

        // Gib die Groesse des Hauptspeichers aus
        System.out.println("Hauptspeicher1 Größe: " + h1.groesse);
        System.out.println("Hauptspeicher2 Größe: " + h2.groesse);

        // --- Berechnung der Gesamtgröße aller RAM-Module ---
        int gesamtGroesse = computer.speicher.groesse + h1.groesse + h2.groesse;
        System.out.println("\nGesamtgröße aller RAM-Module: " + gesamtGroesse + " MB"); // 2560 MB
    }
}
