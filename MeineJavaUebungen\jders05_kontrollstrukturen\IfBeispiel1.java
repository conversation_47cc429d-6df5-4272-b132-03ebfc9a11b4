package jders05_kontrollstrukturen;

public class IfBeispiel1 {
    public static void main(String[] args) {
        int a = 5, b = 6, c = 7;

        // <PERSON>berprüfung, ob a kleiner als b ist
        if (a < b) {
            // Wenn a kleiner als b ist, wird dieser Block ausgeführt

            // Überprüfung, ob b kleiner als c ist
            if (b < c) {
                // Wenn b kleiner als c ist, wird dieser Block ausgeführt

                a = c;  // Der Wert von a wird auf den Wert von c gesetzt
            }

            // Ausgabe der Summe von a, b und c
            System.out.println(a + b + c);  // 20

            // Ausgabe der Werte von a, b und c
            System.out.println("a= " + a + " b= " + b + " c= " + c);  // a= 7 b= 6 c= 7

            // Ausgabe der konkatenierten Werte von a, b und c
            System.out.println("" + a + b + c);  // 767
        }
    }

}
