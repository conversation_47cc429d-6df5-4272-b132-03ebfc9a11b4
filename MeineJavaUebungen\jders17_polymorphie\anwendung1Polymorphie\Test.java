package jders17_polymorphie.anwendung1Polymorphie;

public class Test {
    public static void main(String[] args) {

        // Wir wollen in unserer Anwendung die Liste von allen Personen, mit nur jeweils der Person Eigenschaften aus der Klasse


        Student student1 = new Student("<PERSON><PERSON><PERSON>", "Woll<PERSON>", 1999, "2383", null);
        // Person person1 = new Student("<PERSON>örn", "Wolle", 1999, "2383", null); so kommen wir nicht an die Klasse Student heran

        Dozent dozent1 = new Dozent("<PERSON>", "Helle", 1971, "Mathematik");
        // Person person2 = new Dozent("<PERSON>", "Helle", 1971, "Mathematik"); so kommen wir nicht an die Klasse Dozent heran

        Doktor doktor1 = new Doktor("<PERSON><PERSON>", "Luther", 1969, "Neurologie");

        /*Wir wollen von allen Personen die Liste Halten und erstellen eine ArrayList,
        dies hatten wir zuvor in anderen Anwendungen für alle Studenten getan*/

        /*
        // Wir fügen alle Personen der art Person des Objektes der Klasse Person in die Liste Personen.
        // Mit den Indizien Person ist Dozent und Student vom Typ (Person),
        // wenn sie in ihrer Klasse von Klasse-Person erben. Mit add in die Liste wird der Typ Person verlangt.
        // Die Liste enthält die Variablen der Klasse Person. (Zu Beachten dass die Personen 1&2 bereits in
        den Klassen Student, Dozent erstellt sind)
        ArrayList<Person> personen =new ArrayList<>();

        personen.add(dozent1);
        personen.add(student1);
        */

        // Über die static Methode personenInfosAusgeben
        personenInfosAusgebenStatic(dozent1);
        System.out.println();
        personenInfosAusgebenStatic(student1);
        System.out.println();
        personenInfosAusgebenStatic(doktor1);

        /*
        // Oder wir könnten ein Objekt der Klasse erstellen, um die Methode ohne static zu nutzen
        Test test = new Test();
        test.personenInfosAusgeben(student1);
        System.out.println();
        test.personenInfosAusgeben(dozent1);
        */

        // Über Person-bezogene Methode Ausgeben
        System.out.println("--------Ausgabe-zur-Person-bezogenen-Eigenschaften-aus-der-Unterklasse-----------");
        studentInfosAusgeben(student1);  // verlangt direkt Student
        dozentInfosAusgeben(dozent1);  // verlangt direkt Dozent
        System.out.println();
        doktorInfosAusgeben(doktor1);



    }

    // Methode gibt nur die Person bezogenen Eigenschaften zurück
    public void personenInfosAusgeben(Person person){

        System.out.println("Name : " + person.getVorname());
        System.out.println("Nachname : " +person.getNachname());
        System.out.println("Geburtsjahr : " + person.getGeburtsjahr());
    }



    // Static damit wir nicht ein Objekt der Klasse erstellen müssen
    public static void personenInfosAusgebenStatic(Person person) {

        System.out.println("Name : " + person.getVorname());
        System.out.println("Nachname : " + person.getNachname());
        System.out.println("Geburtsjahr : " + person.getGeburtsjahr());
    }

    public static void studentInfosAusgeben(Student stu){

        System.out.println("Name : " + (stu.getVorname()));
        System.out.println("Nachname : " + stu.getNachname());
        System.out.println("Geburtsjahr : " + stu.getGeburtsjahr());
        System.out.println("Studenten Nummer : " + stu.getStudentenNummer());
        System.out.println("Unterrichts Fächer : " + stu.getUnterrichtsFaecher());
    }

    public static void dozentInfosAusgeben(Dozent doz){
        /*
        System.out.println("Name : " + (doz.getVorname()));
        System.out.println("Nachname : " + doz.getNachname());
        System.out.println("Geburtsjahr : " + doz.getGeburtsjahr());
        System.out.println("Branche : " + doz.getBranche());
        */
        System.out.println(doz);
    }

    public static void doktorInfosAusgeben(Doktor dr) {

        System.out.println(dr);
    }

}
