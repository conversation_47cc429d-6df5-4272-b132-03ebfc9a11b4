package effectiveJava.effectiveJava03;

public class DefineSingleton {

    // Wir erstellen die Instance innerhalb der Klasse
    public static DefineSingleton ds = new DefineSingleton();

    private DefineSingleton() {
    }

    // Empfohlen wird das mit static Factory-Method aufzurufen, falls später noch etwas ge-updated wird
    public static DefineSingleton getInstance(){
        // Die static Methode Returned dann unsere Instance
        return ds;
    }
}
