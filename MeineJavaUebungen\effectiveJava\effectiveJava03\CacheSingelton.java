package effectiveJava.effectiveJava03;

import java.util.HashMap;

public enum CacheSingelton implements Cache {
    INSTANCE;

    private HashMap<Object, Object> map = new HashMap<Object, Object>();

    // Die put-Methode implementiert das Hinzufügen eines Schlüssel-Wert-Paares zur Map.
    @Override
    public void put(Object key, Object value) {

    }

    // Die get-Methode implementiert das Abrufen eines Wertes anhand eines Schlüssels aus der Map.
    @Override
    public Object get(Object key) {
        return null;

    }
}
