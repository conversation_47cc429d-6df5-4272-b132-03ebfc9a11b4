package com.example.mcpjavaapp.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.example.mcpjavaapp.model.CodeRequest;
import com.example.mcpjavaapp.model.PromptRequest;
import com.example.mcpjavaapp.service.OllamaService;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger; // Import Logger
import org.slf4j.LoggerFactory; // Import LoggerFactory

@RestController
@RequestMapping("/api/assistant")
@RequiredArgsConstructor
public class CodeAssistantController {

    private static final Logger log = LoggerFactory.getLogger(CodeAssistantController.class); // Logger Instanz
    private final OllamaService ollamaService;
    
    @PostMapping("/analyze")
    public String analyzeCode(@RequestBody CodeRequest request) {
        log.info("Anfrage an /api/assistant/analyze erhalten mit Code: {}", request.getCode());
        return ollamaService.analyzeCode(request.getCode());
    }
    
    @PostMapping("/document")
    public String generateDocumentation(@RequestBody CodeRequest request) {
        log.info("Anfrage an /api/assistant/document erhalten mit Code: {}", request.getCode());
        return ollamaService.generateDocumentation(request.getCode());
    }
    
    @PostMapping("/improve")
    public String improveCode(@RequestBody CodeRequest request) {
        log.info("Anfrage an /api/assistant/improve erhalten mit Code: {}", request.getCode());
        return ollamaService.improveCode(request.getCode());
    }
    
    @PostMapping("/generate")
    public String generateCompletion(@RequestBody PromptRequest request) {
        log.info("Anfrage an /api/assistant/generate erhalten mit Prompt: {}", request.getPrompt());
        return ollamaService.generateCompletion(request.getPrompt());
    }
}
