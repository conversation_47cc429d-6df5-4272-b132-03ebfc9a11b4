package oca_05_nullPointer;

public class NullPointer {
    public static void main(String[] args) {


        int x =0;       // dies ist ein bestehender Wert
        String s=null;  // dies ist kein wert, es ist nur der Kennzeichner der kennzeichnet das kein wert besteht!
/*

        if (x==s) System.out.println("Success");
        else System.out.println("Failure");

        NullPointer.java:atLine 10
        java: incomparable types: int and java.lang.String

*/

    }
}
