package jders06_03_einfuerungAnwendungZahlenAddieren;

public class AlleZahlenAddierenWhile {
    public static void main(String[] args) {

        // Gesamtzahl von allen Zahlen ausgeben

        int a = 0;
        int gesamt = 0;


        while (a <= 50) {
            gesamt = gesamt + a;
            a++;
          // System.out.println("a : " + a);
        }
        System.out.println("Gesamt : " + gesamt);
    }
}
