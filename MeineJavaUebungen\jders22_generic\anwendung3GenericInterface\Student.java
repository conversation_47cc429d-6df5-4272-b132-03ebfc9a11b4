package jders22_generic.anwendung3GenericInterface;

public class Student extends Person {

    private String studentenNummer;

    public Student() {
    }

    public Student(String vorname, String nachname, int geburtsjahr) {
        super(vorname, nachname, geburtsjahr);
    }

    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer) {
        super(vorname, nachname, geburtsjahr);
        this.studentenNummer = studentenNummer;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    @Override
    public String toString() {
        return "Student [studentenNummer=" + studentenNummer + ", Vorname=" + getVorname() +
                ", Nachname=" + getNachname() + ", Geburtsjahr=" + getGeburtsjahr() + "]";
    }


}
