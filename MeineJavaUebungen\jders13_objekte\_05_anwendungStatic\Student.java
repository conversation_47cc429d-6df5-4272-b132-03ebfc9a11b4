package jders13_objekte._05_anwendungStatic;

public class Student {
    // Für alle Objekte eine statische Gemeinsamkeit
    private static String schule = "34.Oberschule";

    private String vorname;

    private String nachname;

    private int gerburtsjahr;

    private String studentenNummer;

    public Student(){

    }

    public Student(String vorname, String nachname, int gerburtsjahr, String studentenNummer) {

        this.vorname = vorname;
        this.nachname = nachname;
        this.gerburtsjahr = gerburtsjahr;
        this.studentenNummer = studentenNummer;
    }

    public static void setSchule(String schule){
        // für alle Objekte geltende Methode wird die Klasse als verweis genommen anstelle von This
        // wir können hier wie sonst auch unsere Validierungslogik implementieren
        Student.schule = schule;
    }

    public static String getSchule(){

        return schule;
    }

    public void setVorname(String name) {

        this.vorname = vorname;
    }

    public String getVorname(){
        return vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGerburtsjahr() {
        return gerburtsjahr;
    }

    public void setGerburtsjahr(int gerburtsjahr) {
        this.gerburtsjahr = gerburtsjahr;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }
}
