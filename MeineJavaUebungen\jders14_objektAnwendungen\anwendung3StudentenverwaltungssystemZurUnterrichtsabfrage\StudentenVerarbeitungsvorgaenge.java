package jders14_objektAnwendungen.anwendung3StudentenverwaltungssystemZurUnterrichtsabfrage;

import java.util.ArrayList;

public class StudentenVerarbeitungsvorgaenge {
    // Diese Klasse dient nicht zu designs Zwecken der Anwendung,
    // sondern dazu wo Methoden als bekannt erklärt werden.
    // Dient zu Lernzwecken, um ein Objekt zwischen Methoden zu übergeben

    // wir werden student1 zu einer Methode in dieser Klasse senden.
    // Um dies zu können müssen wir bereits eine Instanz haben, um die Methode zu erreichen
    public void studentenInfosZeigen(Student stu) {
        // Ab jetzt ist in stu alles ein Datentyp vom Objekt Student,
        // und wir können alles in der Klasse über stu.erreichen

        System.out.println("Name : " + stu.getName());
        System.out.println("Nachname : " + stu.getNachname());
        System.out.println("Geburtsjahr : " + stu.getGeburtsjahr());

        // Weil wir getSchule() als static haben erreichen wir es nicht über die Instanz,
        // weil es nichts Spezifisches über eine Insanz ist und für alle aus der Klasse gilt
        System.out.println("Schule : " + Student.getSchule());
        System.out.println("Schüler Nummer : " + stu.getSchuhlNummer());
        System.out.println("Unterrichts Fächer : " + stu.getUnterrichtsFach());
    }

    /* Wir schicken eine Studenten-Instanz die für Überprüfung einer Liste:
        unterrichtsFaecher.add("Mathematik");
        unterrichtsFaecher.add("Physik");
        unterrichtsFaecher.add("Chemie");
        unterrichtsFaecher.add("Biologie");
        Teilnahme am unterrichtsFach in die Methode als abfrage
    */
    public void unterrichtsAbfrage(Student stud, String fach) {

        boolean zustand = true;
        // Wir ziehen die liste in die Methode
        ArrayList<String> uFaecher = stud.getUnterrichtsFach();
        // Jedes Fach der Liste wird in die Schleife zum Iterieren übergeben,
        // als zustand=true. Solange liste true ist, durchläuft die Schleife die Liste.
        for (String f : uFaecher) {
            // Ob f.listenWert = Fach ist, was wir im Parameter Senden
            if (f.equals(fach)) { // Wenn listen Wert gleich ist
                // Fach: ist enthalten = false
                zustand = false;
                // Ausgabe zur Instanz über stud.getName()
                System.out.println("Am Gesuchtem Fach nimmt " + '\'' + stud.getName() + '\''  + '\'' + stud.getNachname() + '\'' + " teil.");
                // Anschließend wird die Schleife für das jeweilige 'Fach' beendet
                break;
            }
            // Zustand = true
        }

        // Abfrage rutscht durch zum if, wenn Fach enthalten ist
        if (zustand) { // Iteration = true
            // Ausgabe zur Instanz über stud.getName()
            System.out.println("Am Gesuchtem Fach nimmt " + '\'' + stud.getName() + '\'' + '\'' + stud.getNachname() + " nicht teil !");

        }
    } // Liste wird in der Methode abgefragt bis Methode endet.

}
