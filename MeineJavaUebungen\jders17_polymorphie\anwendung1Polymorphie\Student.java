package jders17_polymorphie.anwendung1Polymorphie;

import java.util.ArrayList;

public class Student extends Person {

    private String studentenNummer;

    private ArrayList<String> unterrichtsFaecher;

    public Student() {

    }

    public Student(String vorname, String nachname, int geburtsjahr) {
        super(vorname, nachname, geburtsjahr);
    }

    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer, ArrayList<String> unterrichtsFaecher) {
        super(vorname, nachname, geburtsjahr);
        this.studentenNummer = studentenNummer;
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    public ArrayList<String> getUnterrichtsFaecher() {
        return unterrichtsFaecher;
    }

    public void setUnterrichtsFaecher(ArrayList<String> unterrichtsFaecher) {
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    @Override
    public String toString() {
        return "Student [Studenten Nummer : " + studentenNummer + ", Unterrichts Fächer " + unterrichtsFaecher +
                ", Name : " + getVorname() + ", Nachname : " + getNachname() + ", Geburtsjahr : " + getGeburtsjahr() + "]";
    }

}
