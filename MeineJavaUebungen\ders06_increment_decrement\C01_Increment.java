package ders06_increment_decrement;

/**
 * Diese Klasse demonstriert die Verwendung von Inkrement- und Dekrement-Operatoren in Java.
 *
 * Es werden verschiedene Arten der Wertänderung gezeigt:
 * - Direkte Zuweisung mit Operatoren (+=, -=)
 * - Post-Inkrement (zahl++)
 * - Pre-Inkrement (++zahl) - in dieser Klasse nicht gezeigt, aber in anderen Beispielen
 */
public class C01_Increment {
    /**
     * Die Hauptmethode demonstriert verschiedene Arten der Wertänderung einer Variablen.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Initialisierung einer Variablen
        int zahl = 10;
        System.out.println("Initialwert: " + zahl);  // 10

        // 1. Direkte Zuweisung mit Addition
        System.out.println("\n1. Direkte Zuweisung mit Addition:");
        System.out.println("zahl = zahl + 3: " + (zahl = zahl + 3));  // 13
        // Der Wert wurde geändert und bleibt geändert
        System.out.println("Neuer Wert von zahl: " + zahl);  // 13

        // 2. Verwendung des Zuweisungsoperators -=
        System.out.println("\n2. Verwendung des Zuweisungsoperators -=:");
        System.out.println("zahl -= 5: " + (zahl -= 5));  // 8
        // Der Wert wurde geändert und bleibt geändert
        System.out.println("Neuer Wert von zahl: " + zahl);  // 8

        // 3. Verwendung des Post-Inkrement-Operators ++
        System.out.println("\n3. Verwendung des Post-Inkrement-Operators ++:");
        System.out.println("zahl++: " + zahl++);  // 8
        // Der Wert wurde NACH der Ausgabe erhöht
        System.out.println("Neuer Wert von zahl: " + zahl);  // 9

        /*
         * WICHTIG: Unterschied zwischen Post-Inkrement und Pre-Inkrement
         *
         * Post-Inkrement (zahl++):
         * - Erst wird der aktuelle Wert verwendet (z.B. für Ausgabe oder Zuweisung)
         * - Dann wird der Wert um 1 erhöht
         *
         * Pre-Inkrement (++zahl):
         * - Erst wird der Wert um 1 erhöht
         * - Dann wird der neue Wert verwendet (z.B. für Ausgabe oder Zuweisung)
         *
         * Analog funktionieren Post-Dekrement (zahl--) und Pre-Dekrement (--zahl)
         */
    }
}
