package effectiveJava.effectiveJava03;

// Durch ein privaten Konstruktor die Singleton-Eigenschaft erzwingen
public class Item3Test {

    public static void main(String[] args) {

        /*
        // Kleine Demonstration des Problems und einer möglichen Lösung:
        // Da von public DefineSingleton hier wir mehr als eine Instance erstellen können, spricht dies gegen die Vorgehensweise von Singleton
        DefineSingleton ds1 = new DefineSingleton();
        DefineSingleton ds2 = new DefineSingleton();
        DefineSingleton ds3 = new DefineSingleton();
        DefineSingleton ds4 = new DefineSingleton();
        */

        /*
        // Wir erreichen unsere Instance, indem wir die Instance in der DefineSingleton Klasse erstellen
        DefineSingleton ds = DefineSingleton.ds;
        DefineSingleton ds2 = DefineSingleton.getInstance();

        // Indem wir die Hash-Codes anzeigen können wir sehen das es die ein und selbe Instance ist.
        System.out.println(ds.hashCode());  // 1808253012
        System.out.println(ds2.hashCode());  // 1808253012

        */


        // Zum vergleichen der Hashcodes im Falle von 2 Threads, ob alle nur eine Instanz haben
        new Thread(new Runnable() {
            @Override
            public void run() {
                // unser Grund für die nutzung Thread.sleep(1) ist das wir verursachen wollen beide Threads gleichzeitig zu starten
                try {
                    Thread.sleep(1);

                    // InterruptedException ist bei solchen versuchen durchaus sinnvoll und wichtig bei Threads
                } catch (InterruptedException e) {

                }

                // nun wollen wir die jeweiligen Instanzen kontrollieren
                System.out.println("HashCode of Lazy : " + LazyCache.getInstance().hashCode());
                System.out.println("HashCode of Lazy3 : " + LazyCache3.getInstance(4000).hashCode());
                System.out.println("HashCode of Lazy2 : " + LazyCache2.getInstance().hashCode());
                System.out.println("HashCode of Eager : " + EagerCache.getInstance().hashCode());
                System.out.println("HashCode of Enum : " + CacheSingelton.INSTANCE.hashCode());

            }
        }).start();

        new Thread(new Runnable() {
            @Override
            public void run() {

                System.out.println("HashCode of Lazy : " + LazyCache.getInstance().hashCode());
                System.out.println("HashCode of Lazy3 : " + LazyCache3.getInstance(4000).hashCode());
                System.out.println("HashCode of Lazy2 : " + LazyCache2.getInstance().hashCode());
                System.out.println("HashCode of Eager : " + EagerCache.getInstance().hashCode());
                System.out.println("HashCode of Enum : " + CacheSingelton.INSTANCE.hashCode());

            }
        }).start();

        // HashCode of Lazy : 405458889
        // HashCode of Lazy : 73596594
        // Bei Lazy ist unser Singelton prinzip nicht geschützt gegen zwei Threads oder mehr die gleichzeitig starten
        // HashCode of Eager : 384643772
        // HashCode of Eager : 384643772
        // HashCode of Enum : 178589774
        // HashCode of Enum : 178589774
        // Bei Eager und Enum ist unser Singelton geschützt
        // da beide mit einer einzigen Instanz Arbeiten, auch wenn zwei oder mehr Threads gleichzeitig starten

    }
}

       /* Es gibt mehrere Möglichkeiten, das Singleton-Muster in Java zu implementieren */