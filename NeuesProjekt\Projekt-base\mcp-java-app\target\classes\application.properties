# Server-Konfiguration
server.port=9090

# Spring AI Ollama-Konfiguration
spring.ai.ollama.base-url=http://localhost:11434
spring.ai.ollama.chat.options.model=phi4-mini-reasoning:3.8b
spring.ai.model.chat=ollama

# Zusätzliche Konfiguration für Ollama
spring.ai.ollama.chat.options.temperature=0.7
spring.ai.ollama.chat.options.top-k=40

# Automatisches Herunterladen von <PERSON> (wenn nötig)
spring.ai.ollama.init.pull-model-strategy=when_missing
spring.ai.ollama.init.timeout=5m
spring.ai.ollama.init.max-retries=1

# Server-Konfiguration
server.port=9090

# Logging-Konfiguration
logging.level.com.example.mcpjavaapp=DEBUG
