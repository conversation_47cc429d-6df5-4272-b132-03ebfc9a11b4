# Server-Konfiguration
server.port=8080

# Ollama-Konfiguration (Spring AI 1.0 GA Style)
spring.ai.ollama.base-url=http://localhost:11434
spring.ai.ollama.chat.options.model=phi4-mini-reasoning:3.8b
spring.ai.ollama.chat.options.temperature=0.7
spring.ai.ollama.chat.options.top-k=40

# MCP-Konfiguration
mcp.protocol.version=0.9.0
mcp.logging.enabled=true

# Fallback für direkte API-Kommunikation
ollama.base-url=http://localhost:11434
ollama.model=phi4-mini-reasoning:3.8b

# Logging-Konfiguration
logging.level.com.example.mcpjavaapp=DEBUG
logging.level.org.springframework.ai=DEBUG
logging.level.io.modelcontextprotocol=DEBUG
logging.level.root=INFO
