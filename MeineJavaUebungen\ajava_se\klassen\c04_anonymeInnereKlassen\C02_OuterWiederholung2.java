package ajava_se.klassen.c04_anonymeInnereKlassen;

/**
 * Diese Klasse demonstriert die Verwendung von anonymen inneren Klassen in Java.
 * <PERSON><PERSON> zei<PERSON>, wie man anonyme Klassen zur Implementierung des Runnable-Interfaces verwenden kann,
 * um Threads zu erstellen und auszuführen.
 */
public class C02_OuterWiederholung2 {

    /**
     * Diese Methode erstellt eine anonyme Klasse, die das Runnable-Interface implementiert,
     * und startet einen neuen Thread mit dieser Implementierung.
     */
    public void method() {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                String threadName = Thread.currentThread().getName(); // Thread-Namen abrufen
                System.out.println("Hello world! " + threadName);
            }
        };
        new Thread(runnable).start();
    }

    /**
     * Die Hauptmethode demonstriert zwei verschiedene Arten, anonyme Klassen zu verwenden:
     * 1. Direkt bei der Erstellung eines Thread-Objekts
     * 2. Durch Aufruf der method()-Methode einer anderen Klasse (C02_OuterClass)
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        Thread thread = new Thread(new Runnable(){
            @Override
            public void run() {
                String threadName = Thread.currentThread().getName();
                System.out.println("Merhabalar " + threadName);
            }
        });
        thread.start();
        C02_OuterClass outer = new C02_OuterClass();
        outer.method();
    }

    /* Erwartete Ausgabe:
     * Merhabalar Thread-0
     * Hello, world! Thread-1
     */
}
