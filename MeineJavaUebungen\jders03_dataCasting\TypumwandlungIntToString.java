package jders03_dataCasting;

public class TypumwandlungIntToString {
    public static void main(String[] args) {

        int zahl1 = 15;
        int zahl2 = 25;

        int gesamt = zahl1 + zahl2;

        System.out.println("Gesamt : " + gesamt);  // Gesamt : 40

        @SuppressWarnings("unused")     // für überraschende Warnungen: the value of local variable is not used. oder über der Main
        String zahl1Umwandlung = String.valueOf(zahl1);

        String zahl2Umwandlung = String.valueOf(zahl2);

        String gesamtString = zahl1Umwandlung + zahl2Umwandlung;


        System.out.println("Gesamt als String : " + gesamtString);  // Gesamt als String : 1525

        // double to String
        double d = 25.54;

        String doubleUmwandlung = String.valueOf(d);
        System.out.println("double Typumwandlung : " + doubleUmwandlung);  // double Typumwandlung : 25.54

    }
}
