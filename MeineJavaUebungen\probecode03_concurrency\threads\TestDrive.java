package probecode03_concurrency.threads;

public class TestDrive {
    /*
    In Java hat Concurrency neben Threads noch mit folgenden Themen zu tun:

    Locks: Locks sind ein Mechanismus, um den exklusiven Zugriff auf eine Ressource zu gewährleisten.
    Dies ist wichtig, um zu verhindern, dass zwei Threads gleichzeitig auf die gleiche Ressource zugreifen und sich gegenseitig blockieren.

    Condition Variables: Condition Variables sind ein Mechanismus, um Threads zu synchronisieren.
    Sie können verwendet werden, um Threads zu warten, bis eine bestimmte Bedingung erfüllt ist.

    Semaphores: Semaphores sind ein Mechanismus, um die Anzahl der Threads zu begrenzen, die gleichzeitig auf eine
    Ressource zugreifen können. Dies ist nützlich, um zu verhindern, dass zu viele Threads auf eine Ressource zugreifen und sie überlasten.

    ExecutorService: Der ExecutorService ist ein Framework, das die Ausführung von Threads verwaltet.
    Es bietet eine Reihe von Methoden, um Threads zu erstellen, zu starten und zu beenden.

    Concurrency Utilities: Die Concurrency Utilities sind eine Sammlung von Klassen und Interfaces,
    die die Entwicklung von concurrenten Programmen vereinfachen. Sie enthalten unter anderem Klassen für
    Threads, Locks, Condition Variables und Semaphores.

    Bei der Entwicklung concurrenter Programme ist es wichtig, die folgenden Grundsätze zu beachten:


    Mutual Exclusion: Der exklusive Zugriff auf eine Ressource muss gewährleistet sein. Dies kann durch Locks oder Semaphores erreicht werden.

    Progression: Threads sollten sich nicht gegenseitig blockieren. Dies kann durch die Verwendung von Condition Variables erreicht werden.

    Liveness: Threads sollten nicht endlos blockiert sein. Dies kann durch die Verwendung von Timeouts erreicht werden.

    Safety: Das Programm sollte frei von race conditions sein. Race conditions sind Fehler, die auftreten,
    wenn zwei Threads gleichzeitig auf die gleiche Ressource zugreifen und die Ergebnisse ihrer Zugriffe voneinander abhängen.

    Die Einhaltung dieser Grundsätze ist wichtig, um die Entwicklung robuster und zuverlässiger concurrenter Programme zu gewährleisten.*/

    private static class OrderProcessor extends Thread {
        @Override
        public void run() {
            print5();
        }

    }

    private static class PaymentProcessor implements Runnable {

        @Override
        public void run() {
            print5();
        }
    }


    public static void main(String[] args) {

        // print5();

        /*Thread t1 = new OrderProcessor();
        Thread t2 = new OrderProcessor();
        t1.start();
        t2.start();*/

        /*Thread t1 = new Thread(new PaymentProcessor(),"ahmet");
        Thread t2 = new Thread(new PaymentProcessor(),"mehmet");
        t1.start();
        t2.start();*/

        Thread t1 = new Thread(TestDrive::print5);
        Thread t2 = new Thread(TestDrive::print5);
        t1.start();
        t2.start();

    }

    private static void print5() {
        for (int i = 0; i < 5; i++) {
            // von Thread können wir viele static-Methoden erreichen
            System.out.println("Count " + i + ", Thread: " + Thread.currentThread().getName());
        }
    }


}
