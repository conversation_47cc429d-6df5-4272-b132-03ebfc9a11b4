package ajava_se.klassen.c01_innereMitgliedsklassen;

class C03_Aussen {
    int zahlOuterClass = 5;

    public void methodeA(int a) {
        // Die Variable b in methodeA ist nicht explizit als final deklariert, aber sie ist "effektiv final",
        // da sie nach der Initialisierung nicht mehr geändert wird. Daher kann InnerClassInMethodeA darauf zugreifen.
        int b = a;  // effektiv final -> Zugriff aus innerer Klasse möglich
        final String textA = "\nIn MethodeA von Outer-Klasse.";  // Zugriff möglich

        class InnerClassInMethodeA {
            public void gebeTextAus() {
                // innere Klasse kann nur auf lokale Konstanten in der MethodeA zugreifen
                // und auf Membervariablen und Methoden der äußeren Klasse
                System.out.println(textA);  // textA ist als final deklariert und lokal
                System.out.println(b);  // möglich, da 'b' effektiv final ist
                gebeZahlAus(zahlOuterClass);  // Membervariable und Methode der Außen-Klasse
            }
        }

        InnerClassInMethodeA icimA = new InnerClassInMethodeA();
        icimA.gebeTextAus();
    }

    public void gebeZahlAus(int a) {
        System.out.println(a);
    }
}
/*Die Beschränkung, dass innere Klassen nur auf final oder effektiv final lokale Variablen der umschließenden
Methode zugreifen können, gilt speziell für lokale Variablen und Parameter, die in der Methode deklariert sind,
in der die innere Klasse definiert ist.

In unserem Code ist die innere Klasse InnerClassInMethodeA innerhalb der Methode methodeA definiert.
Daher kann sie nur auf lokale Variablen und Parameter von methodeA zugreifen, die als final oder effektiv final deklariert sind.
In Java wird eine Variable als effektiv final bezeichnet, wenn sie nach der Initialisierung nicht mehr geändert wird,
auch wenn sie nicht explizit als final deklariert ist.

In unserem Code wird die Variable b einmal initialisiert und danach nicht mehr geändert.
Daher gilt sie als effektiv final. Das bedeutet, dass sie sich so verhält, als ob
sie mit dem Schlüsselwort final deklariert worden wäre, obwohl das in der Codezeile nicht explizit angegeben ist.

Dieses Konzept wurde in Java 8 eingeführt und ermöglicht es uns, auf Variablen in Lambda-Ausdrücken und
anonymen Klassen zuzugreifen, die nicht explizit als final deklariert sind, aber dennoch als effektiv final gelten.
*/
