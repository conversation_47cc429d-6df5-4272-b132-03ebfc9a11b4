package jders21_ordnerVerarbeitungen.anwendung2Scanner;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Scanner;

public class DateiLesen {
    public static void main(String[] args) {

        File datei = new File("C:/Users/<USER>/Desktop/Projeler/Dokument2.txt");

        try {

            Scanner sc = new Scanner(datei);


            // String erstesWort = sc.next();
            // String zweitesWort = sc.next();

            String ersteZeile = sc.nextLine();
            String zweiteZeile = sc.nextLine();

            // System.out.println(erstesWort);
            // System.out.println(zweitesWort);

            System.out.println(ersteZeile);
            System.out.println(zweiteZeile);

            sc.close();
        } catch (FileNotFoundException e) {
            // Ausgabe der ToString Methode von FileNotFoundException
            System.err.println("Fehler : " + e);
        }
    }
}
