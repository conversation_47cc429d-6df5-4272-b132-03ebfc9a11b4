package ders12_switchStatements;

import java.util.Scanner;

public class C04_switchStatements {
    public static void main(String[] args) {
        // Vom Nutzer den Monat als Zahl nehmen und die Jahreszeit Ausgeben.
        // In der Lösung gruppieren wir die Cases nach Jahreszeiten

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte Monat als Zahl eingeben");
        int zahlMonat = scan.nextInt();

        switch (zahlMonat) {
            case 12:
            case 1:
            case 2:
                System.out.println("Winter"); // code für alle untereinander stehendem cases gültig
                break;  //  Ende
            case 3:
            case 4:
            case 5:
                System.out.println("Frühling");
                break;  //  Ende
            case 6:
            case 7:
            case 8:
                System.out.println("Sommer");
                break;  //  Ende
            case 9:
            case 10:
            case 11:
                System.out.println("Herbst");
                break;
            default:            // Wie das letzte else, für alle werte was in kein case passend ist
                System.out.println("Ungültige Zahl für Monat");
                //  Ende
        }
    }
}
