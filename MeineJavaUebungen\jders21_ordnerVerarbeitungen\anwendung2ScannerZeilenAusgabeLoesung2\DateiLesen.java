package jders21_ordnerVerarbeitungen.anwendung2ScannerZeilenAusgabeLoesung2;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Scanner;

public class DateiLesen {
    public static void main(String[] args) {

        /* wenn ihre Datei Bsp. nur zwei Zeilen hat und Sie sicherstellen möchten, dass jede Zeile einzeln untereinander ausgegeben wird*/

        File datei = new File("C:/Users/<USER>/Desktop/Projeler/Dokument2.txt");

        try {

            Scanner sc = new Scanner(datei);

            for (int i = 0; i < 2; i++) { // Schleife läuft zweimal (für die zwei Zeilen)
                if (sc.hasNextLine()) {
                    System.out.println(sc.nextLine()); // Gib die nächste Zeile aus
                }
            }

        } catch (FileNotFoundException e) {
            System.err.println("Fehler : " + e);
        }
    }
}
