package designPatterns.creational.builder;

public class Haus {

    private String strasse;
    private String bezirk;
    private String stadt;

    private int gebaeudeJahr;
    private int zimmerZahl;
    private int badZahl;
    private int balkon;

    private boolean isMoebeliert;
    private boolean isParkplatz;
    private boolean isSpielplatz;
    private boolean isA<PERSON><PERSON><PERSON>platz;

    public Haus() {

    }

    public Haus(String strasse, String bezirk, String stadt, int gebaeudeJahr, int zimmerZahl, int badZahl, int balkon, boolean isMoebeliert, boolean isParkplatz, boolean isSpielplatz, boolean isAbstellplatz) {
        this.strasse = strasse;
        this.bezirk = bezirk;
        this.stadt = stadt;
        this.gebaeudeJahr = gebaeudeJahr;
        this.zimmerZahl = zimmerZahl;
        this.badZahl = badZahl;
        this.balkon = balkon;
        this.isMoebeliert = isMoebeliert;
        this.isParkplatz = isParkplatz;
        this.isSpielplatz = isSpielplatz;
        this.isAbstellplatz = isAbstellplatz;
    }

    public String getStrasse() {
        return strasse;
    }

    public void setStrasse(String strasse) {
        this.strasse = strasse;
    }

    public String getBezirk() {
        return bezirk;
    }

    public void setBezirk(String bezirk) {
        this.bezirk = bezirk;
    }

    public String getStadt() {
        return stadt;
    }

    public void setStadt(String stadt) {
        this.stadt = stadt;
    }

    public int getGebaeudeJahr() {
        return gebaeudeJahr;
    }

    public void setGebaeudeJahr(int gebaeudeJahr) {
        this.gebaeudeJahr = gebaeudeJahr;
    }

    public int getZimmerZahl() {
        return zimmerZahl;
    }

    public void setZimmerZahl(int zimmerZahl) {
        this.zimmerZahl = zimmerZahl;
    }

    public int getBadZahl() {
        return badZahl;
    }

    public void setBadZahl(int badZahl) {
        this.badZahl = badZahl;
    }

    public int getBalkon() {
        return balkon;
    }

    public void setBalkon(int balkon) {
        this.balkon = balkon;
    }

    public boolean isMoebeliert() {
        return isMoebeliert;
    }

    public void setMoebeliert(boolean moebeliert) {
        isMoebeliert = moebeliert;
    }

    public boolean isParkplatz() {
        return isParkplatz;
    }

    public void setParkplatz(boolean parkplatz) {
        isParkplatz = parkplatz;
    }

    public boolean isSpielplatz() {
        return isSpielplatz;
    }

    public void setSpielplatz(boolean spielplatz) {
        isSpielplatz = spielplatz;
    }

    public boolean isAbstellplatz() {
        return isAbstellplatz;
    }

    public void setAbstellplatz(boolean abstellplatz) {
        isAbstellplatz = abstellplatz;
    }

    @Override
    public String toString() {
        return "Haus{" +
                "strasse='" + strasse + '\'' +
                ", bezirk='" + bezirk + '\'' +
                ", stadt='" + stadt + '\'' +
                ", gebaedeJahr=" + gebaeudeJahr +
                ", zimmerZahl=" + zimmerZahl +
                ", badZahl=" + badZahl +
                ", balkon=" + balkon +
                ", isMoebeliert=" + isMoebeliert +
                ", isParkplatz=" + isParkplatz +
                ", isSpielplatz=" + isSpielplatz +
                ", isAbstellplatz=" + isAbstellplatz +
                '}';
    }
}
