package probecode03_concurrency.threads;

public class CounterAtomicTest {
    public static void main(String[] args) {



    CounterAtomic counterAtomic = new CounterAtomic();
    Thread t1 = new Thread(() -> {
        for (int i = 0; i < 100_000; i++) {
            counterAtomic.increment();
        }
        System.out.println("Count ist " + counterAtomic.getCount() + ";Thread ist " + Thread.currentThread().getName());

    });

    Thread t2 = new Thread(() -> {
        for (int i = 0; i < 100_000; i++) {
            counterAtomic.increment();
        }
        System.out.println("Count ist " + counterAtomic.getCount() + ";Thread ist " + Thread.currentThread().getName());

    });

        t1.start();
        t2.start();

    }
}
