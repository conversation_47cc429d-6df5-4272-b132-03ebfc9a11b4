package ders09_if_else_Satements;

import java.util.Scanner;

public class C05_ifElseStatements {
    public static void main(String[] args) {
        // Ganze <PERSON>ahl einlesen,
        // wenn Zahl durch 3 teilbar ist ausgeben,
        // wenn Zahl durch 5 teilbar ist ausgeben,
        // wenn Zahl durch 3 und 5 teilbar ist "super Zahl" ausgeben

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie eine Zahl ein");
        int eingabeZahl = scan.nextInt();

        // Abfrage sollte von der schmalen Seite beginnen die am meisten begrenzt ist
        // deshalb beginnen erst die Zahlen in der Schleife die am meisten begrenzt umfassen tun
        if (eingabeZahl % 3 == 0 && eingabeZahl % 5 == 0){
            System.out.println("Eingabe war eine super Zahl");
        } else if (eingabeZahl % 5 == 0) {
            System.out.println("Zahl ist durch 5 teilbar");     // Da die Zahlen die durch 5 und 3 teilen kann voneinander unabhängig sind, hat es keine Wirkung in der Reihenfolge
        } else if (eingabeZahl % 3 == 0) {
            System.out.println("Zahl ist durch 3 teilbar");
        }
    }
}
