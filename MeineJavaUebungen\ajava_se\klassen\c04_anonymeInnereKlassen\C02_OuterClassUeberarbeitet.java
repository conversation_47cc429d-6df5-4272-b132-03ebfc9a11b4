package ajava_se.klassen.c04_anonymeInnereKlassen;

public class C02_OuterClassUeberarbeitet {

    /* Anonyme Klassen - Überblick und Verwendung
     
     Anonyme Klassen sind eine spezielle Form von inneren Klassen in Java, die ohne expliziten Namen definiert werden.
     Sie werden in einem einzigen Ausdruck erstellt und können nicht wiederverwendet werden.
     
     Eigenschaften und Einschränkungen anonymer Klassen:
     
     - Deklaration: Anonyme Klassen werden innerhalb eines Ausdrucks definiert und haben keinen Namen.
       Sie können nur an der Stelle verwendet werden, an der sie definiert wurden.
     
     - Verwendungszweck: Anonyme Klassen eignen sich besonders für einmalige Implementierungen von
       Interfaces oder Erweiterungen von abstrakten Klassen.
     
     - Zugriff auf Variablen: Anonyme Klassen können auf finale oder effektiv finale Variablen
       der umgebenden Methode zugreifen.
     
     - Einschränkungen: Anonyme Klassen können nicht als public, private oder static deklariert werden.
       Sie können keine statischen Initialisierungsblöcke oder Interfaces enthalten.
     
     - Typische Anwendungsfälle: Event-Handler, Callbacks, Thread-Implementierungen und
       einmalige Implementierungen von Interfaces.
     
     Vorteile:
     - Kompakter Code für einmalige Implementierungen
     - Keine separate Klassendefinition notwendig
     
     Nachteile:
     - Nicht wiederverwendbar
     - Können bei übermäßiger Verwendung die Lesbarkeit des Codes verringern
     - Erhöhen die Komplexität bei umfangreichen Implementierungen
    */

    public void method() {
        // Deklaration einer anonymen Klasse, die das Runnable-Interface implementiert
        Runnable runnable = new Runnable() {
            // Implementierung der run()-Methode des Runnable-Interfaces
            @Override
            public void run() {
                String threadName = Thread.currentThread().getName();
                System.out.println("Hello, world! " + threadName);
            }
        };
        
        /* In diesem Beispiel implementiert die anonyme Klasse das Runnable-Interface.
           Die anonyme Klasse hat keinen Namen und wird direkt bei der Zuweisung an die
           Variable 'runnable' erstellt. */
           
        // Starten eines neuen Threads mit der anonymen Runnable-Implementierung
        new Thread(runnable).start();
    }

    public static void main(String[] args) {
        // Direktes Erstellen und Starten eines Threads mit einer anonymen Runnable-Implementierung
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String threadName = Thread.currentThread().getName();
                System.out.println("Merhabalar " + threadName);
            }
        });

        thread.start();
        
        // Erstellen einer Instanz von C02_OuterClass und Aufruf der method()-Methode
        C02_OuterClass outer = new C02_OuterClass();
        outer.method();
    }
}
