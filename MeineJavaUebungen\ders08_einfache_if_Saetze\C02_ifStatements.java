package ders08_einfache_if_Saetze;

import java.util.Scanner;

public class C02_ifStatements {
    public static void main(String[] args) {
        // Dieses Programm nimmt den ersten Buchstaben der Eingabe, und wenn es ein Monat mit dem Anfangsbuchstaben gibt wird er ausgegeben.

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie ein Buchstaben ein!");
        char ersterBuchstabe = scan.next().charAt(0);

        if (ersterBuchstabe == 'o' || ersterBuchstabe == 'O') {
            System.out.println("Oktober");
        }

        if (ersterBuchstabe == 's' || ersterBuchstabe == 'S') {
            System.out.println("September");
        }

        if (ersterBuchstabe == 'm' || ersterBuchstabe == 'M') {
            System.out.println("Mä<PERSON> , Mai");
        }

        if (ersterBuchstabe == 'n' || ersterBuchstabe == 'N') {
            System.out.println("November");
        }

        if (ersterBuchstabe == 'd' || ersterBuchstabe == 'D') {
            System.out.println("Dezember");
        }

        if (ersterBuchstabe == 'j' || ersterBuchstabe == 'J') {
            System.out.println("Januar , Juli , Juni");
        }

        if (ersterBuchstabe == 'f' || ersterBuchstabe == 'F') {
            System.out.println("Februar");
        }

        if (ersterBuchstabe == 'a' || ersterBuchstabe == 'A') {
            System.out.println("August , April");
        }
    }
}
