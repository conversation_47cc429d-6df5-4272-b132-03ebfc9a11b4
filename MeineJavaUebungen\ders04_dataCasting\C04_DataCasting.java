package ders04_dataCasting;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert das Typecasting bei der Division von ganzen Zahlen in Java.
 *
 * Wichtige Konzepte:
 * - Bei der Division zweier int-Werte wird das Ergebnis als int berechnet (Nachkommastellen werden abgeschnitten)
 * - Durch explizites Casting eines der Operanden zu double wird das Ergebnis als double berechnet
 * - Die Reihenfolge der Auswertung und das Casting sind entscheidend für das korrekte Ergebnis
 *
 * Das Programm zeigt drei verschiedene Ansätze zur Division:
 * 1. Division von int durch int (Ergebnis ist int)
 * 2. Zuweisung des int-Ergebnisses zu einer double-Variable (Nachkommastellen bereits verloren)
 * 3. Casting eines Operanden zu double vor der Division (korrektes Ergebnis mit Nachkommastellen)
 */
public class C04_DataCasting {
    /**
     * Die Hauptmethode demonstriert verschiedene Arten der Division und die Auswirkungen des Typcastings.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Aufgabe: Zwei ganze Zahlen vom Benutzer einlesen, sie teilen und das Ergebnis als double ausgeben

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scan = new Scanner(System.in);

        // Erste Zahl einlesen (Dividend)
        System.out.println("Geben Sie die zu teilende ganze Zahl ein (Dividend):");
        int a = scan.nextInt();

        // Zweite Zahl einlesen (Divisor)
        System.out.println("Geben Sie die teilende ganze Zahl ein (Divisor):");
        int b = scan.nextInt();

        // Methode 1: Division von int durch int
        // Bei der Division zweier int-Werte wird das Ergebnis als int berechnet (Nachkommastellen werden abgeschnitten)
        System.out.println("\nMethode 1: Division von int durch int");
        System.out.println("a/b = " + a + "/" + b + " = " + (a/b));
        System.out.println("Erklärung: Da beide Zahlen vom Typ int sind, wird das Ergebnis als int berechnet.");
        System.out.println("Nachkommastellen werden abgeschnitten, nicht gerundet!");

        // Methode 2: Zuweisung des int-Ergebnisses zu einer double-Variable
        // Das Ergebnis der Division wird zuerst als int berechnet und dann zu double konvertiert
        System.out.println("\nMethode 2: Zuweisung des int-Ergebnisses zu einer double-Variable");
        double ergebnis = a/b;
        System.out.println("double ergebnis = a/b = " + ergebnis);
        System.out.println("Erklärung: Java wertet zuerst die rechte Seite aus (a/b als int-Division)");
        System.out.println("und weist dann das Ergebnis der double-Variable zu. Die Nachkommastellen");
        System.out.println("sind zu diesem Zeitpunkt bereits verloren.");

        // Methode 3: Casting eines Operanden zu double vor der Division
        // Durch das Casting eines der Operanden zu double wird auch der andere Operand automatisch zu double konvertiert
        System.out.println("\nMethode 3: Casting eines Operanden zu double vor der Division");
        double richtigesErgebnis = (double) a / b;  // z.B. 15.0 / 4 = 3.75
        System.out.println("double richtigesErgebnis = (double) a / b = " + richtigesErgebnis);
        System.out.println("Erklärung: Durch das Casting von a zu double wird die gesamte Division");
        System.out.println("als double-Division durchgeführt, wodurch das korrekte Ergebnis mit");
        System.out.println("Nachkommastellen erhalten bleibt.");

        // Alternative Methode 4: Casting beider Operanden
        System.out.println("\nAlternative Methode 4: Casting beider Operanden");
        double alternativesErgebnis = (double) a / (double) b;
        System.out.println("double alternativesErgebnis = (double) a / (double) b = " + alternativesErgebnis);
        System.out.println("Erklärung: Diese Methode ist äquivalent zu Methode 3, da das Casting");
        System.out.println("eines Operanden ausreicht, um die gesamte Operation als double durchzuführen.");

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scan.close();

        /*
         * WICHTIGE REGELN FÜR TYPECASTING BEI ARITHMETISCHEN OPERATIONEN:
         *
         * 1. Bei arithmetischen Operationen zwischen verschiedenen Datentypen wird das Ergebnis
         *    im "größeren" Datentyp berechnet (Promotion).
         *
         * 2. Die Reihenfolge der Typpromotion ist:
         *    byte/short/char -> int -> long -> float -> double
         *
         * 3. Bei der Division ist besondere Vorsicht geboten:
         *    - int / int = int (Nachkommastellen werden abgeschnitten)
         *    - double / int = double (int wird zu double konvertiert)
         *    - int / double = double (int wird zu double konvertiert)
         *
         * 4. Das Casting muss VOR der Operation erfolgen, nicht danach!
         */
    }
}
