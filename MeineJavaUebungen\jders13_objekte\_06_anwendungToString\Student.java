package jders13_objekte._06_anwendungToString;

public class Student {
    // Für alle Objekte eine statische Gemeinsamkeit
    private static String schule = "34.Oberschule";

    private String vorname;

    private String nachname;

    private int gerburtsjahr;

    private String studentenNummer;

    public Student() {

    }

    public Student(String vorname, String nachname, int gerburtsjahr, String studentenNummer) {
    // Initialisierung der Instanzvariablen mit den übergebenen Werten
        this.vorname = vorname;
        this.nachname = nachname;
        this.gerburtsjahr = gerburtsjahr;
        this.studentenNummer = studentenNummer;
    }

    public static void setSchule(String schule) {
        // Statische Methode zum Setzen des Schulkonstantenwertes für alle Objekte
        // für alle Objekte geltende Methode wird die Klasse als verweis genommen anstelle von This
        // wir können hier wie sonst auch unsere Validierungslogik implementieren
        Student.schule = schule;
    }

    public static String getSchule() {

        return schule;
    }

    public void setVorname(String name) {

        this.vorname = vorname;
    }

    public String getVorname() {
        return vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGerburtsjahr() {
        return gerburtsjahr;
    }

    public void setGerburtsjahr(int gerburtsjahr) {
        this.gerburtsjahr = gerburtsjahr;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    private void infosAusgeben() {
        System.out.println("Name : " + vorname +
                ", Nachname : " + nachname +
                ", Studenten Nummer : " + studentenNummer +
                ", Geburtsjahr " + gerburtsjahr +
                ", Schule : " + schule);
    }

    public String getInfos() {

        return "Vorname : " + vorname +
                ", Nachname : " + nachname +
                ", Geburtsjahr : " + gerburtsjahr +
                ", Schule : " + schule +
                ", Studenten Nummer : " + studentenNummer;
    }

    @Override
    public String toString() {
        return "Vorname : " + vorname +
                ", Nachname : " + nachname +
                ", Geburtsjahr : " + gerburtsjahr +
                ", Schule : " + schule +
                ", Studenten Nummer : " + studentenNummer;
    }

}