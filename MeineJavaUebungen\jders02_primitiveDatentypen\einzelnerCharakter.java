package jders02_primitiveDatentypen;

public class einzelnerCharakter {
    public static void main(String[] args) {
        /*
        Typ      Speicher     gültiger Wertebereich      Beispiel     Beschreibung
        -----------------------------------------------------------------------
        char     16 Bit       0 bis 65535               'A'          Unicode-Zeichen

        Unicode:
        - Umfasst fast alle Schriftzeichen und Symbole der Welt
        - Jeder Wert entspricht einem Zeichen (z.B. 'A' = 65)
        - Ermöglicht die Darstellung internationaler Texte
         */

        // Beispiele für char-Variablen
        char plusZeichen = '+';
        char minusZeichen = '-';
        char euroZeichen = '\u20AC'; // Unicode für Euro

        // Ausgabe der Zeichen und ihrer Unicode-Werte
        System.out.println("Pluszeichen: " + plusZeichen + " (Unicode: " + (int)plusZeichen + ")");
        System.out.println("Minuszeichen: " + minusZeichen + " (Unicode: " + (int)minusZeichen + ")");
        System.out.println("Eurozeichen: " + euroZeichen + " (Unicode: " + (int)euroZeichen + ")");
    }
}
