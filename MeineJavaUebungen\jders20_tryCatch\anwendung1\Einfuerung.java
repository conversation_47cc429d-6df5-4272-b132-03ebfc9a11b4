package jders20_tryCatch.anwendung1;

public class Einfuerung {
    public static void main(String[] args) {

        // Trotz der Exception wird Hallo am Ende ausgegeben

        int[] zahlen = new int[5];

        // fügt in alle Felder 20 ein
        for (int i = 0; i < zahlen.length; i++) {

            zahlen[i] = 20;
        }

        try {
            // gibt alle Felder aus
            for (int i = 0; i < 6; i++) { // Schleife läuft nur, wenn es keine Ausnahme gibt

                System.out.println(zahlen[i]);
            }

        } catch (Exception e) {

            System.out.println("Fehler : " + e.getMessage());  // Fehler : 5
            System.out.println("e : Fehler : " + e);  // e : Fehler : java.lang.ArrayIndexOutOfBoundsException: 5
            System.out.println("Local Message : Fehler : " + e.getLocalizedMessage());  // Local Message : Fehler : 5
        }

        System.out.println("Hallo!!!");

    }
}
