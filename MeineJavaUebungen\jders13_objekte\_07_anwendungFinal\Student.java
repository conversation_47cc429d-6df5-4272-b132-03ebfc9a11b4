package jders13_objekte._07_anwendungFinal;

public class Student {
    // Final benötigt einen zugewiesenen Wert von begin an, daher könnten set Methoden eventuell im Design wegfallen,
    // und get Methode für final erstellen wir ebenfalls in diesem Design nicht nutzen und wir werden es als public darstellen.

    /* Set-Methoden sind weiterhin relevant, auch wenn ein Attribut mit final markiert ist.
     * Obwohl der Wert des Attributs nicht geändert werden kann,
     * kann die Set-Methode verwendet werden, um den Wert bei der Objekterzeugung oder zu einem späteren
     * Zeitpunkt festzulegen. Obwohl das Schlüsselwort final bedeutet, dass der Wert einer Variable nicht geändert werden kann,
     * ist es immer noch sinnvoll, eine Get-Methode zu haben. Eine Get-Methode ermöglicht den Zugriff
     * auf den unveränderlichen Wert der Variable, sodass er von anderen Teilen des Codes verwendet werden kann.
     * Die Sichtbarkeit einer Methode (z. B. public, private, usw.) ist unabhängig davon, ob ein Attribut als final markiert ist
     * oder nicht. Die Sichtbarkeit einer Methode wird basierend auf den Anforderungen des Designs und der Funktionalität festgelegt,
     * nicht auf der Verwendung von final.*/

    /* Der genaue Unterschied zwischen der Verwendung von static und ohne static liegt
     * in der Art und Weise, wie das Attribut oder die Methode mit der Klasse assoziiert ist.
     * static:
     * Ein static-Attribut oder eine static-Methode gehört zur Klasse selbst, nicht zu einer bestimmten Instanz der Klasse.
     * Es kann auf die Klasse selbst bezogen werden, ohne ein Objekt der Klasse erstellen zu müssen.
     * Es wird über den Klassennamen aufgerufen, gefolgt von der Verwendung des Punktoperators (.).
     * Alle Instanzen der Klasse teilen sich den gleichen Wert oder die gleiche Methode.
     *
     * Ohne static:
     * Ein nicht-static-Attribut oder eine nicht-static-Methode gehört zu einer bestimmten Instanz der Klasse.
     * Es kann nur über ein Objekt der Klasse aufgerufen werden, das zuerst erstellt werden muss.
     * Es wird über eine Referenz auf das Objekt aufgerufen, gefolgt von der Verwendung des Punktoperators (.).
     * Jede Instanz der Klasse hat ihren eigenen Wert oder ihre eigene Methode.
     *
     * Die Wahl zwischen static und ohne static hängt von der Art der Daten oder Aktionen ab,
     * die Sie in Ihrer Klasse darstellen möchten. Wenn Sie ein Attribut oder eine Methode haben,
     * die für alle Instanzen der Klasse gleich ist und keinen spezifischen Instanz-zugriff erfordert,
     * können Sie static verwenden. Andernfalls, wenn das Attribut oder die Methode Instanz-spezifisch ist
     * und für jede Instanz unterschiedlich sein kann, verwenden Sie kein static.
     */
    public static final String SCHULE = "34.Oberschule"; //  Um der Lesbarkeit des Codes zu dienen, schreiben wir konstanten mit oder nur in Großbuchstaben .
    // public final String schule = "34.Oberschule";

    private String vorname;

    private String nachname;

    private int gerburtsjahr;

    private String studentenNummer;

    public Student() {

    }

    public Student(String vorname, String nachname, int gerburtsjahr, String studentenNummer) {

        this.vorname = vorname;
        this.nachname = nachname;
        this.gerburtsjahr = gerburtsjahr;
        this.studentenNummer = studentenNummer;
    }

    public void setVorname(String name) {

        this.vorname = vorname;
    }

    public String getVorname() {
        return vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGerburtsjahr() {
        return gerburtsjahr;
    }

    public void setGerburtsjahr(int gerburtsjahr) {
        this.gerburtsjahr = gerburtsjahr;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    private void infosAusgeben() {
        System.out.println("Name : " + vorname +
                ", Nachname : " + nachname +
                ", Studenten Nummer : " + studentenNummer +
                ", Geburtsjahr " + gerburtsjahr +
                ", Schule : " + SCHULE);
    }

    public String getInfos() {

        return "Vorname : " + vorname +
                ", Nachname : " + nachname +
                ", Geburtsjahr : " + gerburtsjahr +
                ", Schule : " + SCHULE +
                ", Studenten Nummer" + studentenNummer;
    }

    @Override
    public String toString() {
        return "Vorname : " + vorname +
                ", Nachname : " + nachname +
                ", Geburtsjahr : " + gerburtsjahr +
                ", Schule : " + SCHULE +
                ", Studenten Nummer " + studentenNummer;
    }
}
