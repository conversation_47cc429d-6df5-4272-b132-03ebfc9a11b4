package com.example.mcpjavaapp.controller;

import com.example.mcpjavaapp.service.SimpleOllamaService;
import com.example.mcpjavaapp.service.SpringAIOllamaService;
import com.example.mcpjavaapp.tools.DocumentationTools;
import com.example.mcpjavaapp.config.McpConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.HashMap;

/**
 * Erweiterte Controller-Implementierung mit Spring AI 1.0 GA und MCP SDK 0.9.0 Style
 */
@RestController
@RequestMapping("/api/v2")
public class EnhancedMcpController {

    private static final Logger log = LoggerFactory.getLogger(EnhancedMcpController.class);

    private final SimpleOllamaService simpleOllamaService;
    private final SpringAIOllamaService springAIService;
    private final DocumentationTools documentationTools;
    private final McpConfig.McpSessionSimulator mcpSession;

    @Autowired
    public EnhancedMcpController(
            SimpleOllamaService simpleOllamaService,
            SpringAIOllamaService springAIService,
            DocumentationTools documentationTools,
            McpConfig.McpSessionSimulator mcpSession) {
        
        this.simpleOllamaService = simpleOllamaService;
        this.springAIService = springAIService;
        this.documentationTools = documentationTools;
        this.mcpSession = mcpSession;
        
        log.info("EnhancedMcpController initialisiert mit MCP Version: {}", mcpSession.getProtocolVersion());
    }

    /**
     * Test-Endpunkt für die erweiterte API
     */
    @GetMapping("/test")
    public Map<String, Object> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "Die erweiterte MCP-Anwendung läuft erfolgreich!");
        response.put("mcpVersion", mcpSession.getProtocolVersion());
        response.put("springAI", "1.0 GA Style");
        response.put("features", new String[]{"Direct API", "Spring AI Style", "MCP Tools", "Enhanced Logging"});
        
        mcpSession.log("Test-Endpunkt aufgerufen");
        return response;
    }

    /**
     * Direkte API-Kommunikation (bewährte Methode)
     */
    @GetMapping("/direct/generate")
    public String generateDirect(@RequestParam String prompt) {
        mcpSession.log("Direkte API-Anfrage: " + prompt);
        return simpleOllamaService.generateCompletion(prompt);
    }

    /**
     * Spring AI 1.0 GA Style Kommunikation
     */
    @GetMapping("/springai/generate")
    public String generateSpringAI(@RequestParam String prompt) {
        mcpSession.log("Spring AI Style Anfrage: " + prompt);
        return springAIService.generateCompletion(prompt);
    }

    /**
     * MCP Tool: Code-Analyse mit @Tool-Annotation Style
     */
    @PostMapping("/mcp/analyze")
    public Map<String, Object> analyzeCodeMcp(@RequestParam String code) {
        mcpSession.log("MCP Tool: Code-Analyse gestartet");
        
        Map<String, Object> response = new HashMap<>();
        response.put("tool", "code_analyzer");
        response.put("mcpVersion", mcpSession.getProtocolVersion());
        response.put("result", documentationTools.analyzeCode(code));
        
        return response;
    }

    /**
     * MCP Tool: Dokumentationsgenerierung mit @Tool-Annotation Style
     */
    @PostMapping("/mcp/document")
    public Map<String, Object> generateDocumentationMcp(
            @RequestParam String code,
            @RequestParam(defaultValue = "JavaDoc") String format) {
        
        mcpSession.log("MCP Tool: Dokumentationsgenerierung gestartet");
        
        Map<String, Object> response = new HashMap<>();
        response.put("tool", "documentation_generator");
        response.put("mcpVersion", mcpSession.getProtocolVersion());
        response.put("format", format);
        response.put("result", documentationTools.generateDocumentation(code, format));
        
        return response;
    }

    /**
     * MCP Tool: Code-Verbesserung mit @Tool-Annotation Style
     */
    @PostMapping("/mcp/improve")
    public Map<String, Object> improveCodeMcp(@RequestParam String code) {
        mcpSession.log("MCP Tool: Code-Verbesserung gestartet");
        
        Map<String, Object> response = new HashMap<>();
        response.put("tool", "code_improver");
        response.put("mcpVersion", mcpSession.getProtocolVersion());
        response.put("result", documentationTools.improveCode(code));
        
        return response;
    }

    /**
     * Vergleich zwischen direkter API und Spring AI Style
     */
    @PostMapping("/compare")
    public Map<String, Object> compareApproaches(@RequestParam String code) {
        mcpSession.log("Vergleich zwischen direkter API und Spring AI Style");
        
        Map<String, Object> response = new HashMap<>();
        
        // Direkte API
        String directResult = simpleOllamaService.analyzeCode(code);
        
        // Spring AI Style
        String springAIResult = springAIService.analyzeCodeWithAI(code);
        
        response.put("directAPI", directResult);
        response.put("springAI", springAIResult);
        response.put("mcpVersion", mcpSession.getProtocolVersion());
        response.put("comparison", "Beide Ansätze verwenden dasselbe Ollama-Modell, aber unterschiedliche API-Styles");
        
        return response;
    }

    /**
     * Erweiterte Prompt-Verarbeitung mit System-Kontext
     */
    @PostMapping("/springai/enhanced")
    public Map<String, Object> enhancedGeneration(
            @RequestParam String systemPrompt,
            @RequestParam String userPrompt) {
        
        mcpSession.log("Spring AI Enhanced: System + User Prompt");
        
        Map<String, Object> response = new HashMap<>();
        response.put("systemPrompt", systemPrompt);
        response.put("userPrompt", userPrompt);
        response.put("result", springAIService.generateWithSystemContext(systemPrompt, userPrompt));
        response.put("style", "Spring AI 1.0 GA with System Context");
        
        return response;
    }

    /**
     * MCP Session-Informationen
     */
    @GetMapping("/mcp/session")
    public Map<String, Object> getMcpSessionInfo() {
        Map<String, Object> response = new HashMap<>();
        response.put("protocolVersion", mcpSession.getProtocolVersion());
        response.put("loggingEnabled", mcpSession.isLoggingEnabled());
        response.put("status", "MCP Session aktiv");
        response.put("features", new String[]{"@Tool Annotations", "Enhanced Logging", "Protocol 0.9.0"});
        
        return response;
    }
}
