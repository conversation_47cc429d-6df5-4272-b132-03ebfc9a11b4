package jders19_interface.anwendung2;

public class Kreis implements Form {

    /*
     *Inhalt: Math.PI * radius * radius;
     *Umfang: 2 * Math.PI * radius;
     */

    // Constante großgeschrieben und ohne get oder set
    private final double PI = 3.14;

    private double radius;

    public Kreis (){

    }

    public Kreis(double radius) {
        this.radius = radius;
    }

    public double getRadius() {
        return radius;
    }

    public void setRadius(double radius) {
        this.radius = radius;
    }

    @Override
    public double umfangBerechnen() {
        return 2 * Math.PI * radius;
    }

    @Override
    public double inhaltBerchnen() {
        return Math.PI * radius * radius;
    }
}
