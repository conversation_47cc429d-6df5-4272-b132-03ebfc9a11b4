package jders22_generic.anwendung1GenericKlassen;

import java.util.ArrayList;

public class Verarbeitungsvorgaenge<T> {

    // Für unser gesendetes Objekt Student und Dozent wird die ArrayList erstellt
    private ArrayList<T> liste = new ArrayList<>();

    public void speicher(T t) {

        liste.add(t);
    }

    public void loeschen(T t) {

        liste.remove(t);
    }

    public void auflisten() {

        for (T t : liste) {
            // wir können nur mit der toString Methode ausgeben
            System.out.println(t);

            // an der Stelle können wir nicht es nach Wunsch formatiert ausgeben, da t.getName nicht zu erreichen ist.
            // Jedoch könnte die Liste von anderswo geladen werden, um sie nach Bedürfnissen zu bearbeiten
        }
    }

    public ArrayList<T> getListe() {
        return liste;
    }
}
