package jders06_01_einfuerungAnwendungDurchschnitt;
import java.util.Scanner;

public class EinfuerungsAnwendung5 {
    public static void main(String[] args) {
        /*
        Dieser Code überprüft die Eingaben auf Gültigkeit und gibt die entsprechende Fehlermeldung aus,
        falls sie ungültig sind. Andernfalls wird der Durchschnitt berechnet und die Buchstabennote ausgegeben.
        Der Code ist kurz, einfach zu lesen und effektiv.
        */
        Scanner sc = new Scanner(System.in);

        System.out.print("Geben Sie Ihre Visum Note ein: ");
        double visum = sc.nextDouble();

        System.out.print("Geben Sie Ihre Final Note ein: ");
        double finall = sc.nextDouble();

        if (visum < 0 || finall < 0 || visum > 100 || finall > 100) {
            System.out.println("Die Eingabewerte müssen zwischen 0 und 100 liegen!");
        } else {
            double durchschnitt = visum * 0.4 + finall * 0.6;
            String buchstabe;
            if (durchschnitt >= 90) {
                buchstabe = "AA";
            } else if (durchschnitt >= 80) {
                buchstabe = "BB";
            } else if (durchschnitt >= 70) {
                buchstabe = "CC";
            } else if (durchschnitt >= 60) {
                buchstabe = "DD";
            } else {
                buchstabe = "FF";
            }
            System.out.println("Ihr Durchschnitt: " + durchschnitt + " - Buchstabennote: " + buchstabe);
        }
    }
}