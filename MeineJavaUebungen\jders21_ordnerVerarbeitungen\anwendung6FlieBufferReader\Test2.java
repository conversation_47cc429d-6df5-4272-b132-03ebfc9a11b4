package jders21_ordnerVerarbeitungen.anwendung6FlieBufferReader;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

public class Test2 {

    public static void main(String[] args) {

        // Wir deklarieren und initialisieren den bufferedReader außerhalb des try-Blocks.
        // Das ist wichtig, weil wir sicherstellen möchten, dass die Variable im finally-Block, unabhängig davon, ob eine Ausnahme auftritt oder nicht,
        // verwendet werden kann. Wenn der BufferedReader im try-Block jedoch nicht ordnungsgemäß initialisiert wurde
        // (zum Beispiel wenn die Datei nicht gefunden wurde und der BufferedReader somit null ist),
        // wird ein Fehler auftreten, wenn wir versuchen, bufferedReader.close() im finally-Block aufzurufen.
        // Dies liegt da<PERSON>, dass wir nicht auf null-Werte zugreifen können, da sie keine gültigen Objekte sind.
        //
        // Daher ist es wichtig, den BufferedReader mit null zu initialisieren, damit wir im finally-Block überprüfen können,
        // ob er ordnungsgemäß initialisiert wurde, bevor wir versuchen, ihn zu schließen.
        // Dies verhindert, dass wir auf null-Referenzen zugreifen und Ausnahmen auslösen,
        // wenn keine gültige Instanz des BufferedReader erstellt wurde.
        BufferedReader bufferedReader = null;

        try {
            // hier durch wird eine Verbindung zur Datei geöffnet, die gelesen werden soll.
            FileReader fileReader = new FileReader("C:/Users/<USER>/Desktop/Projeler/fileRead.txt");

            bufferedReader = new BufferedReader(fileReader);

            String zeile;
            while ((zeile = bufferedReader.readLine()) != null) {

                System.out.println(zeile);
            }


            // Ausnahme zur Pfadangabe und Ausnahmen über bufferedReader.read kann über IOException abgefangen werden
        } catch (IOException e) {

            System.out.println("Fehler : " + e);


        } finally {

            // Für bekannt gegebene Vorgänge, die wir öffnen und schließen,
            // werden wir im finally block sie in allen fällen am Ende dennoch schließen.
            // Um sie im finally block Aufrufen zu können, müssen sie außerhalb des try blocks bekannt geben

            try {

                // Da es bereits bei null geschlossen ist führen wir die Abfrage ein
                if (bufferedReader != null) {

                    bufferedReader.close();

                }

         /*       // try catch auch ineinander verschachtelt möglich
                try {

                }catch (Exception e2){

                }
         */

            } catch (IOException e) {
                System.err.println("Beim Schließen des BufferedReader´s ist ein Fehler aufgetreten");
                System.err.println("Fehler : " + e);
            }
        }


    }
}
