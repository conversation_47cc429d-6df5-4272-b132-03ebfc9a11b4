package jders12_stringMethoden;

//  Die Klasse VerarbeitungsTest wird definiert.
public class VerarbeitungsTestStringUndStringBuffer {

    /*Die Verwendung des StringBuffer oder des moderneren StringBuilder anstelle des regulären String
    ist in Szenarien empfehlenswert, in denen viele Veränderungen an einem Text vorgenommen werden sollen,
    da dies die Effizienz und die Leistung verbessert.*/

    // Methode, die einen String zur Verarbeitung erstellt.
    // Bei der Konkatenation von Strings mit dem +-Operator wird jedes Mal ein neues
    // String-Objekt erstellt, was zu einem erhöhten Speicherverbrauch führen kann
    public static String StringZurVerarbeitung() {

        // Initialisierung eines Strings mit dem Wert "Java"
        String t = "Java";

        // Schleife, die 100 Mal durchlaufen wird
        for (int i = 0; i < 10000; i++) {

            // String-Konkatenation: Der Wert des Strings "t" wird um den String "Programmieren" erweitert
            t = t + "Programmieren"; // Jedes wird mal ein neues String-Objekt erstellt, was zu einem erhöhten Speicherverbrauch führen kann.
        }

        // Rückgabe des resultierenden Strings
        return t;
    }

    // Methode, die einen StringBuffer zur Verarbeitung erstellt
    // Der StringBuffer hingegen erlaubt das Hinzufügen von Text ohne
    // die Notwendigkeit, neue Objekte zu erstellen, was die Leistung
    // verbessert beziehungsweise intern den Speicherplatz effizienter verwaltet
    public static String StringBufferZurVerarbeitung() {

        // Initialisierung eines StringBuffer mit dem Wert "Java"
        StringBuffer sb = new StringBuffer("Java");

        // Schleife, die 100 Mal durchlaufen wird
        for (int i = 0; i < 30000; i++) {

            // Verwendung der append()-Methode des StringBuffer-Objekts, um den Wert "Programmieren" hinzuzufügen
            sb.append("Programmieren");
        }

        // Rückgabe des resultierenden Strings durch Aufruf der toString()-Methode des StringBuffer-Objekts
        return sb.toString();
    }

    public static void main(String[] args) {
        // Speichert die aktuelle Systemzeit in Millisekunden
        long startZeit = System.currentTimeMillis();

        // Aufruf der Methode StringZurVerarbeitung(), um den String zu verarbeiten
        StringZurVerarbeitung();

        // Berechnet die Zeitdauer, indem die aktuelle Systemzeit minus Startzeit berechnet wird,
        // und gibt sie zusammen mit dem Text "String" aus
        System.out.println("String : " + (System.currentTimeMillis() - startZeit) + "ms");

        // Aktualisiert die Startzeit mit der aktuellen Systemzeit
        startZeit = System.currentTimeMillis();

        // Aufruf der Methode StringBufferZurVerarbeitung(), um den StringBuffer zu verarbeiten
        StringBufferZurVerarbeitung();

        // Berechnet die Zeitdauer, indem die aktuelle Systemzeit minus Startzeit berechnet wird,
        // und gibt sie zusammen mit dem Text "StringBuffer" aus
        System.out.println("StringBuffer : " + (System.currentTimeMillis() - startZeit) + "ms");
    }

}

