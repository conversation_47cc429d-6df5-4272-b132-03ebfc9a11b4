package jders11_einfuerungArrays.arrayList;

import java.util.ArrayList;

public class AnwendungListeVonListeUndListe {
    public static void main(String[] args) {

        /* Die Reihenfolge der Elemente und ihre Indizes bleiben erhalten,
         * solange keine Operationen wie das Einfügen oder Löschen von Elementen stattfinden,
         * die die Reihenfolge ändern.
         * Es ist wichtig zu beachten, dass die Elemente in der ArrayList über den Index zugänglich sind,
         * d. h. können wir auf ein Element anhand seines Index zugreifen, indem wir die get()-Methode verwenden,
         *
         *z. B. list.get(0) gibt das Element an Index 0 zurück.
         * Die Elemente aus der cloudStudents-Liste haben somit einen niedrigeren Index
         * als die Elemente aus der javaStudents-Liste, da sie zuerst hinzugefügt wurden.
         * */

        // Erstellen der ArrayLists für den Kurs und die einzelnen Gruppen
        ArrayList<String> courseStudentList = new ArrayList<>();
        ArrayList<String> javaStudents = new ArrayList<>();
        ArrayList<String> cloudStudents = new ArrayList<>();

        // Hinzufügen von Studenten zur Java-Gruppe
        javaStudents.add("Sinai");
        javaStudents.add("Dalvik");
        javaStudents.add("Caren");

        // Hinzufügen von Studenten zur Cloud-Gruppe
        cloudStudents.add("Hans");
        cloudStudents.add("Kunz");
        cloudStudents.add("Franz");

        // Hinzufügen der Studenten aus den Gruppen zur Kursliste
        courseStudentList.addAll(cloudStudents);  // Bedenken der Reihenfolge für Elemente und ihre Indizes
        courseStudentList.addAll(javaStudents);  // Wir könnten auch an einem bestimmten Index beginnen


        System.out.println("Liste der Studenten von allen Kursen:");
        // Ausgabe der Liste der Studenten im Kurs
        for (String stud : courseStudentList) {
            System.out.println(stud);
        }


        System.out.println();
        System.out.println("Liste der Java-Studenten:");

        /*for (String stud : javaStudents) {
         *     System.out.println(stud);
         *  }
         *  Beide Ansätze führen zur Ausgabe der Elemente der javaStudents-Liste,
         *  aber sie unterscheiden sich in der Art und Weise, wie die Schleife durchlaufen wird
         *  und auf die Elemente zugegriffen wird. Die "foreach"-Schleife ist in der Regel einfacher
         *  und lesbarer, wenn man nur auf die Elemente zugreifen möchte, während die herkömmliche
         *  for-Schleife nützlich ist, wenn man zusätzlich den Index benötigt.
         *
         * Wenn man sich dafür entscheidet, die "foreach"-Schleife zu verwenden und den Index
         * nicht explizit zu verwalten, hat dies keine direkten Auswirkungen auf die Verwaltung der Elemente in der Liste.
         * Die Verwaltung der Elemente in der Liste bleibt unverändert, unabhängig davon, ob man den Index verwendet oder nicht.
         *
         * Allerdings gibt es einige mögliche Konsequenzen oder Überlegungen, die man beachten sollte:
         * Zugriff auf den Index: Wenn man den Index der Elemente benötigt, um spezifische Operationen durchzuführen,
         * wie zum Beispiel das Aktualisieren oder Löschen eines Elements,
         * dann ist die Verwendung der "foreach"-Schleife nicht geeignet.
         * In diesem Fall wäre die Verwendung einer herkömmlichen for-Schleife mit Indexzugriff angemessener.
         *
         * Lesbarkeit und Einfachheit: Die "foreach"-Schleife bietet eine einfachere und lesbarere Syntax,
         * da man sich nicht um die Verwaltung des Indexes kümmern muss.
         * Dies kann den Code übersichtlicher machen, insbesondere wenn man nur auf die Elemente zugreifen
         * und keine spezifischen Operationen mit dem Index durchführen muss.
         *
         * Performance: In Bezug auf die Performance kann die Verwendung der "foreach"-Schleife in einigen Fällen ineffizient sein.
         * Wenn man beispielsweise auf bestimmte Elemente basierend auf ihrem Index zugreifen muss,
         * könnte die Verwendung der for-Schleife mit dem expliziten Indexzugriff effizienter sein.
         * Die "foreach"-Schleife führt intern einen Iterator ein, um die Elemente zu durchlaufen,
         * was eine geringfügige Leistungseinbuße verursachen kann.
         *
         * Insgesamt ist die Wahl zwischen der Verwendung der "foreach"-Schleife und der herkömmlichen
         * for-Schleife mit Indexzugriff abhängig von den spezifischen Anforderungen des Codes,
         * der Lesbarkeit und den Performance-Erwartungen.
         * Es ist wichtig, den geeigneten Ansatz entsprechend der Situation auszuwählen.
         */

        // Ausgabe der Liste der Java-Studenten mit Index
        for (int i = 0; i < javaStudents.size(); i++) {
            System.out.println(javaStudents.get(i) + " im Index :" + i);
        }

        // Ausgabe der Liste der Cloud-Studenten
        System.out.println();
        System.out.println("Liste der Cloud-Studenten:");

        for (String stud : cloudStudents) {
            System.out.println(stud);
        }

    }
}

