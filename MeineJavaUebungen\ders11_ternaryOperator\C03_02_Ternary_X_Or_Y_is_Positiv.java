package ders11_ternaryOperator;

public class C03_02_Ternary_X_Or_Y_is_Positiv {
    public static void main(String[] args) {
        int a = 10;

        System.out.println(a > 0 ? "Positive Zahl" : "Zahl nicht Positiv");  // true

        System.out.println(a > 20 ? a * a : a++);  // a=11

        System.out.println(a < 100 || a < 0 ? 3 * a + 1 : 2 + a / 5);  // 34

        int x = 10;
        int y = 15;

        int z = a > 0 ? y++ : --x;  // z=15 y=16

        System.out.println(x + ", " + y + ", " + z); // 10, 16, 15
    }
}
