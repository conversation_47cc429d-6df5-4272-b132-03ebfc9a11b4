package ders14_scope;

public class C03_VonAnderenKlassenAusClassLevelVariablesErreichen {
    public static void main(String[] args) {

       // Nutze die static variablen aus C02_ClassLevelVariables.

        System.out.println(C02_ClassLevelVariables.bls);  // false
        System.out.println(C02_ClassLevelVariables.strs);  // Java
        System.out.println(C02_ClassLevelVariables.zahls);  // 0
        System.out.println(C02_ClassLevelVariables.chrs);  // a

        // instance variablen aus C02_ClassLevelVariables.
        // Von der Klasse ein Objekt erstellen
        C02_ClassLevelVariables obj = new C02_ClassLevelVariables();
        // dieses mal erreichen wir nur die instance variablen
        System.out.println(obj.bli);  // false
        System.out.println(obj.stri);  // null
        System.out.println(obj.zahli);  // 23
        System.out.println(obj.chri);  // ''  

        System.out.println(obj.bls);
        System.out.println(obj.stri);

    }


}
