package jders21_ordnerVerarbeitungen.anwendung6FlieBufferReader;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;

public class Test {
    public static void main(String[] args) {

        try {
            FileReader fileReader = new FileReader("C:/Users/<USER>/Desktop/Projeler/fileRead.txt");

            BufferedReader bufferedReader = new BufferedReader(fileReader);

            // Jeder Aufruf erzeugt weitere Zeile
            // String zeile = bufferedReader.readLine();
            // String zeile1 = bufferedReader.readLine();
            // System.out.println(zeile);
            // System.out.println(zeile1);

            String zeile;
            while ((zeile = bufferedReader.readLine()) != null) {

                System.out.println(zeile);
            }

            /* In der ersten while-Schleife lesen wir jede Zeile der Datei, bis wir das Ende erreichen
               (wenn bufferedReader.readLine() null zurückgibt). Danach, wenn wir in der zweiten while-Schlei<PERSON> versuchen,
               weitere Zeilen zu lesen, gibt es keine mehr, da wir bereits am Dateiende sind.
               Deshalb wird in der zweiten while-Schleife nichts ausgegeben.
               Um beide while-Schleifen zum Erzeugen von Ausgaben zu bringen, können wir den BufferedReader schließen und erneut öffnen,
               bevor wir die zweite while-Schleife starten. Alternativ könnten wir zwei separate BufferedReader-Instanzen verwenden.
               Wo jedoch zu beachten ist, dass das erneute Öffnen des BufferedReader oder die Verwendung mehrerer Instanzen
               je nach Dateigröße und spezifischen Anforderungen unserer Anwendung möglicherweise nicht ideal sind.*/
            // Eine weitere Möglichkeit der Schleife
            while (true){

                zeile = bufferedReader.readLine();

                if (zeile == null) {
                    break;
                }
                else {
                    System.out.println(zeile);
                }
            }

    /*
            // Lesen von char
            int i = bufferedReader.read();
            System.out.println((char) i);
    */

            // Ausnahme zur Pfadangabe
        } catch (FileNotFoundException e) {

            System.out.println("Fehler : " + e);

            // Ausnahmen über bufferedReader.read
        } catch (IOException e) {

            System.out.println("Fehler : " + e);
        }

    }
}
