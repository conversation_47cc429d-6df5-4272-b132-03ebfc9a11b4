package jders15_objektBeziehungen.anwendung3StudentManagement;

import java.util.ArrayList;

public class Student {

    private String vorname;

    private String nachname;

    private int geburtsjahr;

    // Ein Student kann mehr als eine Telefonnummer haben
    private ArrayList<String> telefonnummern;

    private String studentenNummer;
    // static, weil die Anwendung nur für eine Schule erstellt wird, die wir später benennen können
    private static String schule;

    // Muss nicht den Namen unterrichtsFaecher als ArrayList zugewiesen bekommen, wichtig sind nur die .add Werte die übergeben werden.
    private ArrayList<String> unterrichtsFaecher;

    // Nur Eine Adresse
    private Adressen adresse;

    private ArrayList<Fahrzeug> fahrzeuge;

    // Das Objekt ohne werte zuzuweisen erstellen
    public Student() {

    }

    // Den rest kann und soll Student nachträglich mit den jeweiligen set-Methoden eintragen
    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
        this.studentenNummer = studentenNummer;
    }
    // get-set für alle Felder erstellt
    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    public ArrayList<String> getTelefonnummern() {
        return telefonnummern;
    }

    public void setTelefonnummern(ArrayList<String> telefonnummern) {
        this.telefonnummern = telefonnummern;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    public static String getSchule() {
        return schule;
    }

    public static void setSchule(String schule) {
        Student.schule = schule;
    }

    public ArrayList<String> getUnterrichtsFaecher() {
        return unterrichtsFaecher;
    }

    public void setUnterrichtsFaecher(ArrayList<String> unterrichtsFaecher) {
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    public Adressen getAdresse() {
        return adresse;
    }

    public void setAdresse(Adressen adresse) {
        this.adresse = adresse;
    }

    public ArrayList<Fahrzeug> getFahrzeuge() {
        return fahrzeuge;
    }

    public void setFahrzeuge(ArrayList<Fahrzeug> fahrzeuge) {
        this.fahrzeuge = fahrzeuge;
    }

    @Override
    public String toString() {
        return "Student{" +
                "Vorname='" + vorname + '\'' +
                ", Nachname='" + nachname + '\'' +
                ", Geburtsjahr=" + geburtsjahr +
                ", Telefonnummern=" + telefonnummern +
                ", Studenten Nummer='" + studentenNummer + '\'' +
                ", Unterrichts Fächer=" + unterrichtsFaecher +
                ", Adresse=" + adresse +
                ", Fahrzeuge=" + fahrzeuge +
                '}';
    }
}
