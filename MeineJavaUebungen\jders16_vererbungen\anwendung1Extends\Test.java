package jders16_vererbungen.anwendung1Extends;

public class Test {
    public static void main(String[] args) {

        // in unserer Anwendung gehen wir davon aus das wir keine (Person) ohne Zugehörigkeit erstellen werden

        Leitungspersonal personal1 = new Leitungspersonal();

        personal1.setVorname("Lola");
        personal1.setNachname("Helm");
        personal1.setGeburtsjahr(1975);
        personal1.setZustaendigkeit("Säkreteriat");

        System.out.println("Vorname : " + personal1.getVorname());
        System.out.println("Nachname : " + personal1.getNachname());
        System.out.println("Geburtsjahr : " + personal1.getGeburtsjahr());
        System.out.println("Zuständigkeit : " + personal1.getZustaendigkeit());

        System.out.println(personal1); // Person{vorname='Lola', nachname='<PERSON><PERSON>', geburtsjahr=1975}

    }
}
