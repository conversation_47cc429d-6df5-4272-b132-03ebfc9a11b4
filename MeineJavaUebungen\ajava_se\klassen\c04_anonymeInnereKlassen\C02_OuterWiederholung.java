package ajava_se.klassen.c04_anonymeInnereKlassen;

/**
 * Diese Klasse demonstriert die Verwendung von anonymen inneren Klassen in Java.
 * Anonyme Klassen sind Klassen ohne Namen, die direkt bei der Instanziierung definiert werden.
 * Sie werden häufig für einmalige Implementierungen von Interfaces oder abstrakten Klassen verwendet.
 */
public class C02_OuterWiederholung {

    /**
     * Diese Methode erstellt eine anonyme Klasse, die das Runnable-Interface implementiert,
     * und startet einen neuen Thread mit dieser Implementierung.
     */
    public void method() {

        // Deklaration einer anonymen Klasse vom Typ Runnable
        Runnable runnable = new Runnable() {

            // Die run()-Methode der anonymen Klasse gibt die Nachricht "Hello, world!" aus
            @Override
            public void run() {
                String threadName = Thread.currentThread().getName(); // Thread-Namen abrufen
                System.out.println("Hello, world! " + threadName);
            }
        };
        /* In unserem Beispiel ist die anonyme Klasse die Implementierung des Runnable-Interfaces, die bei
           new Runnable() {...} deklariert wird. Die anonyme Klasse hat keinen Namen,
           da wir sie nur innerhalb dieses Ausdrucks verwenden. */
        // Starten eines neuen Threads mit der anonymen Klasse Runnable als Parameter
        new Thread(runnable).start();
    }

    /**
     * Die Hauptmethode demonstriert zwei verschiedene Arten, anonyme Klassen zu verwenden:
     * 1. Direkt bei der Erstellung eines Thread-Objekts
     * 2. Durch Aufruf der method()-Methode, die eine weitere anonyme Klasse verwendet
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String threadName = Thread.currentThread().getName();
                System.out.println("Merhabalar " + threadName);
            }
        });
        thread.start();
        C02_OuterClass outer = new C02_OuterClass();
        outer.method();
    }
}
