package com.example.mcpjavaapp;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

public class OllamaTest {
    public static void main(String[] args) {
        try {
            // Erstellen eines HTTP-Clients
            HttpClient client = HttpClient.newHttpClient();
            
            // Erstellen einer Anfrage an die Ollama API
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("http://localhost:11434/api/version"))
                    .GET()
                    .build();
            
            // Senden der Anfrage und Empfangen der Antwort
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            
            // Ausgabe der Antwort
            System.out.println("Status Code: " + response.statusCode());
            System.out.println("Response Body: " + response.body());
            
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }
}
