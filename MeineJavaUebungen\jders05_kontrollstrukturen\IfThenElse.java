package jders05_kontrollstrukturen;

public class IfThenElse {
    public static void main(String[] args) {

        int a = 5;

        int b = 0;

        String erg;

        boolean zustand;

       /* if (a > 10) {
            b = 20;
        } else {
            b = 5;
        }*/

        // <PERSON><PERSON>prüfung, ob a größer als 10 ist
        // Wenn ja, wird b auf 20 gesetzt, sonst auf 5
        b = (a > 10) ? 20 : 5;

        // Überprüfung, ob a größer als 10 ist
        // Wenn ja, wird der Wert "Größer" zugewiesen, sonst "kleiner"
        erg = (a > 10) ? "Größer" : "kleiner";

        // Überprüfung, ob a größer als 10 ist
        // Wenn ja, wird der Zustand auf true gesetzt, sonst auf false
        zustand = (a > 10) ? true : false;

        System.out.println("B Wert : " + b);  // B Wert : 5

        System.out.println("Ergebnis : " + erg);  // Ergebnis: kleiner

        System.out.println("Zustand : " + zustand);  // Zustand : false
    }
}
