package jders19_interface.anwendung3;

public interface PersonenVerarbeitungsvorgaenge {

    // Wir werden aus dem interface die in Empfang genommene person in einer leeren Liste speichern, wie wir
    // auch später in leeren Methoden es vorbereiten werden für die Datenbank Eintragungen,
    // in diesem beispiel soll uns die Methode nach dem Speichern/Löschen einer person ein Ergebnis liefern return true/false

    // Die Methoden werden in den implementierten Klassen Pflicht
    public boolean personSpeichern(Person person);

    // ein boolean wert kann erhalten werden, wenn wir wollen
    public boolean personLoeschen(Person person);

    public void personInfosAusgeben(Person person);

    public void adressInfosAusgeben(Person person);

    // Wir wollen alle Personen auflisten, wir senden nichts hier an der Stelle, sondern zur implementierten Klasse
    public void personenListe();



}
