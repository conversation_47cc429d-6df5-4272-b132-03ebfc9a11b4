package jders18_abstract.anwendung2Abstact;

public class Mercedes extends Fahrzeug {

    // Eigenschaft der eigenen Klasse
    private String model;

    public Mercedes() {

    }

    @Override
    public double literVerbrauch() {
        return getGewicht() * 4;
    }

    /*Wenn die Konstruktoren ohne die super-Parameter verwendet werden, bedeute<PERSON> dies,
    dass die Konstruktoren der Oberklasse Fahrzeug nicht aufgerufen werden und somit
    keine Werte in den entsprechenden Attributen der Oberklasse gesetzt werden. Stattdessen werden
    nur die Attribute der Klasse Mercedes mit den übergebenen Parametern initialisiert.

    Das Fehlen der super-Aufrufe kann dazu führen, dass bestimmte Funktionalitäten
    oder Initialisierungen der Oberklasse nicht durchgeführt werden, wenn diese in
    den Konstruktoren definiert sind. Dies kann zu inkonsistenten oder unerwarteten Ergebnissen
    führen, insbesondere wenn die Oberklasse wichtige Eigenschaften oder Verhalten definiert.

    Daher ist es in den meisten Fällen empfehlenswert, den super-Aufruf zu verwenden,
    um sicherzustellen, dass sowohl die Initialisierungen der Oberklasse als auch die spezifischen
    Initialisierungen der Unterklasse durchgeführt werden.
    Dies gewährleistet eine korrekte und konsistente Initialisierung der Objekte.
    public Mercedes(String model) {
        this.model = model;
    }*/

    public Mercedes(String farbe, String fahrzeugArt, double gewicht, String model) {
        super(farbe, fahrzeugArt, gewicht);
        this.model = model;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    // Es ist üblich, die toString()-Methode mit StringBuilder zu überschreiben, da StringBuilder für unsere Anforderungen effizient genug ist.
    @Override
    public String toString() {
        return "Mercedes{" +
                "model='" + model + '\'' +
                ", farbe='" + getFarbe() + '\'' +
                ", Fahrzeug Art='" + getFahrzeugArt() + '\'' +
                ", gewicht=" + getGewicht() +
                '}';
    }
}
