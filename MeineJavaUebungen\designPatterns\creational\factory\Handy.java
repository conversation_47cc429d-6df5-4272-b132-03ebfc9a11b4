package designPatterns.creational.factory;

public interface Handy {

    /*
    Interfaces können als Vertrag zwischen verschiedenen Klassen verwendet werden. Sie definieren eine Reihe von Methoden,
    die von den Klassen implementiert werden müssen, die das Interface implementieren.

    Unterschiede zwischen abstrakten Klassen und Interfaces:
    Abstrakte Klassen können Attribute und Methoden mit einem Implementierungscode haben, während Interfaces nur Methoden mit einem Signatur haben.
    Abstrakte Klassen können als Basisklassen verwendet werden, während Interfaces nicht als Basisklassen verwendet werden können.
    Abstrakte Klassen können direkt instanziiert werden, während Interfaces nicht direkt instanziiert werden können.
    Abstrakte Klassen können von einer oder mehreren Klassen geerbt werden, während Interfaces von einer oder mehreren Klassen implementiert werden können.*/

    String getMarke();
    String getModel();
    int getGroesse();
    int getGewicht();


}
