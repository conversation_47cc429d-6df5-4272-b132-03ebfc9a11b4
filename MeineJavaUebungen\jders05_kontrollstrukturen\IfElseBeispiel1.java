package jders05_kontrollstrukturen;

public class IfElseBeispiel1 {
    public static void main(String[] args) {

        // if-else Concatenations
        int a = 5, b = 6, c = 7;

        // <PERSON><PERSON>pr<PERSON><PERSON><PERSON>, ob a kleiner als b ist
        if (a < b) {
            // Wenn a kleiner als b ist, wird dieser Block ausgeführt
            a = b;
        } else {
            // Wenn a nicht kleiner als b ist, wird dieser Block ausgeführt
            b = a;
        }

        // Ausgabe der Werte von a und b
        System.out.println("a&b = " + a + " & " + b);  // a&b = 6 & 6

        // Ausgabe der Summe von a und b
        System.out.println(a + b);  // 12

        // Ausgabe der konkatenierten Werte von a und b
        System.out.println("ab = " + a + b);  // ab = 66

        // Ausgabe der Summe von a und b sowie des Werts von b
        System.out.println("a+b&b = " + (a + b) + " & " + b);  // a+b&b = 12 & 6

        // Ausgabe der Summe von a und b als String
        System.out.println(String.valueOf(a + b));  // 12
    }

}
