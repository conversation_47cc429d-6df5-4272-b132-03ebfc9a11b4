package jders11_einfuerungArrays.zweidimensionaleArrays;

public class ZweidimensionaleEinfuerungWiederholung {

    public static void main(String[] args) {
        /*
        * Dieser Code erstellt ein 2D-Array "tabelle" mit 4 Zeilen und 7 Spalten.
        * Dann wird jede Zelle des Arrays mit dem Wert 10 gefüllt. Anschließend werden die Werte
        * des Arrays zeilenweise ausgegeben, wobei jeder Wert durch ein Leerzeichen getrennt ist und
        * jede Zeile in einer neuen Zeile angezeigt wird.
        * */

        // Erstellen eines 2D-Arrays mit 4 Zeilen und 7 Spalten
        int[][] tabelle = new int[4][7];

        // Schleife zum Durchlaufen der Zeilen
        for (int i = 0; i < tabelle.length; i++) {

            // Schleife zum Durchlaufen der Spalten in jeder Zeile
            for (int j = 0; j < tabelle[i].length; j++) {

                // Setzen des Werts 10 in jede Zelle des 2D-Arrays
                tabelle[i][j] = 10;
            }
        }

        // Schleife zum Durchlaufen der Zeilen
        for (int i = 0; i < tabelle.length; i++) {

            // Schleife zum Durchlaufen der Spalten in jeder Zeile
            for (int j = 0; j < tabelle[i].length; j++) {

                // Ausgabe des Werts in jeder Zelle des 2D-Arrays, gefolgt von einem Leerzeichen
                System.out.print(tabelle[i][j] + " ");
            }

            // Wechsel zur nächsten Zeile nach dem alle Spalten einer Zeile ausgegeben wurden
            System.out.println();
        }
    }
}
