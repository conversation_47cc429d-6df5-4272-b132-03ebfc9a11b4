package jders13_objekte._03_datenkapselung.Anwendung1Getter;

public class Student {

    private String vorname;

    private String nachname;

    private int geburtsdatum;

    private String studentenNummer;

    public Student() {

    }

    public Student(String vn, String nn, int gd, String sn) {
        vorname = vn;
        nachname = nn;
        geburtsdatum = gd;
        studentenNummer = sn;
    }

    public void setVorname(String vn) {
        // Set Kontrollieren
        if (vn.length() < 3) { // vn = student1.setVorname < 3
            System.out.println("Eingabe nicht Akzeptiert. Vorübergehend wird ihnen der Vorname Nutzer861 zugewiesen");
            vorname = "Nutzer861";
        } else {

            vorname = vn;
        }
    }

    public String getName(){
        return vorname;
    }

    public void setNachname(String nn) {

        if (nn.length() < 3) { // Wenn die Länge des Nachnamens kleiner als 3 ist
            System.out.println("Eingabe nicht Akzeptiert. Vorübergehend wird ihnen der Nachname Nutzer861 zugewiesen");
            nachname = "Nutzer861";
        } else {

            nachname = nn;
        }
    }

    public String getNachname(){
        return nachname;
    }

    public void setGeburtsdatum(int gd) {

        if (gd < 1900 || gd > 2023) { // Wenn das Geburtsdatum kleiner als 1900 oder größer als 2023 ist
            System.out.println("Eingabe nicht Akzeptiert. Vorübergehend wird ihnen das Geburtsdatum 0 zugewiesen");
            geburtsdatum = 0;
        } else {

            geburtsdatum = gd;
        }
    }

    public int getGeburtsdatum(){
        return geburtsdatum;
    }

    public void setStudentenNummer(String sn) {

        if (sn.length() != 4|| sn.length() != 5) { // oder anstatt beide Kontrollen durchzuführen: (sn.length() != 4)
            System.out.println("Eingabe nicht Akzeptiert. Vorübergehend wird ihnen die Studenten Nummer 0000 zugewiesen");
            studentenNummer = "0000";
        } else {

            studentenNummer = sn;
        }
    }

    public String getStudentenNummer(){
        return studentenNummer;
    }

}
