package ders15_StringManipulations;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert die Verwendung der length()-Methode und charAt()-Methode in Java.
 *
 * Das Programm:
 * 1. Liest einen Namen vom Benutzer ein
 * 2. <PERSON><PERSON><PERSON><PERSON>, ob die Länge des Namens gerade oder ungerade ist
 * 3. Gibt den mittleren Buchstaben aus (bei ungerader Länge) oder
 *    die beiden mittleren Buchstaben (bei gerader Länge)
 *
 * Wichtige Konzepte:
 * - Verwendung von length() zur Bestimmung der String-Länge
 * - Verwendung von charAt() zum Zugriff auf einzelne Zeichen
 * - Berechnung von Indizes basierend auf der String-Länge
 * - Unterscheidung zwischen gerader und ungerader Länge mit dem Modulo-Operator
 */
public class C02_lengthWiederholung {
    /**
     * Die Hauptmethode liest einen Namen vom Benutzer ein und gibt den/die mittleren Buchstaben aus.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        /*
         * Aufgabe: Vom Nutzer den Namen einlesen und den mittleren Buchstaben ausgeben.
         * Bei gerader Länge des Namens sollen die beiden mittleren Buchstaben ausgegeben werden.
         */

        // Scanner-Objekt zur Eingabe erstellen
        Scanner sc = new Scanner(System.in);

        // Benutzer zur Eingabe auffordern
        System.out.print("Bitte geben Sie Ihren Namen ein: ");

        // Eingabe einlesen
        String eingegebenerName = sc.nextLine();

        // Die Länge des Namens ermitteln und in einer Variable speichern
        int namensLaenge = eingegebenerName.length();

        System.out.println("Ihr Name \"" + eingegebenerName + "\" hat " + namensLaenge + " Zeichen.");

        // Überprüfen, ob die Länge gerade oder ungerade ist
        if (namensLaenge % 2 == 0) {  // Länge ist gerade
            // Bei gerader Länge gibt es zwei mittlere Buchstaben
            // Beispiel: Bei "Java" (Länge 4) sind die mittleren Buchstaben 'a' und 'v' (Index 1 und 2)

            // Berechnung der Indizes der beiden mittleren Buchstaben
            int ersterMittlererIndex = namensLaenge / 2 - 1;  // Bei Länge 4: 4/2-1 = 1
            int zweiterMittlererIndex = namensLaenge / 2;     // Bei Länge 4: 4/2 = 2

            char ersterMittlererBuchstabe = eingegebenerName.charAt(ersterMittlererIndex);
            char zweiterMittlererBuchstabe = eingegebenerName.charAt(zweiterMittlererIndex);

            System.out.println("Die Länge ist gerade, die mittleren Buchstaben sind: " +
                    ersterMittlererBuchstabe + zweiterMittlererBuchstabe +
                    " (an den Positionen " + (ersterMittlererIndex + 1) + " und " + (zweiterMittlererIndex + 1) + ")");
        } else {  // Länge ist ungerade
            // Bei ungerader Länge gibt es genau einen mittleren Buchstaben
            // Beispiel: Bei "Java!" (Länge 5) ist der mittlere Buchstabe 'v' (Index 2)

            // Berechnung des Index des mittleren Buchstabens
            // Bei Länge 5: (5-1)/2 = 2
            int mittlererIndex = (namensLaenge - 1) / 2;

            char mittlererBuchstabe = eingegebenerName.charAt(mittlererIndex);

            System.out.println("Die Länge ist ungerade, der mittlere Buchstabe ist: " +
                    mittlererBuchstabe +
                    " (an der Position " + (mittlererIndex + 1) + ")");
        }

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        sc.close();

        /*
         * Hinweis zur Berechnung der mittleren Position:
         *
         * Bei gerader Länge (z.B. 4):
         * - Erster mittlerer Index: (4/2)-1 = 1
         * - Zweiter mittlerer Index: 4/2 = 2
         *
         * Bei ungerader Länge (z.B. 5):
         * - Mittlerer Index: (5-1)/2 = 2
         *
         * Beachte: In der Ausgabe werden die Positionen um 1 erhöht angezeigt,
         * da für Benutzer Positionen normalerweise bei 1 beginnen, während
         * Indizes in der Programmierung bei 0 beginnen.
         */
    }
}
