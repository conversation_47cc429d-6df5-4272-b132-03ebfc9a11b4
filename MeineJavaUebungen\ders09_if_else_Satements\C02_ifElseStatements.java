package ders09_if_else_Satements;

import java.util.Scanner;

public class C02_ifElseStatements {
    public static void main(String[] args) {
        // Die Note vom Nutzer Einlesen,
        // wenn die Note 50 oder höher ist soll: "Du wurdest versetzt",
        // bei weniger als 50: "Leider musst du wiederholen" ausgeben werden

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte Note Eingeben");
        double note = scan.nextDouble();

        if (note >= 50) {
            System.out.println("Du wurdest versetzt");
        } else {
            System.out.println("Leider musst du wiederholen");
        }

        //Ein Fall mit mehreren Indizien

        if (note >= 85) {
            System.out.println("Note AA");
        } else if (note >= 65) {
            System.out.println("Note BB");
        } else if (note >= 50) {
            System.out.println("Note CC");
        } else {
            System.out.println("Note DD");
        }
    }
}
