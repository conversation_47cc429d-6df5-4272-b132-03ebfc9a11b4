package com.example.mcpjavaapp.optimization;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import org.springframework.beans.factory.annotation.Value; // Import für @Value

@Component
public class BatchProcessor<T, R> {

    private final ExecutorService executorService;
    private final int batchSize;

    public BatchProcessor(
            @Value("${batch.processor.thread.count:4}") int threadCount, // Standardwert 4
            @Value("${batch.processor.batch.size:10}") int batchSize) {  // Standardwert 10
        this.executorService = Executors.newFixedThreadPool(threadCount);
        this.batchSize = batchSize;
    }

    public List<R> processBatch(List<T> items, Function<T, R> processor) {
        List<R> results = new ArrayList<>(items.size());
        
        for (int i = 0; i < items.size(); i += batchSize) {
            int end = Math.min(i + batchSize, items.size());
            List<T> batch = items.subList(i, end);
            
            List<CompletableFuture<R>> futures = batch.stream()
                .map(item -> CompletableFuture.supplyAsync(() -> processor.apply(item), executorService))
                .toList();
            
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            futures.forEach(future -> results.add(future.join()));
        }
        
        return results;
    }

    public void shutdown() {
        executorService.shutdown();
    }
}
