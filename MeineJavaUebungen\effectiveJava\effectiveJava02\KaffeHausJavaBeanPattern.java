package effectiveJava.effectiveJava02;

public class KaffeHausJavaBeanPattern {

    // Obligatorischer Parameter
    private String kaffeeGroesse;

    // gegen Auswahl ändern
    private String laktoseFreiMilch;
    private String kakao;
    private String vanille;

    // JavaBeans Pattern
    // -Ein leerer Konstruktor
    // -Ein Konstruktor, der die Eigenschaften der Komponente initialisiert
    // -Methoden, die die Eigenschaften der Komponente abrufen und festlegen
    public KaffeHausJavaBeanPattern() {
    }

    public KaffeHausJavaBeanPattern(String kaffeeGroesse, String laktoseFreiMilch, String kakao, String vanille) {
        this.kaffeeGroesse = kaffeeGroesse;
        this.laktoseFreiMilch = laktoseFreiMilch;
        this.kakao = kakao;
        this.vanille = vanille;
    }

    public void setKaffeeGroesse(String kaffeeGroesse) {
        this.kaffeeGroesse = kaffeeGroesse;
    }

    public void setLaktoseFreiMilch(String laktoseFreiMilch) {
        this.laktoseFreiMilch = laktoseFreiMilch;
    }

    public void setKakao(String kakao) {
        this.kakao = kakao;
    }

    public void setVanille(String vanille) {
        this.vanille = vanille;
    }
}
