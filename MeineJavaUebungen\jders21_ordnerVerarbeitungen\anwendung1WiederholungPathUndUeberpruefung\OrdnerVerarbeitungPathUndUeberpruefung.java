package jders21_ordnerVerarbeitungen.anwendung1WiederholungPathUndUeberpruefung;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;

public class OrdnerVerarbeitungPathUndUeberpruefung  {

    public static void main(String[] args) {
        String directoryPath = "C:/Users/<USER>/Desktop/Projeler";
        String fileName = "Dokument3.txt";
        String data = "Hello, world of Java !";
        writeToFile(data, directoryPath, fileName);
    }

    public static void writeToFile(String data, String directoryPath, String fileName) {
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            directory.mkdirs(); // Erstelle das Verzeichnis, wenn es nicht existiert
        }

        File file = new File(directory, fileName);
        try (PrintWriter writer = new PrintWriter(file)) {
            writer.println(data);
            writer.flush();
            if (writer.checkError()) { // Überprüfe auf Fehlerstatus des Writers
                /* Der Code "Fehler beim Schreiben der Datei." wird ausgeben, wenn der PrintWriter einen
                 * Fehler beim Schreiben der Daten in die Datei feststellt. Dies kann in verschiedenen Fällen auftreten, z. B.:
                 * -Nicht genügend Speicherplatz: Wenn auf dem Laufwerk, auf dem die Datei geschrieben werden soll, nicht genügend
                 * Speicherplatz vorhanden ist, kann ein Fehler auftreten.
                 * -Berechtigungsprobleme: Wenn dein Programm nicht die erforderlichen Berechtigungen hat, um in das angegebene
                 * Verzeichnis zu schreiben, wird ein Fehler auftreten.
                 * -Hardware-Fehler: Wenn es Hardwareprobleme gibt, während die Daten auf die Festplatte geschrieben werden, kann ein Fehler auftreten.
                 * Dateisystemfehler: Wenn das Dateisystem beschädigt ist oder Fehler aufweist, kann das Schreiben in die Datei fehlschlagen.
                 *
                 * Um sicherzustellen, dass die Daten erfolgreich geschrieben wurden, ist es eine gute Praxis,
                 * die Methode checkError() zu verwenden, wie wir es im Codebeispiel gemacht haben.
                 * Dies ermöglicht uns, den Fehlerstatus des PrintWriter zu überprüfen und angemessen darauf zu reagieren,
                 * falls ein Fehler aufgetreten ist.*/
                System.out.println("Fehler beim Schreiben der Datei.");
            } else {
                System.out.println("Daten erfolgreich in die Datei geschrieben: " + file.getAbsolutePath());
            }
        } catch (IOException e) {
            System.out.println("Fehler beim Öffnen oder Schließen der Datei.");
        }
    }
}