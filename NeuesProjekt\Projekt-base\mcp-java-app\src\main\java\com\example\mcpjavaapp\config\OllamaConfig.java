package com.example.mcpjavaapp.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OllamaConfig {

    @Value("${spring.ai.ollama.base-url:http://localhost:11434}")
    private String ollamaBaseUrl;

    @Value("${spring.ai.ollama.chat.options.model:phi4-mini-reasoning:3.8b}")
    private String ollamaModel;

    // Optional: Weitere Ollama-Optionen aus Properties laden
    @Value("${spring.ai.ollama.chat.options.temperature:0.7}")
    private Double temperature;

    @Value("${spring.ai.ollama.chat.options.top-k:100}")
    private Integer topK;


    @Bean
    public OllamaApi ollamaApi() {
        return new OllamaApi(ollamaBaseUrl);
    }

    @Bean
    public OllamaChatModel ollamaChatModel(OllamaApi ollamaApi) {
        return OllamaChatModel.builder()
                .ollamaApi(ollamaApi)
                .defaultOptions(
                    OllamaOptions.builder()
                        .model(ollamaModel)
                        .temperature(temperature)
                        .topK(topK)
                        .build()
                )
                .build();
    }

    // Generische ChatClient Bean, die von Services verwendet werden kann
    @Bean
    public ChatClient chatClient(OllamaChatModel ollamaChatModel) {
        return ChatClient.builder(ollamaChatModel).build();
    }
}
