package ders04_dataCasting;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert das Typecasting zwischen char und int in Java
 * sowie die Verwendung von ASCII-Werten.
 *
 * Wichtige Konzepte:
 * - In Java wird der Datentyp char intern als 16-Bit-Unicode-Wert gespeichert
 * - Bei arithmetischen Operationen mit char-Werten werden diese automatisch zu int konvertiert
 * - Durch explizites Casting kann ein int-Wert zurück zu einem char konvertiert werden
 * - Die ASCII-Tabelle ordnet Zeichen numerischen Werten zu (z.B. 'a' = 97, 'b' = 98)
 */
public class C03_DataCastingChar {
    /**
     * Die Hauptmethode demonstriert verschiedene Aspekte des Typcastings zwischen char und int
     * sowie die Verwendung von ASCII-Werten.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Teil 1: Grundlegende Operationen mit char-Werten
        System.out.println("Teil 1: Grundlegende Operationen mit char-Werten");

        // Addition von zwei char-Werten ergibt einen int-Wert (ASCII-Werte werden addiert)
        int a = 'a' + 'b';  // 97 + 98 = 195
        System.out.println("int a = 'a' + 'b' ergibt: " + a);

        // Direkte Addition von char-Werten in der Ausgabe
        System.out.println("'a' + 'b' ergibt: " + ('a' + 'b'));  // 195

        // Durch Voranstellen eines leeren Strings wird die Addition als String-Konkatenation behandelt
        System.out.println("\"\" + 'a' + 'b' ergibt: " + ("" + 'a' + 'b'));  // "ab"
        System.out.println();

        // Teil 2: Interaktives Beispiel mit Benutzereingabe
        System.out.println("Teil 2: Interaktives Beispiel mit Benutzereingabe");
        System.out.println("Aufgabe: Geben Sie ein Zeichen ein, und wir zeigen die drei");
        System.out.println("nachfolgenden Zeichen in der ASCII-Tabelle.");

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scan = new Scanner(System.in);

        // Benutzer zur Eingabe auffordern
        System.out.println("\nBitte geben Sie ein Zeichen ein:");

        // Erstes Zeichen der Eingabe als char einlesen
        char eingegebenerCharacter = scan.nextLine().charAt(0);

        // Automatische Umwandlung von char zu int (ASCII-Wert)
        int asciiValue = eingegebenerCharacter;  // Implizites Casting

        // Ausgabe des eingegebenen Zeichens und seines ASCII-Werts
        System.out.println("\nEingegebenes Zeichen: " + eingegebenerCharacter);
        System.out.println("ASCII-Wert des Zeichens '" + eingegebenerCharacter + "': " + asciiValue);

        // Ausgabe der drei nachfolgenden Zeichen und ihrer ASCII-Werte
        System.out.println("\nDie drei nachfolgenden Zeichen in der ASCII-Tabelle:");

        // Erstes nachfolgendes Zeichen
        System.out.println("1. ASCII-Wert: " + (asciiValue + 1) +
                           " - Zeichen: " + (char)(eingegebenerCharacter + 1));

        // Zweites nachfolgendes Zeichen
        System.out.println("2. ASCII-Wert: " + (asciiValue + 2) +
                           " - Zeichen: " + (char)(eingegebenerCharacter + 2));

        // Drittes nachfolgendes Zeichen
        System.out.println("3. ASCII-Wert: " + (asciiValue + 3) +
                           " - Zeichen: " + (char)(eingegebenerCharacter + 3));

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scan.close();

        /*
         * Hinweise zum Typecasting zwischen char und int:
         *
         * 1. Implizites Casting (char zu int):
         *    - Erfolgt automatisch bei arithmetischen Operationen
         *    - Beispiel: int asciiValue = eingegebenerCharacter;
         *
         * 2. Explizites Casting (int zu char):
         *    - Erfordert explizite Angabe des Zieltyps in Klammern
         *    - Beispiel: char nextChar = (char)(eingegebenerCharacter + 1);
         *
         * 3. Klammern bei Casting-Ausdrücken:
         *    - (char)(eingegebenerCharacter + 1) ist korrekt
         *    - (char)eingegebenerCharacter + 1 würde zuerst casten und dann addieren
         */
    }
}