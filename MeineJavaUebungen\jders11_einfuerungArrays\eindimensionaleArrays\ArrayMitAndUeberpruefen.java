package jders11_einfuerungArrays.eindimensionaleArrays;

import java.util.Scanner;

public class ArrayMitAndUeberpruefen {
    public static void main(String[] args) {

        Scanner sc = new Scanner(System.in);
        int tag;

        System.out.print("geben sie die Zahl vom Tag ein : ");
        tag = sc.nextInt();

        // int [] zahlen = {20, 30, 40, 50, 60, 70, 80, 90, 100};
        String[] tage = {"Montag", "Dienstag", "Mittwoch", "Donnerstag", "Freitag", "Samstag", "Sonntag"};  // Index 0 - 6

        if (tag >= 1 && tag <= tage.length) { // Überprüfung der Eingabe

            System.out.println(tage[tag - 1]); // zieht -1 ab von der Nutzereingabe

        }else {

            System.out.println("kein über eintreffen der tag mit dieser Zahl");
        }

        // tage[3] = "April";

        System.out.println(tage[3]);  // Donnerstag
        System.out.println();


        System.out.println();

        for (int i = 0; i < 7; i++) {

            System.out.println(tage[i]);  // Mo-So
        }


    }

}
