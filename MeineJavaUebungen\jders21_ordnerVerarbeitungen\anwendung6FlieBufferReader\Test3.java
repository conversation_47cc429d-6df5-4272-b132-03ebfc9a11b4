package jders21_ordnerVerarbeitungen.anwendung6FlieBufferReader;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

public class Test3 {

    public static void main(String[] args) {

        // Die Verwendung des try-with-resources-Blocks hat folgende Auswirkungen:
        //-Der BufferedReader wird automatisch geschlossen, wenn der try-with-resources-Block abgeschlossen wird,
        // unabhä<PERSON><PERSON> davon, ob eine Ausnahme auftritt oder nicht.
        //-Es ist nicht mehr erforderlich, den BufferedReader im finally-Block zu schließen.

        try(BufferedReader bufferedReader =  new BufferedReader(new FileReader("C:/Users/<USER>/Desktop/Projeler/fileRead.txt"))) {
            // hier durch wird eine Verbindung zur Datei geöffnet, die gelesen werden soll.

            String zeile;
            while ((zeile = bufferedReader.readLine()) != null) {

                System.out.println(zeile);
            }


            // Ausnahme zur Pfadangabe und Ausnahmen über bufferedReader.read kann über IOException abgefangen werden
        } catch (IOException e) {

            System.out.println("Fehler : " + e);


        }


    }
}
