package jders19_interface.anwendung3;

import java.util.ArrayList;

public class StudentenVerarbeitungsvorgaengeImpl implements StudentenVerarbeitungsvorgaenge {

    private ArrayList<Student> studenten = new ArrayList<>();

    @Override
    public boolean student<PERSON><PERSON><PERSON>(Student student) {

        return studenten.remove(student);
    }

    @Override
    public boolean studentS<PERSON><PERSON><PERSON>(Student student) {

        return studenten.add(student);
    }

    @Override
    public void studentInfosAusgeben(Student student) {

        System.out.println(student);
    }

    @Override
    public void studentAdressInfosAusgeben(Student student) {

        System.out.println(student.getAdresse());
    }

    @Override
    public void studentenListe() {

        for (Student student : studenten) {
            // wenn wir die Ausgabe bearbeiten wollen, können wir die toString Methode nutzen oder auch an der stelle direkt den out-print editieren
            System.out.println(student);
        }

    }

    // wenn wir die Liste auf der anderen Seite erreichen möchten, können wir sie so bekommen
    public ArrayList<Student> getStudenten() {
        return studenten;
    }
}
