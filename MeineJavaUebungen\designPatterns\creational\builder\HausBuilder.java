package designPatterns.creational.builder;

public class HausBuilder {

    private String strasse;
    private String bezirk;
    private String stadt;

    private int gebaedeJahr;
    private int zimmerZahl;
    private int badZahl;
    private int balkon;

    private boolean isMoebliert;
    private boolean isParkplatz;
    private boolean isSpie<PERSON>platz;
    private boolean isA<PERSON>tellplatz;

    // Nicht als Parameter, sondern direkt können wir unsere optionalen bestimmen
    public static HausBuilder startAppartmentHausBuild(String strasse, String bezirk, String stadt, int gebaedeJahr, int zimmerZahl){

        HausBuilder hausBuilder = new HausBuilder();
        hausBuilder.strasse = strasse;
        hausBuilder.stadt = stadt;
        hausBuilder.bezirk = bezirk;
        hausBuilder.gebaedeJahr = gebaedeJahr;
        hausBuilder.zimmerZahl = zimmerZahl;
        hausBuilder.isMoebliert = true;

        return hausBuilder;
    }

    // Beginn des HausBuilder-Vorgangs
    // mit den Pflichtparametern String strasse, String bezirk, String stadt,int gebaedeJahr, int zimmerZahl.
    // Anschließend müssen die Konstruktoren der Pflichtfelder entfernt werden, weil sie nicht optional sind
    public static HausBuilder startNormalHausBuild(String strasse, String bezirk, String stadt, int gebaedeJahr, int zimmerZahl){

        HausBuilder hausBuilder = new HausBuilder();
        hausBuilder.strasse = strasse;
        hausBuilder.stadt = stadt;
        hausBuilder.bezirk = bezirk;
        hausBuilder.gebaedeJahr = gebaedeJahr;
        hausBuilder.zimmerZahl = zimmerZahl;

        return hausBuilder;
    }

    // Fertigstellung des Hausbaus und Rückgabe des erstellten Hauses

    public Haus build(){

        Haus haus = new Haus();

        haus.setStrasse(strasse);
        haus.setBezirk(bezirk);
        haus.setStadt(stadt);
        haus.setGebaeudeJahr(gebaedeJahr);
        haus.setZimmerZahl(zimmerZahl);
        haus.setBadZahl(badZahl);
        haus.setBalkon(balkon);
        haus.setMoebeliert(isMoebliert);
        haus.setParkplatz(isParkplatz);
        haus.setSpielplatz(isSpielplatz);
        haus.setAbstellplatz(isAbstellplatz);

        return haus;

    }
    /*
    // Setzen der Straße und Rückgabe des HausBuilders für fluente API-Verwendung
    public HausBuilder setStrasse(String strasse) {
        this.strasse = strasse;
        return this;
    }

    public HausBuilder setBezirk(String bezirk) {
        this.bezirk = bezirk;
        return this;
    }

    public HausBuilder setStadt(String stadt) {
        this.stadt = stadt;
        return this;
    }

    public HausBuilder setGebaedeJahr(int gebaedeJahr) {
        this.gebaedeJahr = gebaedeJahr;
        return this;
    }

    public HausBuilder setZimmerZahl(int zimmerZahl) {
        this.zimmerZahl = zimmerZahl;
        return this;
    }*/

    public HausBuilder setBadZahl(int badZahl) {
        this.badZahl = badZahl;
        return this;
    }

    public HausBuilder setBalkon(int balkon) {
        this.balkon = balkon;
        return this;
    }

    public HausBuilder setMoebliert(boolean moebliert) {
        isMoebliert = moebliert;
        return this;
    }

    public HausBuilder setParkplatz(boolean parkplatz) {
        isParkplatz = parkplatz;
        return this;
    }

    public HausBuilder setSpielplatz(boolean spielplatz) {
        isSpielplatz = spielplatz;
        return this;
    }

    public HausBuilder setAbstellplatz(boolean abstellplatz) {
        isAbstellplatz = abstellplatz;
        return this;
    }
}
