package effectiveJava.effectiveJava02;

// Builder Design Pattern
public class Item2Test {

    public static void main(String[] args) {

        // Beim <PERSON>ellen können mehrere Parameter uns die Arbeit erschweren und zu Unlesbarkeit des codes führen
        // Telescope Pattern. KaffeHausTelescopePattern kh = new KaffeHaus("Maxi", "Hafer");

        KaffeHausJavaBeanPattern khjb= new KaffeHausJavaBeanPattern();
        khjb.setKaffeeGroesse("Maxi");
        // int wert = 10
        // Da hier code noch zwischen geraten könnte und unsere Einheitlichkeit verloren geht,
        // auch wenn hier jede Zeile einzeln ausgeführt wird und wir keinerlei Kontrollen durchführen können
        khjb.setKaffeeGroesse("Hafer");

        KaffeHausBuilder khb1 = new KaffeHausBuilder.KHBuilder("mini").laktosefreiMilchGruppe("Hafer").kakaoGruppe("Vollmilch Schokolade").build();

        KaffeHausBuilder khb2 = new KaffeHausBuilder.KHBuilder("Maxi").build();

        KaffeHausBuilder khb3 = new KaffeHausBuilder.KHBuilder("").build();

    }

}
