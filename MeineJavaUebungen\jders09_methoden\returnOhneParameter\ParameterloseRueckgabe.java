package jders09_methoden.returnOhneParameter;

public class ParameterloseRueckgabe {
    public static void main(String[] args) {

        int a = addieren();
        System.out.println("Ergebnis : " + a);

        int x = addieren();
        System.out.println("Das 2. Ergebnis : " + x);

        double dMultipliziert = multiplizieren();
        System.out.println("Multiplikation ergibt : " + dMultipliziert);

    }

    public static int addieren() {

        int a = 10;

        int b = 5;

        int addiert = a + b;

        return addiert;

    }

    public static double multiplizieren() {

        int a = 5;
        double b = 2.7;
        // ohne eine weitere Variable für a*b
        return a * b;

    }


}
