package ders06_increment_decrement;

public class C03_03_PostIncrement_OCA {
    public static void main(String[] args) {

        int num1 = 9;

        int num2 = num1++;  // num2: 9 (Post-Inkrement: num1 wird zuerst num2 zugewiesen, dann inkrementiert)

        if (num2 < 10) {    // num2: 9 (<PERSON><PERSON> bleibt 9, da es vor der Inkrementierung zugewiesen wurde)
            System.out.println(num2 + " Hello World");
        } else {
            System.out.println(num1 + " Hello Universe");
        }
    }
}
