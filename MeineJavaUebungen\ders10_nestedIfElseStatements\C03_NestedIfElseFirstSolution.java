package ders10_nestedIfElseStatements;

import java.util.Scanner;

public class C03_NestedIfElseFirstSolution {
    public static void main(String[] args) {
        /*
            Beispiel: Nutzer Eingabe Geschlecht und Alter verarbeiten wo,
            Fr<PERSON><PERSON>, 60 Jahre und über, Männer 65 und über können in Rente.
            Mit Bezug auf Geschlecht und Alter "Du kannst in Rente" Oder "Um Rente zu Beziehen, musst du ..Jahre noch Arbeiten"
         */

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie ihr Geschlecht an");
        String geschlecht = scan.nextLine();
        System.out.println("Bitte geben sie ihr Alter an");
        Double alter = scan.nextDouble();

        // Erst wählen wir unsere, Haupt variable
        // Beispiel wählen wir das Alter und erstellen unsere Hauptkonstruktion
        // im Fall Alter gibt es andere verschiedene Möglichkeiten in unserem Beispiel: Rente
        // unter 60 niemand, ab 60 nur Frauen, ab 65 nur Männer

        if (alter < 0 || alter > 90) {
            System.out.println("Alters Eingabe ungültig");
        } else if (alter < 60) {
            // unter 60 teil
            if (geschlecht.equalsIgnoreCase("Weiblich")) {
                System.out.println("Um Rente zu Beziehen, musst du " + (60 - alter) + " Jahre noch Arbeiten");
            } else if (geschlecht.equalsIgnoreCase("Männlich")) {
                System.out.println("Um Rente zu Beziehen, musst du " + (65 - alter) + " Jahre noch Arbeiten");
            } else {
                System.out.println("Geschlecht Eingabe ungültig");
            }

        } else if (alter < 65) {
            // 60 bis 65
            if (geschlecht.equalsIgnoreCase("Weiblich")) {
                System.out.println("Du Kannst in Rente");
            } else if (geschlecht.equalsIgnoreCase("Männlich")) {
                System.out.println("Um Rente zu Beziehen, musst du " + (65 - alter) + " Jahre noch Arbeiten");
            } else {
                System.out.println("Geschlecht Eingabe ungültig");
            }
        } else {
            // 65 bis 90
            if (geschlecht.equalsIgnoreCase("Weiblich")) {
                System.out.println("Du Kannst in Rente");
            } else if (geschlecht.equalsIgnoreCase("Männlich")) {
                System.out.println("Du Kannst in Rente");
            } else {
                System.out.println("Geschlecht Eingabe ungültig");
            }
        }
    }
}
