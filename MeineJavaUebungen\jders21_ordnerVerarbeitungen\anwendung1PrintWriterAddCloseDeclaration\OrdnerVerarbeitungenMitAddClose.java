package jders21_ordnerVerarbeitungen.anwendung1PrintWriterAddCloseDeclaration;

import java.io.FileNotFoundException;
import java.io.PrintWriter;

public class OrdnerVerarbeitungenMitAddClose {
    public static void main(String[] args) throws FileNotFoundException {

        System.out.println("Der Schreibvorgang beginnt. ");

        //  Der Drucker wird auf Null gesetzt, um sicherzustellen, dass er nicht mehr verwendet wird, wenn ein Fehler auftritt.
        PrintWriter schreiber = null;

        try {

            schreiber = new PrintWriter("C:/Users/<USER>/Desktop/Projeler/Dokument3.txt");

            schreiber.println("Gute Nacht! ");
            schreiber.println("Wie geht es dir? ");



        }catch (Exception e){

        }


        // Es ist wichtig, den gesamten Kontext im try-catch-Block zu verwenden, damit das Programm im Falle eines Fehlers nicht abstürzt,
        // wenn das Schreiben der Datei nicht erfolgreich ist bleibt unser Programm im try-catch block stehen
        schreiber.println("Nur wenn kein Fehler auftretet,dann geht es hier weiter lang");
        schreiber.close();

        System.out.println("Fehler : ");


    }
}
