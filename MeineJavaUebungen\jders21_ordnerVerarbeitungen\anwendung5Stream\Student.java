package jders21_ordnerVerarbeitungen.anwendung5Stream;

import java.io.Serializable;

@SuppressWarnings("serial")
public class Student implements Serializable {

    /*Um die Reihenfolge als ablauf einer Serie Festzulegen nutzen wir implements Serializable  */

    private String vorname;

    private String nachname;

    private int geburtsjahr;

    private String studentenNummer;

    public Student() {

    }

    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
        this.studentenNummer = studentenNummer;
    }

    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    @Override
    public String toString() {
        return "Student{" +
                "vorname='" + vorname + '\'' +
                ", nachname='" + nachname + '\'' +
                ", geburtsjahr=" + geburtsjahr +
                ", studentenNummer='" + studentenNummer + '\'' +
                '}';
    }
}
