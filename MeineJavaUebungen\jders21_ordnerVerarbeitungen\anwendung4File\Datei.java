package jders21_ordnerVerarbeitungen.anwendung4File;

import java.io.File;
import java.util.Date;

public class Datei {
    public static void main(String[] args) {

        // Erstellt ein neues File-Objekt für die Datei "studenten.txt".
        File file = new File("C:/Users/<USER>/Desktop/Projeler/studenten1.txt");
        // File file = new File("C:/Users/<USER>/Desktop/Projeler/");

        // Überprüft, ob das File-Objekt ausgeführt werden kann.
        boolean funktionszustand = file.canExecute();

        // Gibt den Funktionszustand des File-Objekts aus.
        System.out.println("Funktionszustand : " + funktionszustand);

        // Überprüft, ob das File-Objekt gelesen werden kann.
        boolean leseZustand = file.canRead();

        // Gibt den Lesezustand des File-Objekts aus.
        System.out.println("Lese zustand : " + leseZustand);

        // Überprüft, ob das File-Objekt geschrieben werden kann.
        boolean schreibZustand = file.canWrite();

        // Gibt den Schreibzustand des File-Objekts aus.
        System.out.println("Schreib zustand : " + schreibZustand);

        // Überprüft, ob das File-Objekt existiert.
        boolean existsZustand = file.exists();

        // Gibt den Existenzzustand des File-Objekts aus.
        System.out.println("Existence zustand : " + existsZustand);

        // Ruft den absoluten Pfad des File-Objekts ab.
        String path = file.getAbsolutePath();

        // Gibt den absoluten Pfad des File-Objekts aus.
        System.out.println("Path : " + path);

        // Ruft den Namen des File-Objekts ab.
        String name = file.getName();

        // Gibt den Namen des File-Objekts aus.
        System.out.println("Name : " + name);

        // Ruft den Elternordner des File-Objekts ab.
        String parent = file.getParent();

        // Gibt den Elternordner des File-Objekts aus.
        System.out.println("Parent :" + parent);

        // Ruft das Datum der letzten Änderung des File-Objekts ab.
        long letzteBearbeitung = file.lastModified();

        // Gibt das Datum der letzten Änderung des File-Objekts als long aus.
        System.out.println("Zuletzt bearbeitet als long : " + letzteBearbeitung);

        // Erstellt ein neues Date-Objekt für das Datum der letzten Änderung des File-Objekts.
        Date date = new Date(letzteBearbeitung);

        // Gibt das Datum der letzten Änderung des File-Objekts als Datum aus.
        System.out.println("long als Datum : " + date);

        // Überprüft, ob das File-Objekt ein Verzeichnis ist.
        boolean istOrdner = file.isDirectory();

        // Gibt an, ob das File-Objekt ein Verzeichnis ist.
        System.out.println("Ordner Festgestellt : " + istOrdner);
    }
}
