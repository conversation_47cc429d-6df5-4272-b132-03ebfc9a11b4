package jders15_objektBeziehungen.anwendung2Aggregation;

public class Student {

    private String vorname;

    private String nachname;

    private int geburtsjahr;

    private String studentenNummer;

    /*
    * Die Klasse "Student" hat ein Attribut vom Typ "Adressen" namens "adressen".
    * Das ist ein Zeichen dafür, dass die Klasse "Student" eine Aggregation mit der Klasse "Adressen" hat.
    *
    * In(Test-Klasse) werden dann die Studenten erstellt und ihnen Adressen zugewiesen.*/
    private Adressen adressen;

    public Student() {

    }

    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer, Adressen adressen) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
        this.studentenNummer = studentenNummer;
        this.adressen = adressen;
    }

    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    public Adressen getAdressen() {
        return adressen;
    }

    public void setAdressen(Adressen adressen) {
        this.adressen = adressen;
    }

    @Override
    public String toString() {
        return "Student{" +
                "vorname='" + vorname + '\'' +
                ", nachname='" + nachname + '\'' +
                ", geburtsjahr=" + geburtsjahr +
                ", studentenNummer='" + studentenNummer + '\'' +
                ", adressen=" + adressen +
                '}';
    }
}
