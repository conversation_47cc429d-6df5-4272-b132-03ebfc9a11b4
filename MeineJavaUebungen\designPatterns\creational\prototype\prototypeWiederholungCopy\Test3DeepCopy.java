package designPatterns.creational.prototype.prototypeWiederholungCopy;

import java.util.Arrays;

public class Test3DeepCopy {
    public static void main(String[] args) {

        // Dies demonstriert den Unterschied zwischen einer tiefen Kopie (Deep Copy),
        // bei der separate Objekte erstellt werden, und einer flachen Ko<PERSON> (Shallow Copy),
        // bei der nur Verweise auf die gleichen Objekte kopiert werden. Hier werden tatsächliche
        // separate Integer-Objekte für jedes Element in beiden Arrays erstellt,
        // wodurch sichergestellt wird, dass Änderungen in einem Array das andere nicht beeinflussen.

                final int NUM = 8;
                Integer[] old = new Integer[NUM];
                Integer[] new1 = new Integer[NUM];

                for (int i = 0; i < NUM; i++) {
                    old[i] = i;
                }

                for (int i = 0; i < NUM; i++) {
                    // Eine Deep Copy: Jedes Element von "old" wird in "new1" kopiert.
                    new1[i] = old[i];
                }

                new1[3] = 30000;



        System.out.println(Arrays.toString(old));
        System.out.println(Arrays.toString(new1));


        System.out.println(old);  // [Ljava.lang.Integer;@6bc7c054
        System.out.println(new1);  // [Ljava.lang.Integer;@232204a1

    }
}