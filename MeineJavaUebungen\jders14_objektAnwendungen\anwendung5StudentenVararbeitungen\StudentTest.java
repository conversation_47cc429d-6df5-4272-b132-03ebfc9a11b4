package jders14_objektAnwendungen.anwendung5StudentenVararbeitungen;

import java.util.ArrayList;

public class StudentTest {
    public static void main(String[] args) {

        // Wenn Student('Beispiels auf eine Seite') <PERSON><PERSON><PERSON> wurde, jedoch F<PERSON> noch nicht gewählt wurden.
        // Beispielsweise wenn Nutzer keine Werte zuweisen muss = null
        // Wenn wir z.B. eine vordefinierte studentenNummer an anderer Stelle übergeben wollen, sollte studentenNummer = null stehen
        Student student1 = new Student("Jörn", "Musial", 1994, "2350", null);

        // Student wählt seine Fächer in die ArrayList ('Beispiels auf einer anderen Seite') vom System
        ArrayList<String> faecherStudiumBioInformatik = new ArrayList<>();
        faecherStudiumBioInformatik.add("Mathematik");
        faecherStudiumBioInformatik.add("Physik");
        faecherStudiumBioInformatik.add("Biologie");

        // Dann wird anschließend Fächer an Studenten Übergeben
        student1.setUnterrichtsFaecher(faecherStudiumBioInformatik);

        // Mehrere Instanzen von Studenten
        Student student2 = new Student("Tim", "Feld", 1995, "2351", null);
        // Wenn Student2 will, kann er das gleiche Studium wie Student1 beziehen
        student2.setUnterrichtsFaecher(faecherStudiumBioInformatik);


        Student student3 = new Student ("Jan", "Butter", 1993, "2352", null);

        Student student4 = new Student("Bernd", "Fisch", 1994, "2353", null);

        // Rufen die Klasse StudentenVerarbeitungsVorgaenge zum Hinzufügen/entfernen der Studenten,
        // in dem wir sie nun über erreicheSVV erreichen werden.
        StudentenVerarbeitungsvorgaenge erreicheSVV = new StudentenVerarbeitungsvorgaenge();

        // Studenten werden der Reihe nach in die über unsere Methode Liste eingefügt
        erreicheSVV.studentEinfuegen(student1);
        erreicheSVV.studentEinfuegen(student2);
        erreicheSVV.studentEinfuegen(student3);
        erreicheSVV.studentEinfuegen(student4);

        System.out.println("-------------------------------------------------------");
        erreicheSVV.studentenAuflisten();

    }


}
