package designPatterns.creational.prototype.prototypeWiederholungCopy;

public class Test2ShallowCopy {
    public static void main(String[] args) {

        // Am Ende enthält new1 eine tiefe Kopie von old, sodass Änderungen an einem der Arrays
        // das andere nicht beeinflussen, da sie unterschiedliche Speicherbereiche verwenden.

        final int NUM = 8;
        int[] old = new int[NUM]; // Ein neues Integer-Array "old" mit 8 Elementen wird erstellt.
        int[] new1 = new int[NUM]; // Ein neues Integer-Array "new1" mit 8 Elementen wird erstellt.

        for (int i = 0; i < NUM; i++) {
            old[i] = i; // Das "old"-Array wird mit den Werten von 0 bis 7 gefüllt.
        }

        for (int i = 0; i < NUM; i++) {
            new1[i] = old[i]; // Eine Deep Copy: <PERSON><PERSON> von "old" wird in "new1" kopiert.
        }


        System.out.println(old); // [I@6bc7c054
        System.out.println(new1);  // [I@232204a1

    }

}
