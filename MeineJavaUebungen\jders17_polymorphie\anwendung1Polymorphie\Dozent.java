package jders17_polymorphie.anwendung1Polymorphie;

public class Dozent extends Person {

    private String branche;

    public Dozent() {

    }

    public Dozent(String vorname, String nachname, int geburtsjahr) {
        super(vorname, nachname, geburtsjahr);
    }

    public Dozent(String vorname, String nachname, int geburtsjahr, String branche) {
        super(vorname, nachname, geburtsjahr);
        this.branche = branche;
    }

    public String getBranche() {
        return branche;
    }

    public void setBranche(String branche) {
        this.branche = branche;
    }

    @Override
    public String toString() {
        return "Dozent{" +
                " Branche: '" + branche + '\'' +
                ", Vorname: '" + getVorname() + '\'' +
                ", Nachname: '" + getNachname() + '\'' +
                ", Geburtsjahr: " + getGeburtsjahr() +
                '}';
        /* Das Zeichen '' wird in Java als Escape-Zeichen verwendet.
         * Es ermöglicht die Darstellung bestimmter Sonderzeichen oder Zeichenfolgen,
         * die sonst eine spezielle Bedeutung hätten, innerhalb von Zeichenketten oder Zeichenliteralen.
         *
         * Hier sind einige Beispiele für die Verwendung von '' als Escape-Zeichen:
         * '' als Escape-Zeichen: Wenn Sie das Zeichen '' selbst darstellen möchten,
         * müssen Sie es mit einem '' escapen. Zum Beispiel: \\ ergibt eine Zeichenkette mit dem Wert ''.
         *
         * '"' als Anführungszeichen: Wenn Sie das Zeichen '"' (Anführungszeichen) innerhalb einer Zeichenkette
         *  darstellen möchten, müssen Sie es mit '' escapen. Zum Beispiel: "\"" ergibt eine Zeichenkette mit dem Wert '"'.
         *
         * '\n' als Zeilenumbruch: Das Zeichen '\n' repräsentiert einen Zeilenumbruch.
         * Wenn es innerhalb einer Zeichenkette verwendet wird, erzeugt es einen Zeilenumbruch
         * an dieser Stelle. Zum Beispiel: "Hallo\nWelt" gibt die Zeichenkette "Hallo" gefolgt
         * von einem Zeilenumbruch und dann "Welt" aus.
         *
         * Es gibt noch viele weitere Escape-Sequenzen,
         * die verwendet werden können, um spezielle Zeichen darzustellen,
         * wie zum Beispiel '\t' für einen horizontalen Tabulator oder '\b' für einen Backspace.
         *
         * Durch die Verwendung des Escape-Zeichens '' können Sie in Java spezielle Zeichen oder
         * Zeichenfolgen darstellen, die sonst in Zeichenketten oder Zeichenliteralen eine spezielle Bedeutung hätten.
         * */
    }
}
