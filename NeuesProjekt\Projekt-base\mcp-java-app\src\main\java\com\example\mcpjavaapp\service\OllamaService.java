package com.example.mcpjavaapp.service;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class OllamaService {

    private static final Logger log = LoggerFactory.getLogger(OllamaService.class);
    private final ChatClient chatClient;

    @Autowired
    public OllamaService(ChatClient chatClient) {
        this.chatClient = chatClient;
    }

    public String analyzeCode(String code) {
        String promptText = "Analysiere den folgenden Java-Code auf Bugs, Performance-Probleme und Verbesserungsmöglichkeiten. Gib konkrete Vorschläge:\n\n```java\n" + code + "\n```";
        return callChatClient(promptText);
    }

    public String generateDocumentation(String code) {
        String promptText = "Erstelle eine detaillierte Dokumentation für den gegebenen Java-Code im Markdown-Format:\n\n```java\n" + code + "\n```";
        return callChatClient(promptText);
    }

    public String improveCode(String code) {
        String promptText = "Verbessere den folgenden Java-Code. Optimiere ihn für Lesbarkeit und Performance. Gib nur den verbesserten Code zurück:\n\n```java\n" + code + "\n```";
        return callChatClient(promptText);
    }

    public String generateCompletion(String userPrompt) {
        return callChatClient(userPrompt);
    }

    private String callChatClient(String promptText) {
        log.info("Sende Prompt an Spring AI ChatClient: '{}'", promptText);
        try {
            String content = chatClient.prompt().user(promptText).call().content();
            log.info("Erfolgreiche Antwort vom Spring AI ChatClient erhalten.");
            log.debug("Antwort Inhalt: {}", content);
            return content;
        } catch (Exception e) {
            log.error("Fehler bei der Kommunikation mit dem Spring AI ChatClient: ", e);
            return "Fehler bei der Kommunikation mit dem LLM: " + e.getMessage();
        }
    }
}
