package jders13_objekte._01_objekteEinfuerung.anwendung2ObjektAutomitConstructor;

public class Auto {

    /* Beim <PERSON> eines Objekts kann mann den Constructor auswählen welchen man benutzen möchte
     * oder die IDE nutzt automatisch den Constructor der in jeweiligen Klasse gegeben ist. */

    String marke;
    String model;
    int jahr;
    String farbe;

    // Objekt ohne Parameter erstellen, werte sind nicht zugewiesen ALT + EINFÜGEN
    public Auto() {
        
    }

    // Objekt mit zwei Parametern erstellen, restlichen werte sind für int 0 und für String null
    public Auto(String mrk, String mdl){

        marke = mrk;
        model = mdl;
    }

    /*
    public Auto(){
        marke = "Mercedes";
        model = "E 390";
        farbe = "Schwarz";
        jahr = 2002;
    }
    */

    // Objekt mit allen Parametern
    public Auto(String mrk, String mdl, int j, String frb){

        marke = mrk;
        model = mdl;
        jahr = j;
        farbe = frb;
    }


}