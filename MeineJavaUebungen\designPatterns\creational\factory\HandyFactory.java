package designPatterns.creational.factory;

public class HandyFactory {

    public static Handy getHandy(String model, String marke, int groesse, int gewicht) {

        Handy handy;
        if ("s22".equalsIgnoreCase(model)) {

            handy=new S22(model,marke,groesse, gewicht );

        } else if ("S23".equalsIgnoreCase(model)) {

            handy = new S23(model, marke, groesse, gewicht);
        } else {

            throw new RuntimeException("Leider kein gültiges model");
        }

        return handy;
    }
}
