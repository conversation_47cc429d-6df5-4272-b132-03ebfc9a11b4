package ajava_se.klassen.c02_statischeInnereKlassen;

/**
 * C02_Wiederholung2: Fokus auf Zugriff auf nicht-statische Member aus statischer Klasse
 * 
 * Ursprünglicher Klassenname: C02_Wiederholung2
 */
public class C02_Wiederholung2 {

    private int zahl = 25;

    public static class StatischeInnere {
        public void ausgeben() {
            // Der Zugriff auf nicht-statische Member erfordert eine Instanz der äußeren Klasse.
            C02_Wiederholung2 aussen = new C02_Wiederholung2();
            System.out.println("Zahl aus statischer innerer Klasse: " + aussen.zahl);
        }
    }

    public static void main(String[] args) {
        C02_Wiederholung2.StatischeInnere staticInner = new C02_Wiederholung2.StatischeInnere();
        staticInner.ausgeben();
    }
}

