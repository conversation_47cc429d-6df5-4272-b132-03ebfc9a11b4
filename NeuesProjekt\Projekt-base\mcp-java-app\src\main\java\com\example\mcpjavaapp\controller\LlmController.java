package com.example.mcpjavaapp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
// import org.springframework.web.reactive.function.client.WebClient; // Nicht mehr direkt hier verwendet
import com.example.mcpjavaapp.model.PromptRequest;
import com.example.mcpjavaapp.service.OllamaService;
import reactor.core.publisher.Mono;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@RestController
public class LlmController {

    private static final Logger log = LoggerFactory.getLogger(LlmController.class);
    private final OllamaService ollamaService;

    @Autowired
    public LlmController(OllamaService ollamaService) {
        this.ollamaService = ollamaService;
    }

    @PostMapping("/api/llm/generate")
    public Mono<ResponseEntity<String>> generateResponse(@RequestBody PromptRequest requestPayload) {
        log.info("Anfrage an /api/llm/generate erhalten mit Prompt: {}", requestPayload.getPrompt());
        try {
            String serviceResponse = ollamaService.generateCompletion(requestPayload.getPrompt());
            if (serviceResponse.startsWith("Fehler:")) { // Einfache Prüfung auf Fehler vom Service
                log.warn("Fehler von OllamaService erhalten: {}", serviceResponse);
                // Wir könnten hier spezifischere Fehlercodes basierend auf dem Service-Fehler zurückgeben
                return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(serviceResponse));
            }
            log.info("Erfolgreiche Antwort für /api/llm/generate.");
            return Mono.just(ResponseEntity.ok(serviceResponse));
        } catch (Exception e) {
            log.error("Unerwarteter Fehler im LlmController bei /api/llm/generate: ", e);
            return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Ein interner Fehler ist aufgetreten."));
        }
    }
}
