package jders17_polymorphie.anwendung2InstanceOfWiederholung;

import java.util.ArrayList;

public class Test {
    public static void main(String[] args) {

        /*
         * Einen direkten Zugriff auf spezifische Eigenschaften und Methoden bietet
         * dieses Beispiel wie in unserer Anwendung1InstanceOf.
         * Dies bedeutet, dass Sie auf die spezifischen Funktionen und Daten einer bestimmten Klasse zugreifen können.
         * Dies ist nütz<PERSON>, wenn Sie genau wissen, welchen konkreten Klassentyp Sie haben
         * und spezifische Operationen darauf ausführen möchten.
         * ---- Viereck viereck1 =new Viereck(4.15);
         * ---- Kreis kreis1 = new Kreis(4.15);
         * Hier sind einige Szenarien, wie in unserem Beispiel in der Anwendung1InstanceOf genutzt angemessen sein kann:
         *
         * 1.Wenn Sie spezifische Operationen ausführen müssen, die nur für einen bestimmten Klassentyp relevant sind.
         * Zum Beispiel, wenn Sie eine Methode haben, die spezifische Berechnungen für ein Viereck
         * durchführt und Zugriff auf die Seitenlängen benötigt, wäre es sinnvoll, ein Objekt
         * vom Typ "Viereck" zu erstellen und darauf zuzugreifen.
         *
         * 2.Wenn Sie spezifische Eigenschaften ändern oder abrufen müssen, die nur für einen bestimmten
         * Klassentyp definiert sind. Wenn Sie beispielsweise den Radius eines Kreises ändern müssen,
         * wäre es sinnvoll, ein Objekt vom Typ "Kreis" zu erstellen und darauf zuzugreifen.
         *
         * 3.Wenn Sie bereits wissen, dass Sie nur mit einem bestimmten Klassentyp arbeiten und
         * keine Flexibilität für andere Klassentypen benötigen.
         */

        /* Hier sind einige Szenarien, in denen Beispiel 2 Anwendung2InstanceOfWiederholung angemessen sein kann:
         *
         * 1.Wenn Sie eine gemeinsame Schnittstelle für mehrere Klassentypen haben, die ähnliche
         * Operationen ausführen, aber unterschiedliche Implementierungen haben.
         * Durch die Verwendung der Oberklasse können Sie auf diese gemeinsame Schnittstelle
         * zugreifen und generische Operationen durchführen, ohne spezifisch auf jede Unterklasse
         * eingehen zu müssen. Dies erleichtert die Erweiterung des Codes, da Sie einfach neue
         * Unterklassen hinzufügen können, die die gleiche Schnittstelle implementieren.
         *
         * 2.Wenn Sie eine Liste von Objekten unterschiedlicher Unterklassen haben und generische
         * Operationen auf allen diesen Objekten ausführen möchten. Durch die Verwendung der
         * Oberklasse als Datentyp können Sie alle Objekte in der Liste behandeln,
         * unabhängig von ihrer konkreten Implementierung. Dies bietet Flexibilität und
         * ermöglicht es Ihnen, Code wiederzuverwenden und auf generische Weise mit den Objekten umzugehen.
         *
         * 3.Wenn Sie situationsabhängig zwischen verschiedenen Unterklassen wechseln müssen.
         * Durch die Verwendung der Oberklasse als Datentyp können Sie zur Laufzeit festlegen,
         * welche konkrete Unterklasse verwendet werden soll, basierend auf bestimmten Bedingungen
         * oder Logik. Dies ermöglicht Ihnen, flexibel zwischen verschiedenen Unterklassen
         * zu wechseln, ohne den Code anpassen zu müssen.
         * */
        // Wir erstellen nie ein Objekt der Klasse Form, aber wir geben nun die Eigenschaften
        // getInhalt&getUmfang der Klasse Form in unsere Arraylist (Siehe Klasse).
        Form form1 = new Viereck(4.15);
        Form form2 = new Kreis(4.15);
        Form form3 = new Kreis(5.0);
        Form form4 = new Viereck(5.0);
        Form form5 = new Kreis(7.25);

        /*
        ArrayList<Form> formen = new ArrayList<>();
        formen.add(form1);
        formen.add(form2);
        formInfosAusgeben(formen);
        */

        berechneForm(form1);
        berechneForm(form2);
        berechneForm(form3);
        berechneForm(form4);
        berechneForm(form5);

    }

    public static void umfangUndInhaltAusgeben(Form form) {

        System.out.println("Umfang : " + form.getUmfang());
        System.out.println("Inhalt : " + form.getInhalt());

    }

    // Hat nur den gleichen Namen wie unsere Array List die wir senden wollen
    public static void formInfosAusgeben(ArrayList<Form> formen) {

        for (Form f : formen) {

            System.out.println("Umfang : " + f.getUmfang());
            System.out.println("Inhalt : " + f.getInhalt());
            System.out.println();
        }
    }

    // Methode zur Ausgabe mit getInhalt&getUmfang für jeweilige Form
    public static void berechneForm(Form form) {

        if (form instanceof Viereck) {

            System.out.println("Viereck : ");
            System.out.println("Inhalt : " + form.getInhalt());
        } else if (form instanceof Kreis) {

            System.out.println("Kreis : ");
            System.out.println("Umfang : " + form.getUmfang());
            System.out.println("Inhalt : " + form.getInhalt());

        }

        System.out.println();
    }
}
