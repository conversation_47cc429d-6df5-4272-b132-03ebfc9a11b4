package jders06_01_einfuerungAnwendungDurchschnitt;

import java.util.Scanner;

public class EinfuerungsAnwendungTennaryOperator {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);

        System.out.print("Geben Sie Ihre Visum Note ein: ");
        double visum = sc.nextDouble();  // 80

        System.out.print("Geben Sie Ihre Final Note ein: ");
        double finall = sc.nextDouble();  // 100

        if (visum < 0 || finall < 0 || visum > 100 || finall > 100) {
            System.out.println("Die Eingabewerte müssen zwischen 0 und 100 liegen!");
        } else {
            double durchschnitt = visum * 0.4 + finall * 0.6;
            String buchstabe = (durchschnitt >= 90) ? "AA"
                    : (durchschnitt >= 80) ? "BB"
                    : (durchschnitt >= 70) ? "CC"
                    : (durchschnitt >= 60) ? "DD"
                    : (durchschnitt >= 0) ? "FF"
                    : "Es konnte kein passender Durchschnitt berechnet werden!";
            System.out.println("Ihr Durchschnitt: " + durchschnitt + " - Buchstabennote: " + buchstabe);
            // Ihr Durchschnitt: 92.0 - Buchstabennote: AA
        }

    }
}
