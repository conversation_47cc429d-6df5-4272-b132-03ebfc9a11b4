package ajava_se.klassen.c01_innereMitgliedsklassen;

// Die äußere Klasse, die auch als OuterClass bezeichnet wird
public class C01_AussenKlasse {
    byte idAussenKlasse; // Membervariable für die ID der äußeren Klasse
    String ort; // Membervariable für den Ort der äußeren Klasse

    // Konstruktor zur Initialisierung der Membervariablen
    public C01_AussenKlasse(byte id, String ort) {
        this.idAussenKlasse = id;
        this.ort = ort;
    }

    // Diese Methode erstellt ein Objekt der inneren Klasse und zeigt ihre Details an
    public void erzeugeInnereKlasseUndGebeDetailsAus(String ortInnereKlasse) {
        InnereKlasse innereKlasse = new InnereKlasse(); // Ein Objekt der inneren Klasse erstellen
        innereKlasse.ort = ortInnereKlasse; // Den Ort der inneren Klasse setzen
        zeigeInnereKlasseDetails(innereKlasse); // Die Details der inneren Klasse anzeigen
    }

    // Diese Methode zeigt die Details der inneren Klasse an
    public void zeigeInnereKlasseDetails(InnereKlasse innereKlasse) {
        innereKlasse.zeigeDetails("In innerer Klassenmethode"); // Korrigierter String
    }

    // Diese Methode zeigt ein Objekt der inneren Klasse an (nutzt jetzt toString())
    public void zeigeObjekt(InnereKlasse innereKlasse) {
        System.out.println("\nObjekt: " + innereKlasse);
    }

    // Diese Methode gibt den übergebenen Text aus
    public void druckeText(String text) { // Umbenannt von zeigeIdUndOrt
        System.out.println(text);
    }

    // Die innere Klasse, auch als InnerClass bezeichnet
    class InnereKlasse {
        private String ort; // Membervariable für den Ort der inneren Klasse

        // Diese Methode zeigt Details an und akzeptiert einen Ortsparameter
        private void zeigeDetails(String ort) {
            String idUndOrt = "\nID: " + idAussenKlasse; // ID der äußeren Klasse
            idUndOrt += "\nOrt: " + C01_AussenKlasse.this.ort; // Ort der äußeren Klasse
            idUndOrt += " -> " + this.ort; // Ort der inneren Klasse
            idUndOrt += " -> " + ort; // Übergebener Ort-Parameter

            C01_AussenKlasse.this.zeigeObjekt(this); // Objekt der inneren Klasse anzeigen
            druckeText(idUndOrt); // Aufruf der umbenannten Methode
        }

        // Überschriebene toString()-Methode für eine bessere Darstellung
        @Override
        public String toString() {
            return "InnereKlasse [ort=" + ort + ", bezogen auf AussenKlasse ID=" + idAussenKlasse + "]";
        }
    }
}
