<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code-Assistent</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        textarea {
            width: 100%;
            height: 300px;
            margin-bottom: 10px;
            font-family: monospace;
        }
        button {
            padding: 10px 15px;
            margin-right: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>KI-Code-Assistent</h1>
    <p><PERSON><PERSON><PERSON><PERSON>hren Java-Code ein und wählen Sie eine Aktion:</p>
    
    <textarea id="codeInput" placeholder="Java-Code hier eingeben..."></textarea>
    
    <div>
        <button onclick="analyzeCode()">Code analysieren</button>
        <button onclick="generateDocumentation()">Dokumentation generieren</button>
        <button onclick="improveCode()">Code verbessern</button>
    </div>
    
    <h2>Ergebnis:</h2>
    <div id="result">Die Ergebnisse werden hier angezeigt...</div>
    
    <script>
        async function sendRequest(endpoint, data) {
            console.log(`Sende Anfrage an /api/assistant/${endpoint} mit Daten:`, data);
            document.getElementById('result').textContent = 'Verarbeitung läuft...';
            
            try {
                const response = await fetch(`/api/assistant/${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                console.log('Antwort vom Server erhalten:', response);
                
                if (!response.ok) {
                    console.error('HTTP-Fehler! Status:', response.status, response.statusText);
                    const errorText = await response.text();
                    console.error('Fehlertext vom Server:', errorText);
                    throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}. Serverantwort: ${errorText}`);
                }
                
                const resultText = await response.text();
                console.log('Erfolgreiche Antwort (Text):', resultText);
                document.getElementById('result').textContent = resultText;
            } catch (error) {
                console.error('Fehler in sendRequest:', error);
                document.getElementById('result').textContent = `Fehler: ${error.message}`;
            }
        }
        
        function analyzeCode() {
            const code = document.getElementById('codeInput').value;
            sendRequest('analyze', { code });
        }
        
        function generateDocumentation() {
            const code = document.getElementById('codeInput').value;
            sendRequest('document', { code });
        }
        
        function improveCode() {
            const code = document.getElementById('codeInput').value;
            sendRequest('improve', { code });
        }
    </script>
</body>
</html>
