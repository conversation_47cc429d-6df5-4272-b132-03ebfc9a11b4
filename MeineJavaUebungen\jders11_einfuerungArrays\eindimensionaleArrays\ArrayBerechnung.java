package jders11_einfuerungArrays.eindimensionaleArrays;

import java.util.Scanner;

public class ArrayBerechnung {
    public static void main(String[] args) {

        Scanner sc = new Scanner(System.in);

        int arrayLaenge; // Variable zur Speicherung der Array-Länge
        int gesamt = 0; // Variable zur Speicherung der Gesamtsumme der Zahlen

        System.out.print("Wie viele Stellen soll das Array beinhalten : ");
        arrayLaenge = sc.nextInt(); // Benutzereingabe der Array-Länge

        int[] zahlen = new int[arrayLaenge]; // Array zur Speicherung der Zahlen erstellen

        for (int i = 0; i < zahlen.length; i++) {
            // Schleife zum Eingeben der Werte in das Array
            System.out.print((i + 1) + ". Wert eingeben : ");
            zahlen[i] = sc.nextInt(); // Benutzereingabe des aktuellen Werts
        }

        for (int i = 0; i < zahlen.length; i++) {
            // Schleife zur Berechnung der Gesamtsumme der Zahlen im Array
            gesamt = gesamt + zahlen[i]; // Aktuellen Wert zum Gesamt hinzufügen
        }

        System.out.println("Werte Addiert : " + gesamt); // Gesamtsumme ausgeben

        System.out.println("Durchschnitt der Zahlen : " + (gesamt / zahlen.length)); // Durchschnitt der Zahlen ausgeben

    }

}
      /*Die Variable arrayLaenge wird verwendet, um die Länge des Arrays zu speichern.
       *Die Variable gesamt wird initialisiert und zur Berechnung der Gesamtsumme der Zahlen verwendet.
       *Der Benutzer wird aufgefordert, die Länge des Arrays einzugeben.
       *Das Array zahlen wird mit der angegebenen Länge erstellt.
       *Eine Schleife wird verwendet, um Werte in das Array einzugeben. Der Benutzer wird aufgefordert, jeden Wert einzugeben.
       *Eine weitere Schleife wird verwendet, um die Gesamtsumme der Zahlen im Array zu berechnen, indem jeder Wert zum Gesamt addiert wird.
       *Die Gesamtsumme der Zahlen wird ausgegeben.
       *Der Durchschnitt der Zahlen wird berechnet und ausgegeben, indem die Gesamtsumme durch die Länge des Arrays geteilt wird.
       * */