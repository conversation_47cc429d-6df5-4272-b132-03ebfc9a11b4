package jders14_objektAnwendungen.anwendung2DatenübergabeDurchMethodenparameter;

import java.util.ArrayList;

public class StudentTest {
    public static void main(String[] args) {
        /* Informationen über die Studenten in der Anwendung ausgeben.
         * Um die Liste der Unterrichtsfächer des Studenten zu durchlaufen
         * wird innerhalb der unterrichtsFaecherZeigen()-Methode
         * eine Iteration über die uf-ArrayList stattfinden, die die Unterrichtsfächer
         * eines bestimmten Studenten enthält. In diesem Fall werden die Unterrichtsfächer
         * für jeden Studenten einzeln durchlaufen. um jedes Fach auszugeben. */

        /* In diesem speziellen Fall, in dem die Methode unterrichtsFaecherZeigen()
         * die Informationen über die Unterrichtsfächer eines Studenten ausgibt,
         * wird das Studentenobjekt als Parameter übergeben, um auf die relevanten Daten zuzugreifen.
         * Dies ermöglicht eine flexible und modulare Gestaltung des Codes, da die Methode unabhängig
         * von der konkreten Art der Speicherung der Studentendaten arbeitet*/

        // Hier erstellen wir unsre Leeren ArrayLists
        ArrayList<String> unterrichtsFaecher = new ArrayList<>();

        // Hier erstellen wir unsre vordefinierten Unterrichts Fächer
        unterrichtsFaecher.add("Mathematik");
        unterrichtsFaecher.add("Physik");
        unterrichtsFaecher.add("Chemie");
        unterrichtsFaecher.add("Biologie");

        // Student student1 = new Student("Jörn", "Decker",1995,"2333",null);
        Student student1 = new Student("Jörn", "Decker", 1995, "2333", unterrichtsFaecher);

        Student student2 = new Student("Björn", "Neckar", 1994, "2332", unterrichtsFaecher);

        /*zeigeInformationen(student1);
        System.out.println();
        zeigeInformationen(student2);*/

        unterrichtsFaecherZeigen(student1);
        System.out.println();
        unterrichtsFaecherZeigen(student2);

    }

    /*  Static, weil die Methode auf Klassenebene existiert und aufgerufen werden kann,
        ohne dass eine Instanz der Klasse erstellt werden muss. Dies ermöglicht den direkten
        Zugriff auf die Methode, indem der Klassenname verwendet wird, gefolgt von einem Punkt und dem Methodennamen.
        In diesem Fall wird die Methode auf einem Objekt vom Typ Student aufgerufen, das in der Variable std übergeben wird.*/
    public static void zeigeInformationen(Student std) {

        System.out.println("Vorname : " + std.getName());
        System.out.println("Nachname : " + std.getNachname());
        System.out.println("Geburtsjahr : " + std.getGeburtsjahr());
        System.out.println("Studenten Nummer : " + std.getSchuhlNummer());
        System.out.println("Teilnahme Fach : " + std.getUnterrichtsFach());
    }

    /* Die Methode unterrichtsFaecherZeigen(Student student) nimmt ein Studentenobjekt als Parameter entgegen.
     * Sie ruft die Methode getUnterrichtsFach() auf, um die ArrayList der Unterrichtsfächer
     * für den übergebenen Studenten abzurufen. Anschließend wird eine Schleife verwendet,
     * um jedes Unterrichtsfach in der ArrayList zu durchlaufen und auszugeben.
     * Durch diese Vorgehensweise wird eine Iteration über die verschiedenen Studentenobjekte ermöglicht,
     * um spezifische Informationen für jeden einzelnen Studenten abzurufen und zu verarbeiten.*/
    public static void unterrichtsFaecherZeigen(Student student) {

        /*String name = student.getName();
        int geburtsjahr = student.getGeburtsjahr();*/

        ArrayList<String> uf = student.getUnterrichtsFach();

        System.out.println(student.getName() + ", hat Teilnahme in : ");
        for (String unterricht : uf) {
            System.out.println(unterricht);
        }

        /*System.out.println("Name : " + name);
        System.out.println("Geburtsjahr : " + geburtsjahr);*/

        /*System.out.println("Unterrichts Fächer : ");
        System.out.println(student.getUnterrichtsFach());*/
    }
    /* Der Code geht davon aus, dass die Informationen über die Studenten bereits an anderer
     * Stelle im Programm initialisiert und gespeichert wurden. In diesem konkreten Fall werden
     * die Studentenobjekte student1 und student2 in der main()-Methode erstellt und
     * mit den entsprechenden Informationen initialisiert.
     *
     * Es ist möglich, dass die Informationen über die Studenten beispielsweise aus einer Datenbank,
     * einer Datei oder einer anderen externen Datenquelle stammen. In diesem Fall müsste
     * der Code angepasst werden, um die Daten abzurufen und in Studentenobjekte umzuwandeln,
     * bevor sie an die unterrichtsFaecherZeigen()-Methode übergeben werden.
     *
     * Die Art und Weise, wie die Informationen über die Studenten gespeichert werden, hängt von der
     * konkreten Implementierung ab und kann je nach Anwendungsfall unterschiedlich sein.
     * Es ist wichtig sicherzustellen, dass die Informationen über die Studenten in einer geeigneten
     * Datenstruktur gespeichert werden, auf die bei Bedarf zugegriffen werden kann. */

    /* In Java haben Objekte, die in einer Klasse erstellt werden, keine explizite Reihenfolge
     * basierend auf ihren Speicheradressen. Die Reihenfolge, in der die Objekte in einer Klasse erstellt
     * oder hinzugefügt werden, bestimmt nicht die Speicheradressen oder die Zugriffsreihenfolge auf die Objekte.
     *
     * Die Speicherung und Verwaltung von Objekten erfolgt im Speicherbereich, der als Heap bezeichnet wird.
     * Die genaue Speicherplatzierung und -verwaltung obliegt dem Java Virtual Machine (JVM)-Laufzeitsystem
     * und hängt von verschiedenen Faktoren ab, einschließlich der zugrunde liegenden Implementierung der JVM.
     *
     * Es ist wichtig zu beachten, dass in Java die Reihenfolge, in der Objekte in einer Klasse erstellt
     * oder hinzugefügt werden, keine Auswirkungen auf die Art und Weise hat, wie auf sie zugegriffen oder
     * auf sie verwiesen wird. Die Zugriffsreihenfolge auf Objekte in einer Klasse erfolgt normalerweise
     * durch den Namen des Objekts oder über andere Identifikationsmerkmale, die in der Klasse definiert sind.*/
}
