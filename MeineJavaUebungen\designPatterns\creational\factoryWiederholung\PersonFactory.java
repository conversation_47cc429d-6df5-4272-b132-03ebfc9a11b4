package designPatterns.creational.factoryWiederholung;

public class PersonFactory {

    /* Wir überprüfen, ob Geschlecht nicht leer ist, bevor die GeschlechtDivers-Klasse erstellt wird.
    Wenn Geschlecht leer ist, wird eine RuntimeException ausgelöst, um anzuzeigen, dass kein
    gültiges Geschlecht angegeben wurde. Andernfalls wird die entsprechende Person-Implementierung erstellt.*/

    // Diese Methode erstellt und gibt eine Instanz einer Person oder einer ihrer Unterklassen zurück,
    // basierend auf dem angegebenen Geschlecht, Vorname, Nachname und Geburtsjahr.
    public static Person getPerson(String geschlecht, String vorname, String nachname, int geburtsjahr) {

        Person person; // Deklaration einer Person-Variablen.

        // Überprüfung, ob das Geschlecht "Weiblich" ist, unabhä<PERSON><PERSON> von Groß- und Kleinschreibung.
        if ("Weib<PERSON>".equalsIgnoreCase(geschlecht)) {
            // Wenn das G<PERSON>chlecht weiblich ist, wird eine Frau erstellt.
            person = new Frau(geschlecht, vorname, nachname, geburtsjahr);

        } else if ("Männlich".equalsIgnoreCase(geschlecht)) {
            // Wenn das Geschlecht männlich ist, wird ein Mann erstellt.
            person = new Mann(geschlecht, vorname, nachname, geburtsjahr);

        } else if (!geschlecht.trim().isEmpty()) {
            // Wenn das Geschlecht weder männlich noch weiblich ist und nicht leer ist oder Leerzeichen hat,
            // wird eine Person mit diversen Geschlechtsmerkmalen erstellt.
            person = new Divers(geschlecht, vorname, nachname, geburtsjahr);

        } else {
            // Wenn das Geschlecht leer ist, wird eine Laufzeit-Ausnahme ausgelöst,
            // um anzuzeigen, dass kein gültiges Geschlecht angegeben wurde.
            throw new RuntimeException("Leider kein gültiges Geschlecht");
        }

        // Die erstellte Person oder eine Instanz einer ihrer Unterklassen wird zurückgegeben.
        return person;
    }
}
