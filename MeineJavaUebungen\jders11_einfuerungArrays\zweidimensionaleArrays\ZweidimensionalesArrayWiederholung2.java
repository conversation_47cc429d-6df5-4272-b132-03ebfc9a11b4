package jders11_einfuerungArrays.zweidimensionaleArrays;

public class ZweidimensionalesArrayWiederholung2 {

    public static void main(String[] args) {
        /*Die zusätzliche Bedingung in der zweiten else if-Anweisung ermöglicht das Setzen von "#" auf der Diagonalen Linie und ihrer Spiegelung.*/

        char[][] tabelle = new char[8][14];

        for (int i = 0; i < tabelle.length; i++) {

            for (int j = 0; j < tabelle[i].length; j++) {

                if (i == 0 || i == (tabelle.length - 1)) {
                    tabelle[i][j] = '#';  // Setzt # in die erste Zeile von links bis rechts und mit || auch in die letzte Zeile

                } else if (j == 0 || j == tabelle[i].length - 1) {
                    tabelle[i][j] = '1';  // Setzt 1 von der zweiten Zeile, erste Spalte bis letzte Spalte || und ihrer Spiegelung

                } else if (i == j || j == (tabelle[i].length - i - 1)) {
                    tabelle[i][j] = '0';  // Setzt 0 auf der Diagonalen Linie und ihrer Spiegelung

                } else {
                    tabelle[i][j] = '-';  // Setzt - für alle anderen Werte
                }

            }
        }

        for (int i = 0; i < tabelle.length; i++) {

            for (int j = 0; j < tabelle[i].length; j++) {

                System.out.print(tabelle[i][j] + " ");  // Gibt den Wert der aktuellen Zelle aus, gefolgt von einem Leerzeichen

            }
            System.out.println();  // Wechselt zur nächsten Zeile nach dem Ausgeben aller Spalten in der aktuellen Zeile
        }

    }
}
   /*
   * Die Berechnung tabelle[i].length - i - 1 wird verwendet, um die Spiegelungsdiagonale im Array zu ermitteln.
   * Es handelt sich um eine Methode, die den Abstand der aktuellen Zelle zur Spiegelungsdiagonalen berechnet.
   * Die Variable i repräsentiert den Index der aktuellen Zeile. tabelle[i].length gibt die Länge der aktuellen Zeile an,
   * also die Anzahl der Spalten in dieser Zeile.
   * Die Berechnung tabelle[i].length - i - 1 subtrahiert den aktuellen Zeilenindex i und 1 von der Länge der aktuellen Zeile.
   * Dadurch wird der Abstand der aktuellen Zelle zur Spiegelungs diagonalen ermittelt.
   *
   * Detaillierte Aufschlüsselung:
   * tabelle[i].length gibt die Anzahl der Spalten in der aktuellen Zeile i zurück.
   * i ist der Index der aktuellen Zeile.
   * 1 wird subtrahiert, um die Null-basierte Indexierung zu berücksichtigen.
   * Die Berechnung tabelle[i].length - i - 1 liefert den Index der Zelle auf der Spiegelungsdiagonalen, indem sie den Abstand der aktuellen Zelle zur Hauptdiagonalen ermittelt.
   *
   * Um es an einem Beispiel zu verdeutlichen:
   * Angenommen, wir haben ein Array tabelle mit einer Länge von 8 Zeilen und 14 Spalten.
   *
   * Für i = 0 (erste Zeile) ergibt sich tabelle[i].length - i - 1 = 14 - 0 - 1 = 13, was der Index der Zelle auf der Spiegelungsdiagonalen ist.
   * Für i = 1 (zweite Zeile) ergibt sich tabelle[i].length - i - 1 = 14 - 1 - 1 = 12, was ebenfalls der Index der Zelle auf der Spiegelungsdiagonalen ist.
   * Die Berechnung wird fortgesetzt, bis alle Zeilen des Arrays verarbeitet wurden.
   * Durch die Verwendung dieser Berechnung tabelle[i].length - i - 1 wird sichergestellt, dass die Zellen
   * auf der Spiegelungsdiagonalen des Arrays mit dem Symbol '0' markiert werden.
   * */