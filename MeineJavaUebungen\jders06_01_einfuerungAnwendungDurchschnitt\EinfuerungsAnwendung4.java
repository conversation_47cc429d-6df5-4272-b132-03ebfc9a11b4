package jders06_01_einfuerungAnwendungDurchschnitt;

import java.util.Scanner;

public class EinfuerungsAnwendung4 {
    public static void main(String[] args) {

        /* Eine Möglichkeit, das zu beheben, ist, die Prüfung der ungültigen Eingabe als erste Bedingung hinzuzufügen,
        bevor die anderen Bedingungen überprüft werden.
        So wird sichergestellt, dass ungültige Eingaben abgefangen werden,
        bevor die Note umgewandelt wird und die anderen Bedingungen überprüft werden. */

        Scanner sc = new Scanner(System.in);

        double visum;
        double finall;
        double durchschnitt;

        System.out.print("Geben sie ihre Visum Note ein : ");
        visum = sc.nextDouble();

        System.out.print("Geben sie ihre Final Note ein : ");
        finall = sc.nextDouble();

        if (visum < 0 || visum > 100 || finall < 0 || finall > 100) {
            System.out.println("Ungültige Eingabe. Bitte geben Sie Noten zwischen 0 und 100 ein.");
        } else {
            durchschnitt = visum * 0.4 + finall * 0.6;

            if (durchschnitt >= 90 && durchschnitt <= 100) {
                System.out.println("Buchstaben note : AA ");
            } else if (durchschnitt >= 80 && durchschnitt < 90) {
                System.out.println("Buchstaben note : BB ");
            } else if (durchschnitt >= 70 && durchschnitt < 80) {
                System.out.println("Buchstaben note : CC ");
            } else if (durchschnitt >= 60 && durchschnitt < 70) {
                System.out.println("Buchstaben note : DD ");
            } else if (durchschnitt >= 0 && durchschnitt < 60) {
                System.out.println("Buchstaben note : FF ");
            }
        }
    }
}
