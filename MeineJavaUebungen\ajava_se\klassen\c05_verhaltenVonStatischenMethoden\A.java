package ajava_se.klassen.c05_verhaltenVonStatischenMethoden;

/**
 * Basisklasse A mit einer statischen Methode.
 * Diese Klasse demonstriert das Verhalten von statischen Methoden in Vererbungshierarchien.
 */
class A {
    /**
     * Statische Methode, die eine Nachricht ausgibt.
     * Statische Methoden gehören zur Klasse selbst, nicht zu Instanzen der Klasse.
     * Sie können nicht überschrieben, sondern nur in Unterklassen versteckt werden (method hiding).
     */
    static void printMessage() {
        System.out.println("Hallo von A");
    }
}

