package jders05_kontrollstrukturen;

public class ForKontrolle2DirektBerechnungenAusfuerenUndAusgeben {
    public static void main(String[] args) {

        for (int x = 3, y = 5; x * y < 50; x++, y += 2) {
            System.out.println("Wert x : " + x + "Wert y : " + y);  // 3x Wert x : 3Wert y : 5Wert x : 4Wert y : 7

        }

        for (int x = 3, y = 5; x * y < 50; x++, y -= 2) {
            System.out.println("Wert x : " + x + "Wert y : " + y);  // max. bis zu int vol. von y. x : 32770Wert y : -65529
        }
    }
}
