package ders15_StringManipulations;

import java.util.Scanner;

public class C02_length {
    public static void main(String[] args) {

        String name = "<PERSON>";

        System.out.println("Namen Länge : " + name.length());  // 21

        // letzten Buchstaben ausgeben
        System.out.println("Letzter Buchstabe : " + name.charAt(name.length() - 1));  // r

        // vom letzten Buchstaben aus den 5. Buchstaben ausgeben
        System.out.println(name.charAt(name.length() - 5));  // l

        // Vom Nutzer den Namen holen und den mittleren Buchstaben ausgeben,
        // wenn die Länge vom Namen eine Grade-Zahl ist sollen die beiden mittleren Buchstaben ausgegeben werden.

        Scanner sc = new Scanner(System.in);
        System.out.print("Bitte geben sie ihren Namen ein : ");
        String eingegebenerName = sc.nextLine();

        // Wir Speichern den wert der Länge vom Namen in einer Variable
        int namensLaenge = eingegebenerName.length();

        if (namensLaenge % 2 == 0) {  // Wenn die Länge gerade ist, dann die beiden mittleren Buchstaben ausgeben
            // int mitte wird verwendet, um den Index des mittleren Buchstabens im eingegebenen Namen zu berechnen.
            int mitte = namensLaenge / 2;
            char mittlererBuchstabe1 = eingegebenerName.charAt(mitte - 1);
            char mittlererBuchstabe2 = eingegebenerName.charAt(mitte);
            System.out.println("Die beiden mittleren Buchstaben sind: " + mittlererBuchstabe1 + mittlererBuchstabe2);
        } else {  // Wenn die Länge ungerade ist, dann den mittleren Buchstaben ausgeben

            int mitte = namensLaenge / 2;
            char mittlererBuchstabe = eingegebenerName.charAt(mitte);
            System.out.println("Der mittlere Buchstabe ist: " + mittlererBuchstabe);
        }
    }
}
