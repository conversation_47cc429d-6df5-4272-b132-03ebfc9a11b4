package jders13_objekte._03_datenkapselung.Anwendung2SetterUndGetter;

public class Test {
    public static void main(String[] args) {

        Student student1 = new Student();

        Student student2 = new Student();

        // Instanz Student1
        student1.setVorname("<PERSON><PERSON><PERSON>");
        student1.setNachname("Bach");
        student1.setGeburtsjahr(1995);
        student1.setStudentenNummer("2321");

        // Instanz Student1
        student2.setVorname("Björn");
        student2.setNachname("Rathaus");
        student2.setGeburtsjahr(1996);
        student2.setStudentenNummer("2322");

        System.out.println("Name : " + student1.getVorname()+
                           ", Nachname : "+ student1.getNachname()+
                           ", Studenten Nummer : " + student1.getStudentenNummer()+
                           ", Geburtsjahr : "+student1.getGeburtsjahr());
        
    }
}
