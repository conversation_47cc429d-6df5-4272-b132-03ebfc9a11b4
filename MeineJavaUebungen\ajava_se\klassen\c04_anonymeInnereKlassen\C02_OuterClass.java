package ajava_se.klassen.c04_anonymeInnereKlassen;

public class C02_OuterClass {

    /*
    WICHTIG: Anonyme Klassen behalten Referenzen auf ihre umgebenden Objekte!
    Dies kann zu Memory-Leaks führen, wenn sie länger leben als die äußere Klasse.

    Häufige Anwendungen:
    - Event-Handler in GUIs (z.B. <PERSON>-<PERSON>licks)
    - Kurze Callback-Implementierungen
    - Adapter für Schnittstellen mit vielen Methoden
    - Temporäre Implementierungen für Tests

    Anonyme Klassen sind eine nützliche Möglichkeit, Implementierungen von Interfaces oder abstrakten Klassen zu erstellen.
    Anonyme Klassen haben jedoch einige Einschränkungen, die bei der Verwendung berücksichtigt werden müssen.

    Zusätzliche Details zu anonymen Klassen:
    -Anonyme Klassen können nicht als Klassennamen verwendet werden.
    -Anonyme Klassen können nicht als Überladungsparameter verwendet werden.
    -Anonyme Klassen können nicht als Rückgabewerte von Methoden verwendet werden.
    Erweitere Erläuterung:
    -Anonyme Klassen sind nicht als Eigenschaften einer Klasse deklariert. Sie werden innerhalb eines Ausdrucks definiert
     und können nur innerhalb dieses Ausdrucks verwendet werden.
    -Anonyme Klassen können innerhalb von Anweisungsblöcken, Methoden und statischen Initialisierungsblöcken definiert werden.
    -Anonyme Klassen können nicht als Schnittstellen deklariert werden.
    -Anonyme Klassen können auf Methoden und Variablen der äußeren Klasse zugreifen, die final sind.
    -Anonyme Klassen, die in einer statischen Methode deklariert sind, können keine
     Methoden der äußeren Klasse aufrufen, die ein Objekt der äußeren Klasse benötigen.
    -Anonyme Klassen können nicht als public, private oder static deklariert werden.


    Beispiele für die Verwendung von anonymen Klassen:
    -Anonyme Klassen können verwendet werden, um Implementierungen von Interfaces zu erstellen.
    -Anonyme Klassen können verwendet werden, um Implementierungen von abstrakten Klassen zu erstellen.
    -Anonyme Klassen können verwendet werden, um anonyme Listener zu erstellen.
    -Anonyme Klassen können verwendet werden, um anonyme Funktionen zu erstellen.

    Vorteile von anonymen Klassen:
    -Anonyme Klassen sind eine kompakte Möglichkeit, Implementierungen von Interfaces oder abstrakten Klassen zu erstellen.
    -Anonyme Klassen sind eine flexible Möglichkeit, anonyme Listener oder Funktionen zu erstellen.

    Nachteile von anonymen Klassen:
    -Anonyme Klassen sind nicht wiederverwendbar.
    -Anonyme Klassen können die Lesbarkeit des Codes verringern.*/

    public void method() {

        // Deklaration einer anonymen Klasse vom Typ Runnable
        Runnable runnable = new Runnable() {

            // Die run()-Methode der anonymen Klasse gibt die Nachricht "Hello, world!" aus
            @Override
            public void run() {
                // Thread-Namen abrufen und direkt verwenden
                String thread1 = Thread.currentThread().getName();
                System.out.println("Hello, world! " + thread1);
            }
        };
        /*In unserem Beispiel ist die anonyme Klasse die Implementierung des Runnable-Interfaces, die bei
          new Runnable() {...} deklariert wird. Die anonyme Klasse hat keinen Namen,
          da wir sie nur innerhalb dieses Ausdrucks verwenden.*/
        // Starten eines neuen Threads mit der anonymen Klasse Runnable als Parameter
        new Thread(runnable).start();
    }

    public static void main(String[] args) {

        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String thread2 = Thread.currentThread().getName();
                System.out.println("Merhabalar " + thread2);
            }
        });

        thread.start();
        C02_OuterClass outer = new C02_OuterClass();
        outer.method();
    }
}
