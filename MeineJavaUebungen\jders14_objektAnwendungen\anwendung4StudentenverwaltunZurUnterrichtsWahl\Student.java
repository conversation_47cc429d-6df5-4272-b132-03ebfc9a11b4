package jders14_objektAnwendungen.anwendung4StudentenverwaltunZurUnterrichtsWahl;

import java.util.ArrayList;

public class Student {

    private String vorname;

    private String nachname;

    private int geburtsjahr;

    private String studentenNummer;

    private ArrayList<String> unterrichtsFaecher;

    public Student() {

    }

    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer, ArrayList<String> unterrichtsFaecher) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
        this.studentenNummer = studentenNummer;
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    // Wichtig ist nur die Reihenfolge in der die Methoden an einer bestimmten Stelle aufgerufen werden.

    // Parameter fordert eine ArrayList<String> die wir übergeben wollen
    public void setUnterrichtsFaecher(ArrayList<String> unterrichtsFaecher) {
        // in dieser Klasse die Fächer sind this.unterrichtsFaecher = über parameter gegebene Liste "Gesendet"
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    // Parameter gibt eine ArrayList<String> zurück, und nimmt keine Parameterliste() an, deshalb (leer)
    public ArrayList<String> getUnterrichtsFaecher() {
        // return private unterrichtsFaecher aus der klasse
        return unterrichtsFaecher;
    }

    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    /*
    @Override
    public String toString() {
        return "Student{" +
                "vorname='" + vorname + '\'' +
                ", nachname='" + nachname + '\'' +
                ", geburtsjahr=" + geburtsjahr +
                ", studentenNummer='" + studentenNummer + '\'' +
                ", unterrichtsFaecher=" + unterrichtsFaecher +
                '}';
    }
    */

    /* Dieser Code implementiert die toString()-Methode für die Klasse Student.
     * Die Methode erstellt eine Zeichenkette, die die Werte der Instanzvariablen des Studentenobjekts enthält.
     *
     * Zuerst wird ein StringBuilder-Objekt erstellt, um den Inhalt der Zeichenkette zusammenzufügen.
     *
     * Dann werden die verschiedenen Teile der Zeichenkette schrittweise dem StringBuilder hinzugefügt.
     *
     * Jeder Teil wird mit der append()-Methode an den StringBuilder angehängt.
     *
     * Für die Unterrichtsfächer wird eine Schleife verwendet, um jedes Fach aus der Liste hinzuzufügen.
     * Wenn es weitere Fächer in der Liste gibt, wird ein Komma und ein Leerzeichen angehängt,
     * um die einzelnen Fächer voneinander zu trennen.
     *
     * 5. Schließlich wird der Inhalt des StringBuilder in einen String umgewandelt und zurückgegeben.
     *
     * Durch die Verwendung des StringBuilder wird der effiziente Aufbau der Zeichenkette erreicht,
     * da der StringBuilder intern den Inhalt verwalten kann, ohne mehrere Zwischenzeichenketten
     * erstellen zu müssen. Dies führt zu einer besseren Leistung bei der Erstellung der formatierten Zeichenkette
     *
     * Wenn append aufgerufen wird, wird der übergebene Wert an den bestehenden Inhalt des StringBuilder-Objekts angehängt.
     * "*null-Werte*": In Java repräsentiert der Wert null das Fehlen einer Referenz auf ein Objekt.
     * Es bedeutet, dass eine Variable keinen gültigen Wert enthält und nicht auf ein tatsächliches Objekt verweist.
     * Die Überprüfung auf null-Werte ist wichtig, um sicherzustellen, dass keine NullPointerException-Fehler auftreten,
     * wenn versucht wird, auf Eigenschaften oder Methoden eines null-Objekts zuzugreifen.
     * Durch die Überprüfung der null-Werte können geeignete Maßnahmen ergriffen werden, um sicherzustellen,
     * dass die Zeichenkette korrekt und ohne Fehler erstellt wird.
     *
     *
     * Am Anfang mit sb.append bedeutet dies, dass der Wert an den StringBuilder-Objekt sb angehängt wird.
     * Der StringBuilder speichert und verwaltet den gesamten Inhalt der Zeichenkette.
     *
     * Am Ende mit .append bedeutet dies, dass der Wert an das StringBuilder-Objekt angehängt wird,
     * auf das die Methode append aufgerufen wird. In deinem Beispiel sieht das
     * so aus: unterrichtsFaecher.append(...). Hier wird der Wert der unterrichtsFaecher-Variable
     * an das StringBuilder-Objekt unterrichtsFaecher angehängt.
     * */
    @Override
    public String toString() {
        /*Der Code verwendet den StringBuilder, um eine Zeichenkette schrittweise zusammenzufügen. Diese Vorgehensweise wird aus mehreren Gründen empfohlen:
         *
         * Effiziente Speicherung: Der StringBuilder ermöglicht effizientes Hinzufügen von Text zu einer Zeichenkette,
         * da er intern einen Puffer verwendet, um den Text zu speichern. Dadurch wird verhindert, dass für jede Textänderung eine neue Zeichenkette erstellt werden muss, was zu unnötigem Speicherplatzverbrauch führen würde.
         *
         * Performance: Bei der Verwendung des StringBuilder werden die Textänderungen direkt im Puffer vorgenommen,
         * was schneller ist als die Konkatenation von Zeichenketten mit dem +-Operator. Dies ist insbesondere relevant,
         * wenn viele Textänderungen oder Schleifen durchgeführt werden, wie im vorliegenden Code.
         *
         * Lesbarkeit und Wartbarkeit: Die Verwendung des StringBuilder verbessert die Lesbarkeit des Codes,
         * da der Codeabschnitt, der den Aufbau der Zeichenkette behandelt, strukturiert ist.
         * Es ist leichter nachvollziehbar, welche Teile der Zeichenkette wo hinzugefügt werden.
         *
         * Flexibilität: Der StringBuilder bietet zusätzliche Methoden zum Einfügen, Ersetzen oder Löschen von Text,
         * falls solche Operationen erforderlich sind.
         *
         * Thread-Sicherheit: Im Gegensatz zu StringBuffer ist StringBuilder nicht thread-sicher,
         *  was in den meisten Fällen keine Auswirkungen hat, wenn der Code nicht in einem multithreaded Umfeld verwendet wird.
         * Durch die Verwendung von StringBuilder statt StringBuffer wird auf die zusätzliche Synchronisierung verzichtet
         * und dadurch potenziell bessere Performance erzielt.
         *
         * Zusammenfassend wird der StringBuilder verwendet, um effizient, leistungsfähig und lesbar eine
         * Zeichenkette zusammenzufügen. Es ist eine bewährte Vorgehensweise, insbesondere wenn viele
         * Textänderungen vorgenommen werden oder der Code in Schleifen ausgeführt wird.*/
        StringBuilder sb = new StringBuilder();
        sb.append("Student{"); // Fügt den Anfang der Zeichenkette hinzu
        sb.append("vorname='").append(vorname).append('\''); // Fügt den Vornamen hinzu
        sb.append(", nachname='").append(nachname).append('\''); // Fügt den Nachnamen hinzu
        sb.append(", geburtsjahr=").append(geburtsjahr); // Fügt das Geburtsjahr hinzu
        sb.append(", studentenNummer='").append(studentenNummer).append('\''); // Fügt die Studentennummer hinzu
        sb.append(", unterrichtsFaecher=["); // Fügt den Anfang der Unterrichtsfächer-Liste hinzu

        if (unterrichtsFaecher != null) { // Überprüft, ob die Liste der Unterrichtsfächer nicht null ist
            for (int i = 0; i < unterrichtsFaecher.size(); i++) {
                String fach = unterrichtsFaecher.get(i); // Aktuelles Fach an der Indexposition i abrufen

                if (fach != null) { // Überprüft, ob das Fach nicht null ist
                    sb.append(fach); // Fügt das Fach zur Zeichenkette hinzu
                } else {
                    sb.append("N/A"); // Fügt einen Platzhalterwert "N/A" für null-Fächer hinzu
                }

                if (i != unterrichtsFaecher.size() - 1) {
                    sb.append(", "); // Fügt ein Komma und ein Leerzeichen hinzu, wenn es weitere Fächer gibt
                }
            }
        }

        sb.append("leer]}"); // Fügt das Ende der Zeichenkette hinzu
        return sb.toString(); // Konvertiert den StringBuilder in einen String und gibt ihn zurück
    }

}
