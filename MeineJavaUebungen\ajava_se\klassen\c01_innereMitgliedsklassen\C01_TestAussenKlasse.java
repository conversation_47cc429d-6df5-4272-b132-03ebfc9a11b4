package ajava_se.klassen.c01_innereMitgliedsklassen;

public class C01_TestAussenKlasse {

    public static void main(String[] args)

    /*Die innere Klasse kann auf die Membervariable werte zugreifen, da sie in ihrem eigenen Kontext sichtbar ist.
    Beim <PERSON> einer Instanz der inneren Klasse wird das Array werte auf dem Heap-Speicher erstellt und
    bleibt so lange bestehen, wie die Instanz der inneren Klasse in Gebrauch ist.

    Der Hauptunterschied besteht in der Sichtbarkeit und dem Zugriffsbereich der Arrays. Das äußere Array ist globaler
    und kann von der äußeren Klasse sowie von anderen Klassen, die eine Instanz der äußeren Klasse haben, verwendet werden.
    Das innere Array ist lokaler und kann nur von Instanzen der inneren Klasse verwendet werden.
    Dies hilft, den Bereich und die Verantwortlichkeiten der verschiedenen Teile des Codes zu organisieren.*/
    {
        // Verwende den neuen Konstruktor von C01_AussenKlasse
        C01_AussenKlasse ak = new C01_AussenKlasse((byte)5, "In outer class"); // das oc-Objekt
        // Die Zuweisungen von ort und idAussenKlasse sind jetzt im Konstruktor
        ak.erzeugeInnereKlasseUndGebeDetailsAus("In inner class");
    }
}
