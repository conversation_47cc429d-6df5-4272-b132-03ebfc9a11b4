package jders08_mathMethoden;

import java.util.Random;

public class RandomGenerator {
    public static void main(String[] args) {

        /* Dieser Code generiert eine Normalverteilung von Zufallszahlen zwischen 84 und 200
         * mit einem Mittelwert von 142 und einer Standardabweichung von 30. Die Bedingungen sorgen dafür,
         * dass alle generierten Zahlen innerhalb des Bereichs bleiben. */


        int n = 1000; // An<PERSON><PERSON> der Zahlen, die generiert werden sollen
        int min = 84; // untere Grenze
        int max = 200; // obere Grenze
        double mean = 142; // Mittelwert
        double stdev = 30; // Standardabweichung

        Random rand = new Random(); // neues Random-Objekt "rand" wird erstellt

        for (int i = 0; i < n; i++) {  // Es wird eine for-Schleife gestartet, die "n" mal durchlaufen wird.
             /*
             * Eine neue Zufallszahl "num" wird erzeugt, indem die Methode "nextGaussian" aufgerufen wird
             * und mit dem Mittelwert und der Standardabweichung multipliziert und dann auf eine Ganzzahl umgewandelt wird.
             * */
            int num = (int) (rand.nextGaussian() * stdev + mean);
            /*Wenn die Zufallszahl kleiner als die untere Grenze "min" ist, wird sie auf den Wert von "min" gesetzt.*/
            if (num < min) {
                num = min;
              /*Wenn die Zufallszahl größer als die obere Grenze "max" ist, wird sie auf den Wert von "max" gesetzt */
            } else if (num > max) {
                num = max;
            }
            System.out.println(num);
        }
    }
}
