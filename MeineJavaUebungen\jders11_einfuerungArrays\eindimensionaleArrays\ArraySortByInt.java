package jders11_einfuerungArrays.eindimensionaleArrays;

public class ArraySortByInt {
    public static void main(String[] args) {

        int [] array = {2,5,7,1,3,22,11,33,14,77};

        /*
        Arrays.sort(array);

        for (int i : array){

            System.out.print(i);
        }
        */

        sortiereArray(array); // Aufruf der sortiereArray-Methode

        serienWerteAusgeben(array);
        System.out.println();

        /*
        * Die Schleife for (int i = 0; i < array.length; i++) in der main-Methode wird verwendet,
        * um über das Array zu iterieren und die Werte auszugeben. Die Bedingung i != 0 wird verwendet,
        * um vor jedem Element außer dem ersten ein Leerzeichen auszugeben.*/
        for (int i = 0; i < array.length; i++) {
            if (i != 0) {
                System.out.print(" "); // Leerzeichen vor jedem Element außer dem ersten
            }
            System.out.print(array[i]); // Ausgabe des aktuellen Elements
        }

    }

    // Implementierung des Bubble Sort Algorithmus
    public static void sortiereArray(int[] array) {

        int n = array.length;
        for (int i = 0; i < n - 1; i++) {
            for (int j = 0; j < n - i - 1; j++) {
                // Vergleiche benachbarte Elemente und tausche sie, wenn sie in falscher Reihenfolge sind
                if (array[j] > array[j + 1]) {
                    int temp = array[j];
                    array[j] = array[j + 1];
                    array[j + 1] = temp;
                }
            }
        }

        /* Die sortiereArray()-Methode ist eine eigene Implementierung des Bubble Sort Algorithmus,
         * um das Array in aufsteigender Reihenfolge zu sortieren.
         *
         * Die äußere Schleife for wird verwendet, um die Anzahl der Durchläufe festzulegen.
         *
         * Die innere Schleife for wird verwendet, um benachbarte
         * Elemente zu vergleichen und sie gegebenenfalls zu tauschen.
         */
    }

    public static void serienWerteAusgeben(int[] z) {

        for (int i = 0; i < z.length; i++) {
            System.out.print(z[i]+" ");
        }
        /*
        * Die Schleife for (int i = 0; i < z.length; i++) in der serienWerteAusgeben-Methode
        * wird verwendet, um über das Array zu iterieren und die Werte auszugeben.
        * Jedes Element wird mit einem Leerzeichen am Ende ausgegeben.*/
    }

}
