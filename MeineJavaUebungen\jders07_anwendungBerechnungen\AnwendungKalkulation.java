package jders07_anwendungBerechnungen;

import java.util.Scanner;

public class AnwendungKalkulation {

    /*
    Mathematische Probleme:
    Eine f(x,y) Funktion soll eingegebe Werte folgend berechnen

    x > 0, y > 0  f(x,y) = x^2 + y^2 + y^2 + 2*x+y + 5

    x < 0, y > 0  f(x,y) = 2 * x * y + 150

    x > 0, y < 0  f(x,y) = 2 * x + 5 * y + 10

    */
    public static void main(String[] args) {

        Scanner sc = new Scanner(System.in);

        double x;
        double y;
        double erg;

        System.out.print("Geben sie den x Wert Ein : ");
        x = sc.nextDouble();

        System.out.print("Geben sie ein y Wert Ein : ");
        y = sc.nextDouble();

        if (x > 0 && y > 0) {

            erg = x * x + y * y + 2 * x * y + 5;
        } else if (x < 0 && y > 0) {

            erg = 2 * x * y + 150;
        } else if (x > 0 && y < 0) {

            erg = 2 * x + 5 * y + 10;
        }else {
            System.out.println("Eingegebenen Werte x und y passen zu keinem Mathematischem Problem ");
            erg = 0;
        }

        System.out.println("Unser Ergebnis : " + erg);  // Zu lösen ohne vorherige Wertzuweisung Else{erg=0;
    }
}
