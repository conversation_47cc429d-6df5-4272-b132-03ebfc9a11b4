package effectiveJava.effectiveJava01.item1;

public class Item1Test {
    public static void main(String[] args) {

        // Den geforderten Parameter für boolean (kalt:) ist es unmöglich ohne die Essen-Klasse zu wissen
        // Essen essen1 = new Essen(kalt:)
        Essen essen = Essen.kaltEssen("Eis");

        /* Beispiels wollen wir nur 37 Jahre alte Spieler als Neuzugang in die Mannschaft
        FCBarcelona fcBarcelona1 = new FCBarcelona("Messi", 37);
        FCBarcelona fcBarcelona2 = new FCBarcelona("Ronaldo", 37);
        Anstatt dies auf die weise zu tun, können wir es so lösen in dem wir eine static Methode schreiben
        */

        // Erstelle direkt 37 Jahre alten Spieler durch unsre neuSpieler37Produzieren Methode
        FCBarcelona fcb = FCBarcelona.neuSpieler37Produzieren("Ronaldinho");

        // eine weitere Unklarheit durch Override ist auch für unsere Wahl ein Problem
        //FCBarcelona fcb2 = new FCBarcelona(neuZugangTrainer);
        //FCBarcelona fcb2 = new FCBarcelona(neuZugangSpieler,alter);


    }
}
