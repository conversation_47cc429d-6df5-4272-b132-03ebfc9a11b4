package ders03;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert die Verwendung verschiedener Scanner-Methoden zum Einlesen
 * von Benutzerdaten und die Formatierung der Ausgabe.
 *
 * Das Programm:
 * 1. Liest Name, Nachname und Alter vom Benutzer ein
 * 2. Gibt eine formatierte Zusammenfassung aus (erster Buchstabe des Namens + Nachname + Alter)
 * 3. Ruft eine separate Methode zur detaillierten Ausgabe aller Daten auf
 *
 * Wichtige Konzepte:
 * - Verwendung von nextLine() zum Einlesen von Textzeilen
 * - Verwendung von nextInt() zum Einlesen von ganzen Zahlen
 * - Formatierung von Ausgaben
 * - Methodenaufruf mit Parametern
 */
public class C04_Scanner {
    /**
     * Die Hauptmethode liest persönliche Daten vom Benutzer ein und gibt sie in verschiedenen Formaten aus.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        /*
         * Aufgabe: Vom Benutzer Name, Nachname und Alter einlesen und ausgeben in dem Format:
         * Erster Buchstabe des Namens + Nachname + Alter
         * Beispiel: N Nachname, 42
         */

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scan = new Scanner(System.in);

        // Name einlesen
        System.out.println("Bitte geben Sie Ihren Vornamen ein:");
        String name = scan.nextLine();

        // Nachname einlesen
        System.out.println("Bitte geben Sie Ihren Nachnamen ein:");
        String nachName = scan.nextLine();

        // Alter einlesen
        System.out.println("Bitte geben Sie Ihr Alter ein:");
        int alter = scan.nextInt();

        // Formatierte Kurzausgabe der eingegebenen Daten
        // Format: Erster Buchstabe des Namens + Nachname + Alter
        System.out.println("Eingegebene Daten: " + name.charAt(0) + " " + nachName + ", " + alter);

        // Aufruf der Methode für die detaillierte Ausgabe
        OtherOutput(name, nachName, alter);  // Beachte: Methodenname beginnt mit Großbuchstaben

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scan.close();
    }


    /**
     * Diese Methode gibt die übergebenen persönlichen Daten in einem detaillierten Format aus.
     *
     * Erwartete Ausgabe:
     * Name : Hans
     * Nachname : Werner
     * Alter : 28
     * Alles Erfolgreich!
     *
     * @param name Der Vorname der Person
     * @param nachName Der Nachname der Person
     * @param alter Das Alter der Person
     */
    public static void OtherOutput(String name, String nachName, int alter) {
        // Formatierte Ausgabe aller Daten mit Zeilenumbrüchen (\n)
        System.out.println(
                "Name : " + name +
                "\nNachname : " + nachName +
                "\nAlter : " + alter +
                "\nAlles Erfolgreich!");
    }

    /**
     * WICHTIGER HINWEIS ZU SCANNER-METHODEN:
     *
     * Man sollte next() und nextLine() vorsichtig kombinieren, da sie unterschiedliche
     * Verarbeitungsweisen für die Eingabe haben:
     *
     * 1. next() liest nur bis zum nächsten Leerzeichen oder Zeilenumbruch
     * 2. nextLine() liest die gesamte Zeile bis zum Zeilenumbruch
     *
     * Probleme entstehen besonders in dieser Reihenfolge:
     * - Wenn nach nextInt(), nextDouble() etc. ein nextLine() folgt
     * - Diese numerischen Methoden lesen nur die Zahl, lassen aber den Zeilenumbruch im Eingabepuffer
     * - Der folgende nextLine() liest dann nur diesen Zeilenumbruch und nicht die gewünschte Benutzereingabe
     *
     * Lösungen:
     * 1. Ein zusätzliches scan.nextLine() nach numerischen Eingaben einfügen, um den Zeilenumbruch zu konsumieren
     * 2. Konsistent nur nextLine() verwenden und Strings bei Bedarf in andere Datentypen konvertieren
     *    (z.B. Integer.parseInt(scan.nextLine()) statt scan.nextInt())
     *
     * In diesem Beispiel tritt das Problem nicht auf, da nextInt() am Ende steht und kein weiterer
     * nextLine()-Aufruf folgt.
     */
}
