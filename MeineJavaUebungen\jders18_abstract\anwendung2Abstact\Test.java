package jders18_abstract.anwendung2Abstact;

public class Test {
    public static void main(String[] args) {

        /* Definition: Eine abstrakte Klasse ist eine Klasse, die nicht direkt instanziiert werden kann.
           Sie wird verwendet, um allgemeine Konzepte zu definieren, die von anderen Klassen implementiert werden können.
           Merkmale:
          -Abstrakte Klassen können Attribute, Methoden und Konstruktoren enthalten.
          -Abstrakte Methoden müssen von abgeleiteten Klassen implementiert werden.
          -Nicht-abstrakte Methoden können von abgeleiteten Klassen überschrieben werden.
          -Abstrakte Klassen können von anderen Klassen erben.*/

        /*Durch die Verwendung von Methoden wie infosAusgeben() und spezifischAudiInfosAusgeben()
        können wir den Code modularer gestalten und Informationen über die Fahrzeuge
        auf unterschiedliche Weise ausgeben, je nach Bedarf.*/

        // Ein Audi-Objekt erstellen und mit Werten initialisieren
        Audi audi1 = new Audi("Rot", "Limousine", 1460, 500);

        // Mercedes-Objekte erstellen und mit Werten initialisieren
        Mercedes mercedes1 = new Mercedes("Schwarz", "Limousine", 1660, "CLA");
        Mercedes mercedes2 = new Mercedes("Grau", "Limousine", 2250, "S");

        // Ausgabe des Mercedes-Objekts über die Standard toString()-Methode der jeweiligen Klasse
        System.out.println(mercedes1);
        System.out.println(mercedes2);

        // Ausgabe des Audi-Objekts über die Standard toString()-Methode der jeweiligen Klasse
        System.out.println(audi1);

        System.out.println("-----------------------------------------------------------------------------------------");

        // Aufruf der Methode infosAusgeben für das Mercedes-Objekt
        infosAusgeben(mercedes1);
        infosAusgeben(mercedes2);

        // Aufruf der Methode infosAusgeben für das Audi-Objekt
        infosAusgeben(audi1);

        System.out.println("-----------------------------------------------------------------------------------------");

        // Aufruf der spezifischen Methode spezifischAudiInfosAusgeben für das Audi-Objekt
        spezifischAudiInfosAusgeben(audi1);

        // Aufruf der spezifischen Methode spezifischMercedesInfosAusgeben für das Mercedes-Objekt
        spezifischMercedesInfosAusgeben(mercedes1);
        spezifischMercedesInfosAusgeben(mercedes2);

    }

    // Methode, um gemeinsame Eigenschaften des Fahrzeugs ohne Details auszugeben
    public static void infosAusgeben(Fahrzeug fahrzeug){

        System.out.println("Farbe: " + fahrzeug.getFarbe() +
                ", Fahrzeug Art: " + fahrzeug.getFahrzeugArt() +
                ", Gewicht: " + fahrzeug.getGewicht() +
                ", Verbrauch: " + fahrzeug.literVerbrauch());

    }

    // Methode, um spezifische Details für Audi auszugeben
    public static void spezifischAudiInfosAusgeben(Audi audi){

        System.out.println("Farbe: " + audi.getFarbe() +
                ", Fahrzeug Art: " + audi.getFahrzeugArt() +
                ", Gewicht: " + audi.getGewicht() +
                ", Verbrauch: " + audi.literVerbrauch() +
                ", Drehmoment: " + audi.getDrehmoment());
    }

    // Methode, um spezifische Details für Mercedes auszugeben
    public static void spezifischMercedesInfosAusgeben(Mercedes mercedes){

        System.out.println("Farbe: " + mercedes.getFarbe() +
                ", Fahrzeug Art: " + mercedes.getFahrzeugArt() +
                ", Gewicht: " + mercedes.getGewicht() +
                ", Verbrauch: " + mercedes.literVerbrauch() +
                ", Modell: " + mercedes.getModel());
    }

}
