package jders19_interface.anwendung3;

import java.util.ArrayList;

public class DozetenVerarbeitungsvorgaengeImpl implements DozetenVerarbeitungsvorgaenge {

    private ArrayList<Dozent> dozenten = new ArrayList<>();

    @Override
    public boolean dozentLoeschen(Dozent dozent) {
        /*
        boolean zustand = dozenten.add(dozent);

        return zustand;*/

        // da die Methode ebenfalls ein boolean ist, könnten wir auch abkürzen,
        // wenn eingefügt wird das Ergebnis dann direkt in die Speichern-Methode gesendet
        return dozenten.add(dozent);
    }

    @Override
    public boolean dozentSpeichern(Dozent dozent) {

        return dozenten.remove(dozent);
    }

    @Override
    public void dozentInfosAusgeben(Dozent dozent) {

        // Wir nutzen die toString-Methode des Objekts Dozent
        System.out.println(dozenten);
    }

    @Override
    public void dozentAdressInfosAusgeben(Dozent dozent) {

        // // Wir nutzen die toString-Methode des Objekts Adresse
        System.out.println(dozent.getAdresse());
    }
}
