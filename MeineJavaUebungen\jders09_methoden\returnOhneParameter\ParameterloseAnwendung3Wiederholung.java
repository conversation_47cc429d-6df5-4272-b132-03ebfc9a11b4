package jders09_methoden.returnOhneParameter;

import java.util.Scanner;

public class ParameterloseAnwendung3Wiederholung {    public static void main(String[] args) {

    String kommenderString = takeNameNachname();

    System.out.println("Willkommen "+kommenderString);

}

    public static String takeNameNachname() {

        Scanner sc = new Scanner(System.in);

        String name;
        String nachname;

        System.out.print("Geben sie ihren Namen ein : ");
        name = sc.next();

        System.out.println("Geben sie ihren Nachnamen ein : ");
        nachname = sc.nextLine();

        return name + " " + nachname;
    }

}
