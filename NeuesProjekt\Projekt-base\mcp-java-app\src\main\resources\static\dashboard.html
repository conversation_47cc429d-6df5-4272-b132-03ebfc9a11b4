<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring AI 1.0 GA & MCP Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .status-bar {
            background: #27ae60;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .api-section {
            margin-bottom: 40px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .api-header {
            background: #34495e;
            color: white;
            padding: 15px 20px;
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .api-content {
            padding: 20px;
        }
        
        .endpoint-group {
            margin-bottom: 25px;
        }
        
        .endpoint {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        
        .method {
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
            margin-right: 15px;
            min-width: 60px;
            text-align: center;
        }
        
        .get { background: #27ae60; }
        .post { background: #e74c3c; }
        
        .url {
            flex: 1;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .test-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .test-btn:hover {
            background: #2980b9;
        }
        
        .interactive-section {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .submit-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1em;
            transition: background 0.3s;
        }
        
        .submit-btn:hover {
            background: #229954;
        }
        
        .result-area {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        
        .loading {
            color: #f39c12;
            font-style: italic;
        }
        
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .success {
            color: #27ae60;
        }
        
        .tabs {
            display: flex;
            background: #ecf0f1;
            border-radius: 10px 10px 0 0;
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            background: #bdc3c7;
            color: #2c3e50;
            font-weight: bold;
            transition: background 0.3s;
        }
        
        .tab.active {
            background: #3498db;
            color: white;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
            background: white;
            border-radius: 0 0 10px 10px;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Spring AI 1.0 GA & MCP Dashboard</h1>
            <p>Vollständige Java-LLM-Integration mit Model Context Protocol</p>
        </div>
        
        <div class="status-bar" id="statusBar">
            ✅ Anwendung läuft erfolgreich - Beide APIs verfügbar
        </div>
        
        <div class="main-content">
            <!-- API Übersicht -->
            <div class="api-section">
                <div class="api-header">📡 Verfügbare API-Endpunkte</div>
                <div class="api-content">
                    <div class="endpoint-group">
                        <h3>Basis-API (Port 8080)</h3>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span class="url">/api/test</span>
                            <button class="test-btn" onclick="testEndpoint('/api/test')">Test</button>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span class="url">/api/generate?prompt=...</span>
                            <button class="test-btn" onclick="testGenerate()">Test</button>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span class="url">/api/analyze</span>
                            <button class="test-btn" onclick="showTab('analyze')">Test</button>
                        </div>
                    </div>
                    
                    <div class="endpoint-group">
                        <h3>MCP Tools (Port 8080)</h3>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span class="url">/api/document</span>
                            <button class="test-btn" onclick="showTab('document')">Test</button>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span class="url">/api/improve</span>
                            <button class="test-btn" onclick="showTab('mcp')">Test</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Interaktive Tests -->
            <div class="interactive-section">
                <div class="tabs">
                    <div class="tab active" onclick="showTab('generate')">🤖 Text Generierung</div>
                    <div class="tab" onclick="showTab('analyze')">🔍 Code Analyse</div>
                    <div class="tab" onclick="showTab('document')">📝 Dokumentation</div>
                    <div class="tab" onclick="showTab('mcp')">⚙️ MCP Tools</div>
                </div>
                
                <!-- Text Generierung -->
                <div class="tab-content active" id="generate">
                    <h3>🤖 Text Generierung mit Ollama</h3>
                    <div class="form-group">
                        <label for="prompt">Prompt:</label>
                        <textarea id="prompt" placeholder="Geben Sie Ihren Prompt ein...">Erkläre mir das Model Context Protocol in einfachen Worten</textarea>
                    </div>
                    <div class="form-group">
                        <label>API-Typ:</label>
                        <p style="color: #27ae60; font-weight: bold;">Spring AI & MCP Integration (Port 8080)</p>
                    </div>
                    <button class="submit-btn" onclick="generateText()">🚀 Generieren</button>
                    <div class="result-area" id="generateResult">Ergebnis wird hier angezeigt...</div>
                </div>
                
                <!-- Code Analyse -->
                <div class="tab-content" id="analyze">
                    <h3>🔍 Code Analyse</h3>
                    <div class="form-group">
                        <label for="codeAnalyze">Java Code:</label>
                        <textarea id="codeAnalyze" placeholder="Fügen Sie Ihren Java-Code hier ein...">public class Example {
    public void method() {
        String str = null;
        System.out.println(str.length());
    }
}</textarea>
                    </div>
                    <button class="submit-btn" onclick="analyzeCode()">🔍 Analysieren</button>
                    <div class="result-area" id="analyzeResult">Analyse-Ergebnis wird hier angezeigt...</div>
                </div>
                
                <!-- Dokumentation -->
                <div class="tab-content" id="document">
                    <h3>📝 Dokumentation Generierung</h3>
                    <div class="form-group">
                        <label for="codeDocument">Java Code:</label>
                        <textarea id="codeDocument" placeholder="Fügen Sie Ihren Java-Code hier ein...">public class Calculator {
    public int add(int a, int b) {
        return a + b;
    }
}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="docFormat">Format:</label>
                        <select id="docFormat">
                            <option value="JavaDoc">JavaDoc</option>
                            <option value="Markdown">Markdown</option>
                            <option value="HTML">HTML</option>
                        </select>
                    </div>
                    <button class="submit-btn" onclick="generateDocumentation()">📝 Dokumentation erstellen</button>
                    <div class="result-area" id="documentResult">Dokumentation wird hier angezeigt...</div>
                </div>
                
                <!-- MCP Tools -->
                <div class="tab-content" id="mcp">
                    <h3>⚙️ MCP Tools (SDK 0.9.0 Style)</h3>
                    <div class="form-group">
                        <label for="mcpCode">Java Code:</label>
                        <textarea id="mcpCode" placeholder="Fügen Sie Ihren Java-Code hier ein...">public class Service {
    private String data;
    
    public void processData() {
        // TODO: Implementierung
    }
}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="mcpTool">MCP Tool:</label>
                        <select id="mcpTool">
                            <option value="analyze">Code Analyzer</option>
                            <option value="document">Documentation Generator</option>
                            <option value="improve">Code Improver</option>
                        </select>
                    </div>
                    <button class="submit-btn" onclick="executeMcpTool()">⚙️ MCP Tool ausführen</button>
                    <div class="result-area" id="mcpResult">MCP-Ergebnis wird hier angezeigt...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab-Funktionalität
        function showTab(tabName) {
            // Alle Tabs und Inhalte deaktivieren
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Aktiven Tab und Inhalt aktivieren
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        }
        
        // Einfache Endpunkt-Tests
        async function testEndpoint(url) {
            try {
                const response = await fetch(url);
                const data = await response.text();
                alert(`✅ Erfolg!\n\nURL: ${url}\nAntwort: ${data}`);
            } catch (error) {
                alert(`❌ Fehler!\n\nURL: ${url}\nFehler: ${error.message}`);
            }
        }
        
        // Text-Generierung
        async function generateText() {
            const prompt = document.getElementById('prompt').value;
            const apiType = document.getElementById('apiType').value;
            const resultArea = document.getElementById('generateResult');
            
            if (!prompt.trim()) {
                alert('Bitte geben Sie einen Prompt ein!');
                return;
            }
            
            resultArea.textContent = '⏳ Generiere Text...';
            resultArea.className = 'result-area loading';
            
            try {
                let url = `/api/generate?prompt=${encodeURIComponent(prompt)}`;
                
                const response = await fetch(url);
                const data = await response.text();
                
                resultArea.textContent = data;
                resultArea.className = 'result-area success';
            } catch (error) {
                resultArea.textContent = `❌ Fehler: ${error.message}`;
                resultArea.className = 'result-area error';
            }
        }
        
        // Status-Check beim Laden
        async function checkStatus() {
            try {
                const response1 = await fetch('/api/test');

                if (response1.ok) {
                    document.getElementById('statusBar').innerHTML = '✅ Spring AI & MCP Anwendung läuft erfolgreich auf Port 8080';
                } else {
                    document.getElementById('statusBar').innerHTML = '❌ API nicht erreichbar';
                }
            } catch (error) {
                document.getElementById('statusBar').innerHTML = '❌ Verbindungsfehler: ' + error.message;
            }
        }
        
        // Code-Analyse
        async function analyzeCode() {
            const code = document.getElementById('codeAnalyze').value;
            const resultArea = document.getElementById('analyzeResult');

            if (!code.trim()) {
                alert('Bitte geben Sie Code ein!');
                return;
            }

            resultArea.textContent = '⏳ Analysiere Code...';
            resultArea.className = 'result-area loading';

            try {
                const formData = new FormData();
                formData.append('code', code);

                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.text();
                resultArea.textContent = data;
                resultArea.className = 'result-area success';
            } catch (error) {
                resultArea.textContent = `❌ Fehler: ${error.message}`;
                resultArea.className = 'result-area error';
            }
        }

        // Dokumentation generieren
        async function generateDocumentation() {
            const code = document.getElementById('codeDocument').value;
            const format = document.getElementById('docFormat').value;
            const resultArea = document.getElementById('documentResult');

            if (!code.trim()) {
                alert('Bitte geben Sie Code ein!');
                return;
            }

            resultArea.textContent = '⏳ Generiere Dokumentation...';
            resultArea.className = 'result-area loading';

            try {
                const formData = new FormData();
                formData.append('code', code);
                formData.append('format', format);

                const response = await fetch('/api/document', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.text();
                resultArea.textContent = data;
                resultArea.className = 'result-area success';
            } catch (error) {
                resultArea.textContent = `❌ Fehler: ${error.message}`;
                resultArea.className = 'result-area error';
            }
        }

        // MCP Tool ausführen
        async function executeMcpTool() {
            const code = document.getElementById('mcpCode').value;
            const tool = document.getElementById('mcpTool').value;
            const resultArea = document.getElementById('mcpResult');

            if (!code.trim()) {
                alert('Bitte geben Sie Code ein!');
                return;
            }

            resultArea.textContent = '⏳ Führe MCP Tool aus...';
            resultArea.className = 'result-area loading';

            try {
                const formData = new FormData();
                formData.append('code', code);

                let endpoint = '/api/improve'; // Standard
                if (tool === 'analyze') endpoint = '/api/analyze';
                if (tool === 'document') endpoint = '/api/document';

                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.text();
                resultArea.textContent = data;
                resultArea.className = 'result-area success';
            } catch (error) {
                resultArea.textContent = `❌ Fehler: ${error.message}`;
                resultArea.className = 'result-area error';
            }
        }

        // Status beim Laden prüfen
        window.onload = checkStatus;
    </script>
</body>
</html>
