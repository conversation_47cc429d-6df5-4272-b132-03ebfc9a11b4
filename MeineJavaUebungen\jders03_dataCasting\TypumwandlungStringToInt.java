package jders03_dataCasting;

public class TypumwandlungStringToInt {
    public static void main(String[] args) {
        String zahl1 = "15";

        String zahl2 = "42";

        String gesamt = zahl1 + zahl2;
        //  or System.out.println(zahl1 + zahl2 + " = " + gesamt);
        System.out.println("Gesamt : " + gesamt);  // 1542

        // String to int
        int zahl1Umwandlung = Integer.parseInt(zahl1);

        int zahl2Umwandlung = Integer.parseInt(zahl2);

        int gesamtZahl = zahl1Umwandlung + zahl2Umwandlung;

        System.out.println("Gesamtergebnis : " + gesamtZahl);  // Gesamtergebnis : 57

        // Eine weitere Möglichkeit zur Umwandlung ist die Methode mit denselben Zweck
        int zahl3Umwandlung = Integer.valueOf(zahl1);              // wenn wir einen String zwischen fügen erhalten ein wir Integer

        int zahl4Umwandlung = Integer.valueOf(zahl2);

        // int = geasamtZahl2; legen wir nicht an da wir es anders lösen wollen
        // in unserem Beispiel da Variable gesamtZahl bereits erstellt wurde so

        gesamtZahl = zahl3Umwandlung + zahl4Umwandlung;

        System.out.println("Gesamt Zahl : " + gesamtZahl); // Gesamt Zahl : 57

        // String to float

        float Zahl1UmwandlungFloat = Float.valueOf(zahl2);
        float Zahl2UmwandlungFloat = Float.valueOf(zahl1);



    }
}
