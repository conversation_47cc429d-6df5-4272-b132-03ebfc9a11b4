package ajava_se.klassen.c04_anonymeInnereKlassen;

import java.awt.*;

public class C01_BesondererPunkt {

    // In diesem Beispiel wird eine anonyme innere Klasse verwendet, um die toString()-Methode
    // der Klasse Point für ein spezifisches Objekt zu überschreiben.
    // Die anonyme Klasse erweitert Point und wird direkt bei der Instanziierung definiert.
    public static void main(String[] args) {
        // Erzeugen eines neuen Point-Objekts mit den Koordinaten 17, 4
        // und gleichzeitiger Definition einer anonymen inneren Klasse, die Point erweitert.
        Point p = new Point(17, 4) {
            // Optional: serialVersionUID - Ein Identifikator für die Serialisierung.
            // Obwohl anonyme Klassen serialisiert werden können (was ihre vollwertige Natur zeigt),
            // ist dies in der Praxis seltener und für dieses Beispiel nicht funktional notwendig.
            // Wir fügen sie hier nur zur Demonstration hinzu, dass es möglich ist.
            private static final long serialVersionUID = 1L; // Standardwert

            // Überschreiben der toString()-Methode von Point (geerbt von Object),
            // um eine benutzerdefinierte Darstellung des Punktes zu erhalten.
            @Override
            public String toString() {
                // Gibt die Koordinaten im Format "x/y" zurück.
                return x + "/" + y;
            }
        }; // Das Semikolon schließt die gesamte Anweisung (Variablendeklaration und Initialisierung inkl. anonymer Klasse) ab.

        // Gibt das Point-Objekt auf der Konsole aus.
        // Da wir toString() in der anonymen Klasse überschrieben haben,
        // wird diese spezielle Implementierung für das Objekt 'p' verwendet.
        System.out.println(p); // Erwartete Ausgabe: 17/4
    }
}
