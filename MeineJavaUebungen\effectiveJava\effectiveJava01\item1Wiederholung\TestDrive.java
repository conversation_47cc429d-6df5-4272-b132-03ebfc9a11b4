package effectiveJava.effectiveJava01.item1Wiederholung;

public class TestDrive {

    public static void main(String[] args) {
        try {
            // Erstelle verschiedene Produkte mithilfe der Factory-Methode
            Product product1 = Product.createProduct("Smartphone", 199.99);
            Product product2 = Product.createProduct("Laptop", 599.99);
            Product product3 = Product.createProduct("Headphones", 19.99);

            // Gib die Produkte aus
            System.out.println("Product 1: " + product1);
            System.out.println("Product 2: " + product2);
            System.out.println("Product 3: " + product3);

            // Versuche ein Produkt mit ungültigen Parametern zu erstellen
            // Dies sollte eine IllegalArgumentException auslösen
            Product invalidProduct = Product.createProduct("", -10.0);
        } catch (IllegalArgumentException e) {
            System.out.println("Fehler beim Erstellen eines ungültigen Produkts: " + e.getMessage());
        }
    }
}
