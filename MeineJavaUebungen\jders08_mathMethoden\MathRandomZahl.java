package jders08_mathMethoden;

import java.util.Random;

public class MathRandomZahl {
    public static void main(String[] args) {

        /*Im Code wird die Methode nextInt(int bound) der Klasse Random verwendet,
        * um eine Zufallszahl zwischen 0 (inklusive) und dem übergebenen Argument bound (exklusive) zu generieren.
        * Da wir eine Zufallszahl zwischen 5 (inklusive) und 10 (inklusive) haben wollen,
        * können wir bound auf 6 setzen, um Zufallszahlen zwischen 0 und 5 (inklusive) zu generieren.
        * Durch Addition von 5 erhalten wir dann Zufallszahlen zwischen 5 und 10 (inklusive).
        * Der Ausdruck x.nextInt(6) erzeugt also eine Zufallszahl zwischen 0 und 5, die dann
        * durch Addition von 5 auf den gewünschten Bereich verschoben wird.
        * */

        Random x = new Random();

        int a = x.nextInt(6) + 5;

        System.out.println(a);

    }
}
