package jders06_03_einfuerungAnwendungZahlenAddieren;

public class AlleZahlenAddierenDoWhile {
    public static void main(String[] args) {

        // Gesamtzahl von allen Zahlen ausgeben

        int a = 0;
        int gesamt = 0;

        do {
            // Mann kann gesamt = gesamt + a; oder kurze Schreibweise: gesamt += a;
            gesamt += a;
            a++;

        } while (a <= 50);
        System.out.println(gesamt);
    }
}
