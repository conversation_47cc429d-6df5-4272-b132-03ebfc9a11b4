package jders21_ordnerVerarbeitungen.anwendung5Stream;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;

public class DateiSchreiben {

    private ObjectOutputStream objectOutputStream;

    public boolean dateiErstellen(String dateiName) {

        String dateiPfad = "C:/Users/<USER>/Desktop/Projeler/";

        try {
            // entweder Pfad oder direkt filename aus wählen
            FileOutputStream fileOutputStream = new FileOutputStream(dateiPfad + dateiName + ".abc");

            // wir könnten statt ein weiteres catch, auch hier ein weiteren try-catch im try Block einsetzen
            objectOutputStream = new ObjectOutputStream(fileOutputStream);

            return true;

        } catch (FileNotFoundException e) {

            System.out.println("FileOutputStream Fehler : " + e);
        } catch (IOException e) {

            System.err.println("ObjectOutputStream Fehler!");
            System.err.println("Fehler : " + e);
        }

        return false;
    }

    // zum Speichern werden wir ObjectOutputStream einsetzen
    public void dateiStudentSpeichern(Student student) {

        try {

            objectOutputStream.writeObject(student);

        } catch (IOException e) {

            System.out.println("Beim speichern eines Studenten ist ein Fehler aufgetreten!");
            System.err.println("Fehler : " + e);
        }
    }

    public void dateiSchliessen() {
        // wenn das Schließen aus irgendeinem Grund nicht automatisch im try-catch geschehen ist
        if (objectOutputStream != null) {
            try {

                objectOutputStream.close();

            } catch (IOException e) {

                System.err.println("Beim Schließen von ObjectOutputStream ist ein Fehler aufgetreten!");
                System.err.println("Fehler : " + e);
            }
        }
    }
}
