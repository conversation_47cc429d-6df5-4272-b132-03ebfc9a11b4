package jders21_ordnerVerarbeitungen.anwendung1WiederholungUndKorrektur;

import java.io.IOException;
import java.io.PrintWriter;

public class OrdnerVerarbeitungenWiederholung {
    public static void main(String[] args) {

        // Der try-with-resources-Block wird verwendet, um sicherzustellen, dass die Datei geschlossen wird,
        // auch wenn ein Fehler auftritt. Dies geschieht, indem die Datei in die Ressourcenliste aufgenommen wird
        // und die Ressourcen automatisch geschlossen werden, wenn der try-Block abgeschlossen ist.
        try (PrintWriter writer = new PrintWriter("C:/Users/<USER>/Desktop/Projeler/Dokument2.txt")) {
            writer.println("Hello, world!");
            writer.flush(); // Optional, um sicherzustellen, dass die Daten geschrieben werden
            // Die Methode checkError() wird verwendet, um zu überprüfen, ob ein <PERSON>hler beim <PERSON> in die Datei aufgetreten ist.
            // Wenn ein Fehler aufgetreten ist, wird die Ausgabe Fehler beim Schreiben der Datei. ausgegeben.
            if (writer.checkError()) {
                /* Der PrintWriter wirft normalerweise keine IOException, wenn das Schreiben in die Datei fehlschlägt.
                Stattdessen setzt der PrintWriter intern einen Fehlerstatus, der später über die Methode checkError() überprüft werden kann.
                Die Methode checkError() prüft, ob während des Schreibvorgangs ein Fehler aufgetreten ist.
                Wenn wir jedoch den PrintWriter innerhalb eines Try-with-Resources-Blocks verwenden, wird er automatisch geschlossen,
                wenn der Block verlassen wird. Wenn beim Schließen des PrintWriter ein Fehler auftritt, wird eine IOException ausgelöst
                und wir würden die Meldung "Fehler beim Öffnen oder Schließen der Datei." sehen.
                Um den Fehlerstatus des PrintWriter zu überprüfen, könnten wir den PrintWriter außerhalb des Try-with-Resources-Blocks
                deklarieren und ihn nach der Überprüfung manuell schließen. Was wir noch im Package: anwendung1WiederholungFehlerUeberpruefung sehen werden*/
                System.out.println("Fehler beim Schreiben der Datei.");
            } else {
                System.out.println("Daten erfolgreich in die Datei geschrieben.");
            }
        } catch (IOException e) {
            System.out.println("Fehler beim Öffnen oder Schließen der Datei.");
        }

    }

}
