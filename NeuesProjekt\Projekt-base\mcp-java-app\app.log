
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-05-20T22:31:14.673+02:00  INFO 14392 --- [           main] c.e.mcpjavaapp.McpJavaAppApplication     : Starting McpJavaAppApplication v0.0.1-SNAPSHOT using Java 21.0.6 with PID 14392 (C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\target\mcp-java-app-0.0.1-SNAPSHOT.jar started by IL-pc in C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app)
2025-05-20T22:31:14.676+02:00 DEBUG 14392 --- [           main] c.e.mcpjavaapp.McpJavaAppApplication     : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-05-20T22:31:14.676+02:00  INFO 14392 --- [           main] c.e.mcpjavaapp.McpJavaAppApplication     : No active profile set, falling back to 1 default profile: "default"
2025-05-20T22:31:16.264+02:00  INFO 14392 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 9090 (http)
2025-05-20T22:31:16.279+02:00  INFO 14392 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-20T22:31:16.279+02:00  INFO 14392 --- [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-05-20T22:31:16.341+02:00  INFO 14392 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-20T22:31:16.341+02:00  INFO 14392 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1559 ms
2025-05-20T22:31:16.980+02:00  INFO 14392 --- [           main] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page: class path resource [static/index.html]
2025-05-20T22:31:17.550+02:00  INFO 14392 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 9090 (http) with context path ''
2025-05-20T22:31:17.565+02:00  INFO 14392 --- [           main] c.e.mcpjavaapp.McpJavaAppApplication     : Started McpJavaAppApplication in 3.5 seconds (process running for 4.118)
