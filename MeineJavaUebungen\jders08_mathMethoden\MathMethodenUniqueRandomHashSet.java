package jders08_mathMethoden;

import java.util.HashSet;

public class MathMethodenUniqueRandomHashSet {
    public static void main(String[] args) {
        /*
        Wir Verwenden die HashSet-Klasse, die Math.random-Methode und die generierten
        Zufallszahlen werden direkt in der while-Schleife ausgegeben.

        Dieser Code verwendet eine HashSet-Datenstruktur, um sicherzustellen, dass jede
        generierte Zahl einzigartig ist. Der Code generiert eine Zufallszahl zwischen 1 und 20
        (einschließlich 1 und 20) und fügt sie dem HashSet hinzu, falls diese nicht schon vorhanden ist.
        Der Prozess wird so lange wiederholt, bis 10 einzigartige Zahlen generiert wurden.
        Die Ausgabe erfolgt in aufsteigender Reihenfolge der Zufallszahlen, die generiert werden.
        */

        HashSet<Integer> uniqueNumbers = new HashSet<Integer>();// Erstelle ein neues HashSet, um einzigartige Zahlen zu speichern
        int count = 1;// Initialisiere einen Zähler für die Anzahl der generierten einzigartigen Zahlen

        while (uniqueNumbers.size() < 10) {                     // Schleife, bis 10 einzigartige Zahlen generiert wurden
            int randomNum = (int) (Math.random() * 20 + 1);     // Generiere eine Zufallszahl zwischen 1 und 20
            if (!uniqueNumbers.contains(randomNum)) {           // Überprüfe, ob die generierte Zahl bereits im HashSet vorhanden ist
                uniqueNumbers.add(randomNum);                   // Wenn die Zahl nicht im HashSet ist, füge sie hinzu und gib sie aus
                System.out.println(count + ". Zufallszahl : " + randomNum);
                count++;                              // Inkrementiere den Zähler für die Anzahl der generierten einzigartigen Zahlen
            }
        }

    }
}

