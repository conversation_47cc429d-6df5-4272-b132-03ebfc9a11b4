package jders17_polymorphie.anwendung2InstanceOf;

import java.util.ArrayList;

public class Test {
    public static void main(String[] args) {

        // Wir erstellen unsere Anwendung ohne abstract oder Interface einzusetzen

        Viereck viereck1 = new Viereck(4.15);
        Viereck viereck2 = new Viereck(3);
        // <PERSON><PERSON>m Constructor benötigen wir viereck.setSeite();

        Kreis kreis1 = new Kreis(4.15);
        Kreis kreis2 = new Kreis(5.12);


        System.out.println("viereck1 Inhalt : " + viereck1.getInhalt());  // 17.222500000000004
        System.out.println("viereck1 Umfang : " + viereck1.getUmfang());  // 16.6

        System.out.println("kreis1 Inhalt : " + kreis1.getInhalt() +
                "\nkreis1 Umfang : " + kreis1.getUmfang());  // 54.10607947645022 | 26.075219024795285
        System.out.println("---------------------------------------------------------------------------------");

        /*
        System.out.println("viereck1:");
        viereckUmfangUndInhaltAusgeben(viereck1);
        System.out.println("viereck2:");
        viereckUmfangUndInhaltAusgeben(viereck2);
        System.out.println();
        keisUmfangUndInhaltAusgeben(kreis1);
        keisUmfangUndInhaltAusgeben(kreis2);
        */

        umfangUndInhaltAusgeben(viereck2);
        System.out.println();
        umfangUndInhaltAusgeben(kreis2);
        System.out.println();
        umfangUndInhaltAusgeben(viereck1);
        System.out.println();
        umfangUndInhaltAusgeben(viereck1);
        System.out.println("------------------------------AUSGEBE-LISTE---------------------------------");

        // Was wir noch machen könnten, anstatt alles einzeln zu senden
        ArrayList<Form> formen = new ArrayList<>();

        formen.add(kreis1);
        formen.add(kreis1);
        formen.add(viereck1);
        formen.add(viereck2);

        formInfosAusgeben(formen);

    }

    /*
    // Zu Übungszwecken ohne die Oberklasse einzubeziehen was ein Nachteil Effect hat mit mehr Formen
    public static void viereckUmfangUndInhaltAusgeben(Viereck viereck) {

        System.out.println("Umfang : " + viereck.getUmfang());
        System.out.println("Inhalt : " + viereck.getInhalt());
    }

    public static void keisUmfangUndInhaltAusgeben(Kreis kreis) {

        System.out.println("Umfang : " + kreis.getUmfang());
        System.out.println("Inhalt : " + kreis.getInhalt());
    }
    */

    public static void umfangUndInhaltAusgeben(Form form) {

        System.out.println("Umfang : " + form.getUmfang());
        System.out.println("Inhalt : " + form.getInhalt());

    }

    // Hat nur den gleichen Namen wie unsere Array List die wir senden wollen
    public static void formInfosAusgeben(ArrayList<Form> formen) {

        for (Form f : formen) {

            System.out.println("Umfang : " + f.getUmfang());
            System.out.println("Inhalt : " + f.getInhalt());
            System.out.println();
        }
    }

}
