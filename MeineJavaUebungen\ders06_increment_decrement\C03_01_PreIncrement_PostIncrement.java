package ders06_increment_decrement;

/**
 * Diese Klasse demonstriert den Unterschied zwischen Pre-Inkrement (++variable)
 * und Post-Inkrement (variable++) in Java, insbesondere bei der Verwendung
 * innerhalb von Ausgabeanweisungen.
 *
 * Wichtige Konzepte:
 * - Post-Inkrement (variable++): Erst wird der aktuelle Wert verwendet, dann erhöht
 * - Pre-Inkrement (++variable): Erst wird der <PERSON> erhöht, dann verwendet
 * - Auswirkungen bei Ausgaben: Der Zeitpunkt der Erhöhung beeinflusst den ausgegebenen Wert
 *
 * Die Klasse zeigt, wie sich Pre- und Post-Inkrement unterschiedlich verhalten,
 * wenn sie direkt in System.out.println()-Anweisungen verwendet werden.
 */
public class C03_01_PreIncrement_PostIncrement {
    /**
     * Die Hauptmethode demonstriert den Unterschied zwischen Pre-Inkrement und Post-Inkrement
     * bei der Verwendung in Ausgabeanweisungen.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        System.out.println("Demonstration des Unterschieds zwischen Pre-Inkrement und Post-Inkrement");
        System.out.println("bei der Verwendung in Ausgabeanweisungen");

        // Initialisierung der Variablen
        int zahl = 10;
        System.out.println("\nAusgangswert: zahl = " + zahl);

        // BEISPIEL 1: Post-Inkrement als eigenständige Anweisung
        System.out.println("\nBEISPIEL 1: Post-Inkrement als eigenständige Anweisung");
        zahl++;  // Erhöht zahl um 1 (auf 11)
        System.out.println("Nach 'zahl++;': zahl = " + zahl);  // 11

        // BEISPIEL 2: Pre-Inkrement als eigenständige Anweisung
        System.out.println("\nBEISPIEL 2: Pre-Inkrement als eigenständige Anweisung");
        ++zahl;  // Erhöht zahl um 1 (auf 12)
        System.out.println("Nach '++zahl;': zahl = " + zahl);  // 12

        // BEISPIEL 3: Post-Inkrement innerhalb einer Ausgabeanweisung
        System.out.println("\nBEISPIEL 3: Post-Inkrement innerhalb einer Ausgabeanweisung");
        System.out.println("System.out.println(zahl++); gibt aus: " + zahl++);
        // Erst wird der aktuelle Wert von zahl (12) ausgegeben, dann wird zahl um 1 erhöht (auf 13)
        System.out.println("Nach der Ausgabe ist zahl = " + zahl);  // 13

        // BEISPIEL 4: Pre-Inkrement innerhalb einer Ausgabeanweisung
        System.out.println("\nBEISPIEL 4: Pre-Inkrement innerhalb einer Ausgabeanweisung");
        System.out.println("System.out.println(++zahl); gibt aus: " + ++zahl);
        // Erst wird zahl um 1 erhöht (auf 14), dann wird dieser neue Wert ausgegeben
        System.out.println("Nach der Ausgabe ist zahl = " + zahl);  // 14

        // ZUSAMMENFASSUNG
        System.out.println("\nZUSAMMENFASSUNG:");
        System.out.println("1. Als eigenständige Anweisung gibt es keinen funktionalen Unterschied");
        System.out.println("   zwischen zahl++ und ++zahl - beide erhöhen den Wert um 1.");
        System.out.println("2. Bei Verwendung in Ausdrücken (z.B. in Ausgabeanweisungen oder Zuweisungen):");
        System.out.println("   - Post-Inkrement (zahl++): Erst wird der aktuelle Wert verwendet, dann erhöht");
        System.out.println("   - Pre-Inkrement (++zahl): Erst wird der Wert erhöht, dann verwendet");

        /*
         * WICHTIG: Der Unterschied zwischen Pre- und Post-Inkrement wird nur sichtbar,
         * wenn der Wert im selben Ausdruck verwendet wird (z.B. bei einer Ausgabe oder Zuweisung).
         *
         * Bei eigenständigen Anweisungen wie "zahl++;" oder "++zahl;" gibt es keinen
         * funktionalen Unterschied - beide erhöhen den Wert um 1.
         */
    }
}
