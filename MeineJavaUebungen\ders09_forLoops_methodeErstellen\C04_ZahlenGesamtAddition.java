package ders09_forLoops_methodeErstellen;

import java.util.Scanner;

public class C04_ZahlenGesamtAddition {

    public static void main(String[] args) {
        //  Erhalten Sie eine positive ganze Zahl vom Benutzer,
        //  geben Sie die Summe der Zahlen addiert aus.

        Scanner scanner = new Scanner(System.in);
        System.out.println("Bitte geben Sie eine positive Ganzzahl ein");
        int eingegebeneZahl = scanner.nextInt();
        
        int digitsTotal = 0;
        int ziffer = 0;

        for (int i = eingegebeneZahl; i > 0; i /= 10) {

            ziffer = i % 10;
            digitsTotal += ziffer;

        }

        System.out.println(eingegebeneZahl + "Summe der Ziffern der Zahl : " + digitsTotal);


        // Lösen wir die gleiche Frage, indem wir die Anzahl der Ziffern wiederholen

        digitsTotal = 0;
        ziffer = 0;
        int zifferZahl = (eingegebeneZahl + "").length();
        int zahl = eingegebeneZahl;

        // 1234 Ziffernzahl : 4

        for (int i = 1; i <= zifferZahl; i++) { // Loop Schleife entsprechend der Anzahl der Ziffern

            ziffer = zahl % 10;
            digitsTotal += ziffer;
            zahl /= 10;

        }
        System.out.println(eingegebeneZahl + " Gesamtsumme der Ziffern von der Zahl : " + zifferZahl);

    }
}

