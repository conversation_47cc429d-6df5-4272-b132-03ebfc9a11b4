package jders10_BeispielAnwendungen.jders10_anwendungBanking;

import java.util.Scanner;

public class AnwendungBanking {

    static Scanner sc = new Scanner(System.in);        // Global nutzbar durch static für static Methoden

    static int userKontostand = 11110; // Wenn Kontostand kein Wert zugewiesen bekommt, können wir Beispielsweise beim Einzahlen nicht den Wert mit Addieren.

    public static void main(String[] args) {

        String userNameNachname;

        int mAuswahl;

        userNameNachname = takeNameNachname();  // kann glich oder anderes benannt werden wie genutzte Methode benannt werden, da Methode nur den Wert schickt

        do {

            menu(userNameNachname);

            mAuswahl = takeMenuAuswahl();

            auswahlKontrolle(mAuswahl);

            if (mAuswahl == 4) {

                break;
            }

            menuWiederkaehren();

        } while (mAuswahl != 4);

    }

    public static void auswahlKontrolle(int userAuswahl) {

        if (userAuswahl == 1) {

            geldEinzahlen();

        } else if (userAuswahl == 2) {

            geldAbheben();

        } else if (userAuswahl == 3) {

            aktuellerKontostand();

        } else if (userAuswahl == 4) {

            beenden();

        } else {

            System.out.println("Ungültige Auswahl getätigt! Bitte geben sie eine gültige Eingabe erneut ein. ");

        }

    }

    public static void geldEinzahlen() {

        int eingezahlterBetrag;

        System.out.println("Sie haben die 1 für Geld einzahlen gewählt. Ihr Aktueller Kontostand : " + userKontostand);

        do {
            System.out.println("Der Betrag den Sie Einzahlen möchten : ");

            eingezahlterBetrag = sc.nextInt();      // Berechnung der Beträge

            if (eingezahlterBetrag <= 0) {           // Kontrolle vom Betrag <= 0

                System.out.println("Ungültiger Betrag! Bitte geben Sie den Betrag erneut ein.");
            }

            System.out.println();

        } while (eingezahlterBetrag <= 0);

        userKontostand = userKontostand + eingezahlterBetrag;   // Konto Calculation

        System.out.println("Der Betrag wurde ihrem Konto gutgeschrieben. Ihr Aktueller Kontostand : " + userKontostand);

    }

    public static void geldAbheben() {

        int ausgezahlterBetrag;

        System.out.println("Sie haben die 2 für Geld auszahlen gewählt. Ihr Aktueller Kontostand : " + userKontostand);

        do {

            System.out.print("Der Betrag den Sie Auszahlen möchten : ");

            ausgezahlterBetrag = sc.nextInt();

            if (ausgezahlterBetrag <= 0 || ausgezahlterBetrag > userKontostand) {
                System.out.println("Ungültiger Betrag! Bitte geben Sie einen Betrag zwischen 1 und " + userKontostand + " ein.");
            }

            System.out.println();

        } while (ausgezahlterBetrag <= 0 || ausgezahlterBetrag > userKontostand);

        userKontostand = userKontostand - ausgezahlterBetrag;

        System.out.println("Der Betrag wurde ihrem Konto abgebucht. Ihr Aktueller Kontostand : " + userKontostand);

    }

    public static void aktuellerKontostand() {

        System.out.println("Ihr Kontostand beträgt : " + userKontostand + " " +
                "Euro");
    }

    public static String takeNameNachname() {

        String name;
        String nachname;

        System.out.print("Willkommen, Bitte geben sie ihre Daten ein : ");

        System.out.print("Geben sie ihr Vornamen ein : ");
        nachname = sc.next();

        System.out.print("Geben sie ihr Nachnamen ein : ");
        name = sc.next();

        return name + " " + nachname;

    }

    public static void menu(String nameNachname) {

        System.out.println("Willkommen " + nameNachname);

        System.out.println("*MENU");

        System.out.println("1) Geld einzahlen");

        System.out.println("2) Geld auszahlen");

        System.out.println("3) Kontostand\n4) Beenden");

        System.out.println();
    }

    // Methoden können in beliebiger Reihenfolge geschrieben werden
    public static int takeMenuAuswahl() {  // return Auswahl wo Methode genutzt wird

        System.out.println("Bitte wählen sie aus : ");

        int auswahl = sc.nextInt();

        return auswahl;
    }

    public static void beenden() {

        System.out.println("Vorgang beendet! Auf wiedersehen! ");
    }

    public static void menuWiederkaehren() {

        System.out.println("\nSie werden zum Menu weitergeleitet....\n");

    }
}
