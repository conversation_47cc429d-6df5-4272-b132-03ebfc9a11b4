package designPatterns.creational.factoryWiederholung;

public class Test {
    public static void main(String[] args) {

        Person person1 = PersonFactory.getPerson("<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", 1979);
        Person person2 = PersonFactory.getPerson("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 1980);
        Person person3 = PersonFactory.getPerson("<PERSON>", "<PERSON>", "<PERSON>", 1978);
        Person person4 = PersonFactory.getPerson(" ", "<PERSON>", "Schneider", 1977);


        System.out.println("Person 1 - Persönliche Eigenschaften ");
        System.out.println(person1);
        System.out.println();
        System.out.println("Person 2 - Persönliche Eigenschaften ");
        System.out.println(person2);
        System.out.println();
        System.out.println("Person 3 - Persönliche Eigenschaften ");
        System.out.println(person3);
        System.out.println();
        System.out.println("Person 4 - Persönliche Eigenschaften ");
        System.out.println(person4);



    }
}
