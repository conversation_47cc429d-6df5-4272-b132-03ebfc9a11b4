package jders21_ordnerVerarbeitungen.anwendung3;

import java.io.FileNotFoundException;
import java.io.PrintWriter;

public class DateiSchreiben {

    private PrintWriter printWriter;

    public boolean dateiErstellen(String dateiName) {

        String speicherOrt = "C:/Users/<USER>/Desktop/Projeler/";

        try {
            // erstelle dynamischen PrintWriter
            printWriter = new PrintWriter(speicherOrt + dateiName + ".txt");

            return true;

        } catch (FileNotFoundException e) {

            System.err.println("Fehler : " + e);

            return false;
        }
    }

    public void studentSpeichern(Student student) {

        String vorname = student.getVorname();
        String nachname = student.getNachname();
        int geburtsjahr = student.getGeburtsjahr();
        String studentenNummer = student.getStudentenNummer();

        printWriter.print(vorname + " " + nachname + " " + geburtsjahr + " " + studentenNummer);
        printWriter.println();  // oder ohne die Zeile für nebeneinander, mit vorherigen print als ln

        /* Da wir an der stelle, bei einem Eintrag eines Studenten nicht den printWriter für weitere Einträge schließen wollen,
        lagern wir ihn in eine separate Methode aus.
        printWriter.close();*/
    }

    // ob es geschlossen ist oder nicht
    public boolean prnitWriterSchliessen() {

        try {
            // schließt den printWriter im Klassen-Rumpf
            printWriter.close();

            return true;

        } catch (Exception e) {

            System.out.println("Fehler : " + e);

            return false;
        }
    }
}
