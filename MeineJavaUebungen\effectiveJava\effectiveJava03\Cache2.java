package effectiveJava.effectiveJava03;

// Das Interface Cache2 definiert Methoden, die für die Implementierung einer Cache-Datenstruktur erforderlich sind.
// Ein Cache speichert Schlüssel-Wert-Paare und ermöglicht das Speichern, Abrufen und Verwalten von Daten im Speicher.

public interface Cache2<F, S> {

    // Die put-Methode fügt einen Wert value mit dem Schlüssel key in den Cache ein.
    void put(F key, S value);

    // Die get-Methode gibt den Wert für den angegebenen Schlüssel key aus dem Cache zurück.
    S get(F key);

    // Die containsKey-Methode überprüft, ob der angegebene Schlüssel key im Cache vorhanden ist.
    boolean containsKey(F key);

    // Die size-Methode gibt die Anzahl der im Cache gespeicherten Elemente zurück.
    int size();

    // Die clear-Methode löscht alle Elemente im Cache und setzt ihn auf den Anfangszustand zurück.
    void clear();
}
/* Dieses Interface definiert die grundlegenden Operationen, die in einer Cache-Datenstruktur
   benötigt werden: das Einfügen von Werten (put), das Abrufen von Werten (get), das Überprüfen der Existenz
   eines Schlüssels (containsKey), das Ermitteln der Größe des Caches (size) und das Löschen aller
   Elemente im Cache (clear). Implementierungen dieses Interfaces können verwendet werden,
   um unterschiedliche Arten von Caches zu erstellen.*/

