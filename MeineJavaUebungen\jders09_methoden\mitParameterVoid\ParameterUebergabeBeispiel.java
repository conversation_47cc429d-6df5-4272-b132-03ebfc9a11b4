package jders09_methoden.mitParameterVoid;

public class ParameterUebergabeBeispiel {
    public static void main(String[] args) {

        addieren(5, 6, 5, 0.1);  // 8
        addieren(4, 4, 0, 0);  // 16
        addieren(5, 6, 5, 0.1);
        multiplizieren(3,3);
        multiplizieren(9,9);

    }

    public static void multiplizieren (int a, int b) {
        int addiert = a * b;
        System.out.println("Die Multiplikation der ersten beiden Zahlen ist gleich : " + addiert);

    }

    public static void addieren(int a, int b, int c, double d) {
        int addiert = a + b + c;
        System.out.println("Die addition nur der Ersten 3 Zahlen ist gleich : " + addiert);

    }
        /*
        * Einige weitere Varianten von Methoden könnten sein:
        * Methode mit optionalen Parametern: Hierbei können in der Methode einige Parameter optional sein,
        * d.h. sie müssen nicht zwingend mitgegeben werden. Diese können entweder Standardwerte besitzen
        * oder durch Überladung der Methode mit unterschiedlicher Anzahl von Parametern abgedeckt werden.
        *
        * Methoden mit variabler Parameterliste: Diese Methoden können eine variable Anzahl von
        * Parametern akzeptieren, die bei Aufruf der Methode als Array von Parametern übergeben werden.
        * Hierbei wird in Java die varargs Syntax verwendet.
        *
        * Methoden mit Referenzparametern: Hierbei wird anstelle des Wertes einer Variable,
        * ihre Referenz an die Methode übergeben. Dadurch ist es möglich, den Wert der Variablen
        * in der Methode zu ändern und diese Änderungen bleiben auch außerhalb der Methode erhalten.
        *
        * Rekursive Methoden: Hierbei ruft die Methode sich selbst auf, um ein bestimmtes Problem zu lösen.
        * Rekursion wird oft bei komplexen Problemen eingesetzt, die sich in kleinere, lösbare Teilaufgaben zerlegen lassen.
        *
        * Es gibt natürlich noch weitere Varianten von Methoden, aber diese sind einige der gängigen.
        * */
}
