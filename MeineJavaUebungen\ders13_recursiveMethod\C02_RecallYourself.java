package ders13_recursiveMethod;

public class C02_RecallYourself {
    public static void main(String[] args) {

        int input = 5;
        int result = addiere(input);
        System.out.println(result);
    }

    private static int addiere(int input) {
        if (input == 1) {
            return 1;
        } else {
            return input + addiere(input - 1);
            /*
            inputSum(5)
    -> 5 + inputSum(4)  -->  5 + 10 = 15
          -> 4 + inputSum(3)  --> 4 + 6 = 10
                  -> 3 + inputSum(2)  --> 3 + 3 = 6
                          -> 2 + inputSum(1)  --> 2 + 1 = 3
                                  -> 1*/
        }
    }
}
