package jders12_stringMethoden;

import java.nio.charset.StandardCharsets;

public class UmlautErsetzung4 {
    private static String[][] UMLAUT_REPLACEMENTS = {
            {"Ä", "Ae"},
            {"Ü", "Ue"},
            {"Ö", "Oe"},
            {"ä", "ae"},
            {"ü", "ue"},
            {"ö", "oe"},
            {"ß", "ss"}
    };

    public static void main(String[] args) {
        String input = "Özer Özözbek"; // Originaler String

        // Überprüfung und Anpassung der Encoding-Probleme
        String encoding = detectEncoding(input);
        String adjustedInput = adjustEncoding(input, encoding);

        // Umlaute ersetzen
        String umlauteErsetzt = replaceUmlaute(adjustedInput);

        System.out.println("Original: " + input);
        System.out.println("Angepasstes Encoding: " + encoding);
        System.out.println("Ersetzt: " + umlauteErsetzt);
    }

    // Methode zum Ersetzen der Umlaute
    public static String replaceUmlaute(String input) {
        String result = input;

        // Durchlaufe alle Einträge im UMLAUT_REPLACEMENTS-Array
        for (int i = 0; i < UMLAUT_REPLACEMENTS.length; i++) {
            String umlaut = UMLAUT_REPLACEMENTS[i][0];
            String ersatz = UMLAUT_REPLACEMENTS[i][1];

            // Ersetze Umlaut durch entsprechenden Ersatz
            result = result.replace(umlaut, ersatz);
        }

        return result;
    }

    // Methode zur Erkennung des Encodings einer Zeichenkette
    private static String detectEncoding(String input) {
        String[] encodingsToCheck = {"UTF-8", "UTF-32"}; // Liste der zu überprüfenden Encodings

        for (String encoding : encodingsToCheck) {
            byte[] bytes = input.getBytes(StandardCharsets.UTF_8);
            String encodedString = new String(bytes, StandardCharsets.UTF_8);

            if (input.equals(encodedString)) {
                return encoding; // Encoding gefunden
            }
        }

        return "Unbekannt"; // Kein passendes Encoding gefunden
    }

    // Methode zum Anpassen des Encodings einer Zeichenkette
    private static String adjustEncoding(String input, String encoding) {
        try {
            byte[] bytes = input.getBytes(encoding);
            return new String(bytes, encoding);
        } catch (Exception e) {
            System.out.println("Fehler beim Anpassen des Encodings: " + e.getMessage());
            return input;
        }
    }

    /* Dieser Code enthält die beiden zusätzlichen Methoden detectEncoding und adjustEncoding,
     * um die Encoding-Probleme zu überprüfen und das Encoding der Zeichenkette anzupassen, falls erforderlich.
     * In der main-Methode werden diese Methoden vor der Umlaut-Ersetzung aufgerufen, um sicherzustellen,
     * dass das Encoding korrekt ist.*/
}
