package jders13_objekte._02_objektZugriffsmodifikatoren.protectedAnwendung;

import jders13_objekte._02_objektZugriffsmodifikatoren.Auto;

public class ProtectedAnwendung2 extends Auto {
    public static void main(String[] args) {
        // Nur mit Bestehenden getter und setter aus der Oberklasse zu bekommen



        Auto araba1 = new Auto("BMW", "528i", 2013,"Blau");
    /*    System.out.println(araba1.marke);
        System.out.println(araba1.model);
        System.out.println(araba1.jahr);
        System.out.println(araba1.farbe);*/
        System.out.println(araba1);
    }

  /*  public Anwendung5(String mrk, String mdl, int j, String frb) {
        marke = mrk;
        model = mdl;
        jahr = j;
        farbe = frb;
    }*/

    /* Lösung Inkl.get unt set Methoden
   public static void main(String[] args) {
        ProtectedAnwendung2 araba1 = new ProtectedAnwendung2("BMW", "528i", 2013, "Blau");
        System.out.println(araba1.getMarke());
        System.out.println(araba1.getModel());
        System.out.println(araba1.getJahr());
        System.out.println(araba1.getFarbe());
    }

    public ProtectedAnwendung2(String mrk, String mdl, int j, String frb) {
        setMarke(mrk);
        setModel(mdl);
        setJahr(j);
        setFarbe(frb);*/
}
