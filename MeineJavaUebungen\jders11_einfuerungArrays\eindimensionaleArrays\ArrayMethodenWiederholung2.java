package jders11_einfuerungArrays.eindimensionaleArrays;

import java.util.Scanner;

public class ArrayMethodenWiederholung2 {
    static Scanner sc = new Scanner(System.in);
    public static void main(String[] args) {
        // In diesem Beispiel wird das Array in der Main erstellt im Gegensatz innerhalb der Methode
        int laengeArray; // Variable zur Speicherung der Array-Länge

        System.out.print("Wie viele Elemente soll die Array Serie enthalten : ");
        laengeArray = sc.nextInt(); // Erst gibt Benutzer die gewünschte Array-Länge in der Main ein

        int[] arraySerie = serienWerteEingeben(laengeArray);
        serienWerteAusgeben(arraySerie);


    }

    public static void serienWerteAusgeben(int[] z) {

        for (int i = 0; i < z.length; i++) {
            System.out.println(z[i]);
        }
    }

    public static int[] serienWerteEingeben(int arrayLaenge) {

        int[] zahlenSerie = new int[arrayLaenge];

        for (int i = 0; i < zahlenSerie.length; i++) {

            System.out.print((i + 1) + ". Element eingeben : ");

            zahlenSerie[i] = sc.nextInt();
        }

        return zahlenSerie;
    }

}
