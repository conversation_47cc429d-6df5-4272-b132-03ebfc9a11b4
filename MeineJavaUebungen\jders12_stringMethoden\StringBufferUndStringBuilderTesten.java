package jders12_stringMethoden;

public class StringBufferUndStringBuilderTesten {
    public static void main(String[] args) {
        /* Ein möglicher Merksatz für den Unterschied zwischen StringBuilder und StringBuffer lautet:
           "StringBuffer für Threads, StringBuilder für schnelle Konkatenation."

           Dieser Merksatz spielt auf die Unterschiede in Bezug auf Thread-Sicherheit und Leistung an.
           Der StringBuffer ist thread-sicher und kann daher in Multi-Thread-Umgebungen verwendet werden,
           um sicherzustellen, dass mehrere Threads gleichzeitig auf den StringBuffer zugreifen
           und Änderungen vornehmen können, ohne Dateninkonsistenzen zu verursachen. Der StringBuilder hingegen
           ist nicht thread-sicher, bietet jedoch eine bessere Leistung bei der Konkatenation von Strings, da er keine Synchronisierungskosten hat.

           Indem du dir diesen Merksatz merkst, kannst du dich an die wichtigsten Unterschiede zwischen
           StringBuilder und StringBuffer erinnern und die geeignete Klasse basierend auf deinen Anforderungen auswählen.*/
        // StringBuffer = 6ms
        // StringBuilder = 3ms

        /* StringBuffer wird nicht empfohlen, wenn mehrere threads den String gleichzeitig erreichen müssen nutzen wir StringBuilder-
           Aber ein einfacher Anwendungsfall, in dem die fehlende Thread-Sicherheit des StringBuilder nicht wichtig ist,
           ist zum Beispiel in einem einfachen Konsolenprogramm, das von einem einzigen Benutzer ausgeführt wird.
           Wenn das Programm nur sequenziell ausgeführt wird und kein paralleler Zugriff auf den StringBuilder erforderlich ist,
           besteht keine Notwendigkeit für Thread-Sicherheit. Der StringBuilder kann verwendet werden, um effizient Text zu manipulieren
           und zu konkatieren, ohne dass sich mehrere Threads gegenseitig beeinflussen oder Dateninkonsistenzen verursachen können.
           In solchen Fällen bietet der StringBuilder eine einfache und schnelle Möglichkeit, Text zu bearbeiten,
           ohne die Komplexität der Thread-Synchronisierung berücksichtigen zu müssen.*/

        long zeit = System.currentTimeMillis();

        StringBuffer stringBuffer = new StringBuffer("Java");

        for (int i = 0; i < 40000; i++) {
            stringBuffer.append("Programmieren");
        }

        System.out.println("StringBuffer : " + (System.currentTimeMillis() - zeit) + "ms");

        /* StringBuilder. Ein einfacher Anwendungsfall, in dem die fehlende Thread-Sicherheit des StringBuilder nicht wichtig ist,
         ist zum Beispiel in einem einfachen Konsolenprogramm, das von einem einzigen Benutzer ausgeführt wird.
         Wenn das Programm nur sequenziell ausgeführt wird und kein paralleler Zugriff auf den StringBuilder erforderlich ist,
         besteht keine Notwendigkeit für Thread-Sicherheit. Der StringBuilder kann verwendet werden, um effizient
         Text zu manipulieren und zu konkatieren, (Zu Beachten ist)ohne dass sich mehrere Threads gegenseitig beeinflussen! oder
         Dateninkonsistenzen verursachen könnten! In solchen Fällen bietet der StringBuilder eine einfache und schnelle
         Möglichkeit, Text zu bearbeiten, ohne die Komplexität der Thread-Synchronisierung berücksichtigen zu müssen.

         Ein Nachteil von StringBuffer im Vergleich zu StringBuilder ist die zusätzliche Synchronisierung,
         die für die Thread-Sicherheit erforderlich ist. Durch diese Synchronisierung kann es zu Leistungseinbußen kommen,
         insbesondere wenn viele Threads gleichzeitig auf den StringBuffer zugreifen und Änderungen vornehmen möchten.
         Wenn die Thread-Sicherheit nicht erforderlich ist, führt die zusätzliche Synchronisierung zu einem unnötigen Overhead.

         Ein weiterer Nachteil von StringBuffer ist seine feste Größe. Einmal initialisiert, hat ein StringBuffer eine feste Kapazität,
         die durch die Länge des initialen Strings bestimmt wird. Wenn mehr Text hinzugefügt wird und die Kapazität des StringBuffer überschritten wird,
         muss eine interne Neuzuweisung des Speichers erfolgen. Dies kann zu zusätzlichen Speicheranforderungen und Leistungseinbußen führen.

         Zusammenfassend lassen sich die Nachteile von StringBuffer wie folgt zusammenfassen:

         Leistungseinbußen durch zusätzliche Synchronisierung, insbesondere in Multi-Thread-Umgebungen.
         Feste Größe, die zu Speicherneuzuweisungen und möglichen Leistungseinbußen führen kann, wenn die Kapazität überschritten wird.
         Diese Nachteile machen StringBuffer in bestimmten Szenarien weniger geeignet, insbesondere
         wenn keine Thread-Sicherheit erforderlich ist und häufige Änderungen an einem StringBuffer vorgenommen werden.
         In solchen Fällen kann die Verwendung von StringBuilder eine bessere Alternative sein.*/
        zeit = System.currentTimeMillis();

        StringBuilder stringBuilder = new StringBuilder("Java");

        for (int i = 0; i < 40000; i++) {
            stringBuilder.append("Programmieren");
        }

        System.out.println("StringBuilder : " + (System.currentTimeMillis() - zeit) + "ms");

    }

}
