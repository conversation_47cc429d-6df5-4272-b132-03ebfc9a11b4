package jders21_ordnerVerarbeitungen.anwendung3;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Scanner;

public class DateiLesen {

    private Scanner sc;

    public void zuLesendeDatei(String dateiName) {

        String speicherOrt = "C:/Users/<USER>/Desktop/Projeler/";

        // File file = new File(speicherOrt + dateiName + ".txt");


        // Scanner sc = new Scanner(file);
        try {

            sc = new Scanner(new File(speicherOrt + dateiName + ".txt"));

        } catch (FileNotFoundException e) {

            System.err.println("Fehler : " + e);
        }
    }

    public void leseDateiZeilen() {
        /* Zeile für Zeile verarbeiten
        String zeile;*/


        while (sc.hasNextLine()) {
            /*
            zeile = sc.nextLine();
            System.out.println(zeile);
            */

            System.out.println(sc.nextLine());
        }
    }

    public void leseDateiWoerter(){
        // Wort für wort verarbeiten

        while (sc.hasNext()){

            System.out.println(sc.next());
        }

    }

    // Dieses mal werden wir nicht den boolean wert nutzen
    public void dateiLesenSchliessen() {

        sc.close();
    }
}
