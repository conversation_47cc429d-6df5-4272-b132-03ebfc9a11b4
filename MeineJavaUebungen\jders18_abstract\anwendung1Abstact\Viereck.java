package jders18_abstract.anwendung1Abstact;

public class Viereck extends Form {

    /* Abstrakte Methoden in einer abstrakten Klasse dienen als Platzhalter für spezifisches Verhalten,
     * das von den konkreten Unterklassen bereitgestellt werden muss. Wenn eine Klasse von einer abstrakten
     * Klasse erbt und diese abstrakte Methode nicht implementiert, muss die abgeleitete Klasse selbst
     * als abstrakt markiert werden und kann ebenfalls nicht direkt instanziiert werden.
     * Die Verpflichtung, die abstrakten Methoden zu implementieren, liegt also bei
     * den konkreten Unterklassen, um das Verhalten der Methode entsprechend ihrer spezifischen Anforderungen anzupassen. */

    private double seite;

    public Viereck(double seite) {

        this.seite = seite;
    }

    // Wir wählen immer eigentlich set&get, um alle Möglichkeiten unserer Daten genaustes zu überprüfen

    /**
     * Gibt die Seitenlänge des Vierecks zurück.
     *
     * @return Die Seitenlänge des Vierecks.
     */
    public double getSeite() {
        return seite;
    }


    // wir nutzen nach unseren Anforderungen die set-Methode, um neue Werte unserem Objekt Viereck zu setzen.

    /**
     * Setzt die Seitenlänge des Vierecks auf den angegebenen Wert.
     *
     * @param seite Die zu setzende Seitenlänge.
     */
    public void setSeite(double seite) {
        this.seite = seite;
    }


    // Implement Methods from Form
    @Override
    public double getInhalt() {
        // wir nutzen Math.pow(seite,2) für seite*seite
        return seite * 4;
    }

    @Override
    public double getUmfang() {
        // wir nutzen Math.pow(seite,2) für seite*seite
        return Math.pow(seite, 2);
    }
}
