package jders04_operatoren;

public class RechenOperatoren {
    public static void main(String[] args) {
        // = Zuweisung Operator

        int zahl1 = 26;

        int zahl2 = 15;

        // + Additionsoperator nutzen wir nicht immer so
        int addieren = zahl1 + zahl2;

        // - Subtraktion
        int subtrahieren = zahl1 - zahl2;

        // * Multiplikation
        int multiplizieren = zahl1 * zahl2;

        // / Division
        int dividieren = zahl1 / zahl2;
        float fDividieren = (float) zahl1 / zahl2;

        // % Modulo-Rechnung - Division mit Rest
        int mod = zahl1 % zahl2;

        System.out.println("Gesamtergebnis : " + addieren);  // 41

        System.out.println("Gesamtergebnis : " + subtrahieren);  // 11

        System.out.println("Gesamtergebnis : " + multiplizieren);  // 390

        System.out.println("Gesamtergebnis : " + dividieren);  // 1

        System.out.println("Gesamtergebnis : " + fDividieren);  // 1.7333333

        System.out.println("Mod : " + mod);  // 11

    }
}
