package ders11_ternaryOperator;

import java.util.Scanner;

public class C04_NestedTernary {
    public static void main(String[] args) {
        // Beispiel für folgende Vorgabe für Verarbeitung der Eingabe einer Zahl
        // bei positive<PERSON>, gerade <PERSON>ahl oder ungerade Zahl ausgeben
        // bei keiner positiven <PERSON>ahl, eine dreistellige Zahl oder keine dreistellige Zahl ausgeben
        // zusätzlich sehen wir weitere verarbeitete Beispiele als Ausgabe mit den Variablen x und y
        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte Zahl eingeben");
        int a = scan.nextInt();
        int x = 10;
        int y = 20;

        /*
        a>0
            ? a%2==0 ? "Gerade Zahl" : "Ungerade Zahl"
            : a<=-100 && a>-1000 ? "Dreistellig im Minus" : "nicht dreistellig im Minus"
        */

        System.out.println(a > 0 ? a % 2 == 0 ? "Gerade Zahl" : "Ungerade Zahl" : a <= -100 && a > -1000 ? "Dreistellig im Minus" : "nicht dreistellig im Minus");

        System.out.println(y > 5 ? x > 0 ? 100 : 50 : x < 20 ? x + 5 : x - 5);  // 100
        System.out.println(y < x ? y > 0 ? y + x : y - x : x < 10 ? x * 5 : y / x);  //2
        System.out.println(x == y ? x > y ? x : y : x < y ? x + y : x - y);  // 30
    }
}
