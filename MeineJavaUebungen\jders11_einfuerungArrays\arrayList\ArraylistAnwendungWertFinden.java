package jders11_einfuerungArrays.arrayList;

import java.util.ArrayList;
import java.util.Scanner;

public class ArraylistAnwendungWertFinden {
    public static void main(String[] args) {

        Scanner sc = new Scanner(System.in);
        int zahl;
        boolean gefunden = false; // Variable, um festzuhalten, ob der Wert gefunden wurde

        System.out.print("Gesuchter Wert in der Liste: ");
        zahl = sc.nextInt();

        ArrayList<Integer> zahlenListe = new ArrayList<>();  // Keine primitiven Datentypen, Wrapper-Klassen verwenden

        zahlenListe.add(121);
        zahlenListe.add(32);
        zahlenListe.add(2423);
        zahlenListe.add(16);

        // Wenn wir wissen wollen, in welchem Index sich der Wert befindet
        for (int i = 0; i < zahlenListe.size(); i++) {
            if (zahlenListe.get(i) == zahl) {
                System.out.println("Wert wurde gefunden");
                System.out.println("Index: " + i);
                gefunden = true; // Wert wurde gefunden, die Variable auf true setzen
            }
        }

        if (!gefunden) {
            System.out.println("Wert wurde nicht gefunden");
        }
    }
}
