package jders21_ordnerVerarbeitungen.anwendung5Stream;

import java.io.*;

public class DateiLesen {

    private ObjectInputStream ois;


    public boolean leseDatei(String dateiName) {

        String dateiPfad = "C:/Users/<USER>/Desktop/Projeler/";


        // Mit dieser Überprüfung vermeiden wir die Nullpointer-Ausnahme, indem wir sicherstellen, dass die Datei tatsächlich existiert,
        // bevor wir versuchen, sie zu öffnen und zu verwenden.
        try {

            FileInputStream fis = new FileInputStream(dateiPfad + dateiName + ".abc");

            ois = new ObjectInputStream(fis);

            return true;

        } catch (FileNotFoundException e) {

            System.err.println("Geforderte Datei wurde nicht gefunden! ");
            System.err.println("Fehler : " + e);
        } catch (IOException e) {

            System.err.println("Geforderte Datei konnte nicht geöffnet werden! ");
            System.err.println("Fehler : " + e);

        }

        return false;
    }

    public void dateiLesen() {

        try {

            /*
            Die While-Schleife wird in der Methode dateiLesen() verwendet, um die Datei Zeile für Zeile zu lesen.
            Die Schleife beginnt mit der Bedingung true, was bedeutet, dass sie immer ausgeführt wird, es sei denn,
            sie wird durch eine Ausnahme unterbrochen.

            In der Schleife wird zunächst versucht, einen Studenten aus der Datei zu lesen.
            Wenn dies erfolgreich ist, wird der Student auf der Konsole ausgegeben.
            Wenn die Datei leer ist, wird eine EOFException geworfen, die die Schleife beendet.

            Die While-Schleife muss an dieser Stelle zum Einsatz kommen, weil die Datei beliebig viele Zeilen enthalten kann.
            Wenn die Schleife nicht endlos wäre, würde sie die Datei nur teilweise lesen.

            Eine Alternative wäre die Verwendung einer For-Schleife mit einer festen Anzahl von Wiederholungen.
            Dies wäre jedoch nur möglich, wenn die Anzahl der Zeilen in der Datei bekannt ist.

            Im konkreten Beispiel ist die Anzahl der Zeilen in der Datei nicht bekannt.
            Daher ist die Verwendung einer While-Schleife die einzige Möglichkeit, um sicherzustellen, dass alle Zeilen gelesen werden.*/
            while (true) {
                // ein versuch einen Studenten zu lesen
                Student student = (Student) ois.readObject();

                // Wir wollen es auf der Konsole sehen
                System.out.println(student);

                // wenn keine weiteren Einträge bestehen erhalten wir eine Ausnahme die erneut abzufangen ist
            }

        } catch (EOFException e) {
            // wenn aus irgend einem Grund, beim lessen in der while Schleife wir eine Ausnahme erhalten sollten
            System.out.println("Datei Lese Vorgang Beendet");

            // Bei Void Methoden wird die Methode durch ein einfaches return beendet
            return;

            // im Falle, wenn keine weitere Datei zum Lesen zur Verfügung gegeben ist
        } catch (IOException e) {
            // wenn die Student-Klasse nicht gefunden werden konnte
            System.err.println("Studenten-Klasse wurde nicht gefunden");
            System.err.println("Fehler : " + e);

        } catch (ClassNotFoundException e) {
            // wenn die Student-Klasse nicht gelesen werden konnte
            System.err.println("Studenten-Klasse konnte nicht gelesen werden! ");
            System.err.println("Fehler : " + e);
        }

        // wenn wir Weiteres verarbeiten wollen, sollte hier danach der Return kommen
    }


    public boolean dateiLesenSchliessen() {
        // Etwas was im Klassenrumpf deklariert wurde kann bereits von irgendwo anders automatisch bereits geschlossen sein,
        // um es nicht wiederholt zu schließen, kontrollieren wir es ob es nicht geschlossen ist und im und geschlossenem fall schließen wir es.
        // Wenn nicht automatisch bereits geschlossen ist, kommt unser if zum Einsatz
        if (ois != null) {

            try {
                //  hier wird geschlossen
                ois.close();

            } catch (IOException e) {
                // wenn denn noch im try es nicht geschlossen werden konnte
                System.err.println("Beim Schließen von ObjectInputStream wurde ein Fehler festgestellt ! ");
                System.err.println("Fehler : " + e);

                return false;
            }
        }
        return true;
    }

}
