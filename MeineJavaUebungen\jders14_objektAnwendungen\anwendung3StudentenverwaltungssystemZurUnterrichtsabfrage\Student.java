package jders14_objektAnwendungen.anwendung3StudentenverwaltungssystemZurUnterrichtsabfrage;

import java.util.ArrayList;

public class Student {
    // ArrayList im constructor mit Parametern, dass wir eine vorher erstellte Liste weitergeben
    // Anforderungen der Anwendung nur für eine Schule

    public static String schule;

    private String name;

    private String nachname;

    private int geburtsjahr;

    private String schuhlNummer;

    private ArrayList<String> unterrichtsFach;

    public Student() {

    }

    public Student(String name, String nachname, int geburtsjahr, String schuhlNummer, ArrayList<String> unterrichtsFach) {

        this.name = name;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
        this.schuhlNummer = schuhlNummer;
        this.unterrichtsFach = unterrichtsFach;
    }

    public static String getSchule() {
        return schule;
    }

    public static void setSchule(String schule) {
        Student.schule = schule;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    public String getSchuhlNummer() {
        return schuhlNummer;
    }

    public void setSchuhlNummer(String schuhlNummer) {
        this.schuhlNummer = schuhlNummer;
    }

    public ArrayList<String> getUnterrichtsFach() {
        return unterrichtsFach;
    }

    public void setUnterrichtsFach(ArrayList<String> unterrichtsFach) {
        this.unterrichtsFach = unterrichtsFach;
    }
/*
    @Override
    public String toString() {
        return "Student [name=" + name +
                ", nachname=" + nachname +
                ", geburtsjahr=" + geburtsjahr +
                ", schuhlNummer=" + schuhlNummer +
                ", unterrichtsFach=" + unterrichtsFach +
                "]";
    }*/

    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", nachname='" + nachname + '\'' +
                ", geburtsjahr=" + geburtsjahr +
                ", schuhlNummer='" + schuhlNummer + '\'' +
                ", unterrichtsFach=" + unterrichtsFach +
                '}';
    }
}
