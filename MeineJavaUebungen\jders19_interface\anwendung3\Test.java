package jders19_interface.anwendung3;

import java.util.ArrayList;

public class Test {
    public static void main(String[] args) {

        // Wir nutzen ein interface für einen spezifischen Verarbeitungsvorgang

        // Nur aus der Klasse Person Eigenschaften ohne Adresse
        Student student1 = new Student("<PERSON><PERSON>rn", "Sonne", 1994, null);

        // Nur aus der Klasse Adresse wird die Adresse erstellt
        Adresse adresse1 = new Adresse("Berlinerstrasse", "10500", "Berlin");

        // Adresse wird student1 zugewiesen
        student1.setAdresse(adresse1);
        student1.setStudentenNummer("2344");
        // Zuerst muss die ArrayList erstellt werden die verlangt wird, student1.setUnterrichtsFaecher(ArrayList);

        // Leere Liste für die Unterrichtsfächer erstellen, wo die Unterrichtsfächer festgelegt werden
        ArrayList<String> faecher1 = new ArrayList<>();

        // Student1 legt die Unterrichtsfächer fest
        faecher1.add("Mathematik");
        faecher1.add("Chemie");
        faecher1.add("Physik");

        // Liste der Unterrichtsfächer werden student1 zugewiesen. Bsp. mit Speichern button
        student1.setUnterrichtsFaecher(faecher1);

        // erstellen neue Liste für andere Unterrichtsfächer
        ArrayList<String> faecher2 = new ArrayList<>();

        // Student2 legt die Unterrichtsfächer fest
        faecher2.add("Englisch");
        faecher2.add("Deutsch");
        faecher2.add("Türkisch");

        // erstelle neuen student2, der auch adresse1 aber die Unterrichtsfächer faecher2 bekommt
        Student student2 = new Student("Kan", "Ata", 1992, adresse1, "2370", faecher2);

        Adresse adresse2 = new Adresse("Haupstrasse", "10121", "Berlin");

        // erstelle neuen student3
        Student student3 = new Student("Bill", "Johns", 1990, adresse2, "2371", faecher1);

        // erstelle neuen Dozenten
        Dozent dozent1 = new Dozent("Daniel", "Grins", 1971, null);

        // erstelle neue Adresse
        Adresse adresse3 = new Adresse("Pankstrasse", "10303", "Berlin");

        dozent1.setAdresse(adresse3);

        // Um die Klasse PersonenVerarbeitungImpl zu erreichen, erstellen wir ein Objekt von
        PersonenVerarbeitungImpl personenVerarbeitungImpl = new PersonenVerarbeitungImpl();

        /*
        boolean zustand = personenVerarbeitungImpl.personSpeichern(student1);

        if (zustand){ // true

            System.out.println("Ihre Personen bezogenen Daten wurden erfolgreich gespeichert");
        }
        else {

            System.out.println("Beim speichern Ihrer Personen bezogenen Daten wurde ist Fehler aufgetreten! ");
        }
        */

        // Speichern alle unsere zuvor erstellten Personen durch unser interface mit unserer
        // implementierten Methode die unsere Person in eine Liste einfügt und ture oder false zurückgibt
        personenVerarbeitungImpl.personSpeichern(dozent1);
        personenVerarbeitungImpl.personSpeichern(student3);
        personenVerarbeitungImpl.personSpeichern(student2);
        personenVerarbeitungImpl.personSpeichern(student1);

        StudentenVerarbeitungsvorgaenge studentenVerarbeitungsVorgaenge = new StudentenVerarbeitungsvorgaengeImpl();

        studentenVerarbeitungsVorgaenge.studentSpeichern(student3);
        studentenVerarbeitungsVorgaenge.studentSpeichern(student2);
        studentenVerarbeitungsVorgaenge.studentSpeichern(student1);

        studentenVerarbeitungsVorgaenge.studentenListe();


        // alle Personen untereinander ausgeben
        // personenVerarbeitungImpl.personenListe();

    }

    // Hierbei könnte mann generics verwenden anstatt die beiden Interface Student und Dozent Verarbeitungsvorgänge.
}
