package jders09_methoden.returnMitParameter;

public class ParameterReturnAnwendeung4 {
    public static void main(String[] args) {

        int ergMax = max(20, 10);
        System.out.println("Ergebnis Max : " + ergMax);  // Gibt immer größe<PERSON> Zahl von beiden aus

        int ergMin = min(15, 17);
        System.out.println("Ergebnis Min : " + ergMin);  // Gibt immer kleinere Zahl von beiden aus
    }

    public static int max(int a, int b) {

        if (a > b) {
            return a;
        } else {
            return b;
        }
    }

    public static int min(int a, int b) {

        return (a < b) ? a : b;

    }

}
