package ders04_dataCasting;

/**
 * Diese Klasse demonstriert verschiedene Aspekte der Typzuweisung und des Typcastings in Java.
 *
 * Wichtige Konzepte:
 * - Zulässige und unzulässige Zuweisungen für verschiedene Datentypen
 * - Implizites Casting (Widening) von kleineren zu größeren Datentypen
 * - Besonderheiten bei der Zuweisung von Werten zum char-Datentyp
 * - Typbeschränkungen bei boolean-Variablen
 *
 * Die Klasse zeigt, welche Werte den verschiedenen primitiven Datentypen
 * zugewiesen werden können und welche Zuweisungen nicht erlaubt sind.
 */
public class C05_DataCasting {
    /**
     * Die Hauptmethode demonstriert verschiedene Zuweisungen und Typumwandlungen.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        System.out.println("Demonstration verschiedener Typzuweisungen in Java");

        // 1. BOOLEAN-DATENTYP
        System.out.println("\n1. BOOLEAN-DATENTYP");
        System.out.println("Der boolean-Datentyp kann nur die Werte true oder false annehmen.");

        boolean bl = false;
        System.out.println("Initialisierung: boolean bl = false; // Wert: " + bl);

        bl = true;
        System.out.println("Zuweisung: bl = true; // Neuer Wert: " + bl);

        // Unzulässige Zuweisungen (auskommentiert, da sie Kompilierungsfehler verursachen würden)
        System.out.println("\nUnzulässige Zuweisungen für boolean (auskommentiert im Code):");
        System.out.println("// bl = \"true\"; // Fehler: String kann nicht zu boolean konvertiert werden");
        System.out.println("// bl = 20; // Fehler: int kann nicht zu boolean konvertiert werden");
        System.out.println("// bl = 'a'; // Fehler: char kann nicht zu boolean konvertiert werden");

        // 2. CHAR-DATENTYP
        System.out.println("\n2. CHAR-DATENTYP");
        System.out.println("Der char-Datentyp kann einzelne Zeichen oder deren Unicode-Werte speichern.");

        char ch = 't';
        System.out.println("Initialisierung: char ch = 't'; // Wert: '" + ch + "' (Unicode: " + (int)ch + ")");

        ch = '%';
        System.out.println("Zuweisung: ch = '%'; // Neuer Wert: '" + ch + "' (Unicode: " + (int)ch + ")");

        ch = ' ';
        System.out.println("Zuweisung: ch = ' '; // Neuer Wert: Leerzeichen (Unicode: " + (int)ch + ")");

        ch = 99;  // 99 ist der Unicode-Wert für 'c'
        System.out.println("Zuweisung: ch = 99; // Neuer Wert: '" + ch + "' (Unicode-Wert 99)");

        // 3. NUMERISCHE DATENTYPEN UND IMPLIZITES CASTING
        System.out.println("\n3. NUMERISCHE DATENTYPEN UND IMPLIZITES CASTING");
        System.out.println("Implizites Casting erfolgt automatisch bei Zuweisungen von kleineren zu größeren Datentypen.");

        int zahl1 = 20;
        System.out.println("Initialisierung: int zahl1 = 20; // Wert: " + zahl1);

        short zahl2 = 11;
        System.out.println("Initialisierung: short zahl2 = 11; // Wert: " + zahl2);

        double zahl3 = 24;
        System.out.println("Initialisierung: double zahl3 = 24; // Wert: " + zahl3);

        // Implizites Casting von short zu int (Widening)
        zahl1 = zahl2;  // short -> int (automatisches Casting)
        System.out.println("Zuweisung mit implizitem Casting: zahl1 = zahl2; // Neuer Wert von zahl1: " + zahl1);

        // Weitere Beispiele für implizites und explizites Casting
        System.out.println("\nWeitere Beispiele für Typumwandlungen:");

        // Implizites Casting (Widening)
        double d = zahl1;  // int -> double
        System.out.println("Implizites Casting (int -> double): double d = zahl1; // Wert: " + d);

        // Explizites Casting (Narrowing)
        int i = (int) zahl3;  // double -> int (erfordert explizites Casting)
        System.out.println("Explizites Casting (double -> int): int i = (int) zahl3; // Wert: " + i);

        // Unzulässige Zuweisung ohne explizites Casting (auskommentiert)
        System.out.println("\nUnzulässige Zuweisung ohne explizites Casting (auskommentiert im Code):");
        System.out.println("// zahl2 = zahl1; // Fehler: int kann nicht implizit zu short konvertiert werden");
        System.out.println("// Korrekt wäre: zahl2 = (short) zahl1; // Explizites Casting erforderlich");

        /*
         * ZUSAMMENFASSUNG DER TYPUMWANDLUNGSREGELN:
         *
         * 1. Implizites Casting (automatisch, ohne Datenverlust):
         *    - byte -> short -> int -> long -> float -> double
         *    - char -> int -> long -> float -> double
         *
         * 2. Explizites Casting (manuell, mit möglichem Datenverlust):
         *    - double -> float -> long -> int -> char oder short -> byte
         *    - Syntax: (Zieltyp) Ausdruck
         *
         * 3. Besondere Regeln:
         *    - boolean kann nicht in andere Typen umgewandelt werden und umgekehrt
         *    - char kann numerische Werte speichern (Unicode-Werte)
         *    - Bei Zuweisungen von größeren zu kleineren Typen ist explizites Casting erforderlich
         */
    }
}
