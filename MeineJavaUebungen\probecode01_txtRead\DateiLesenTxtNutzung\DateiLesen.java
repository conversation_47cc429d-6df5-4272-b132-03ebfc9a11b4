package probecode01_txtRead.DateiLesenTxtNutzung;

import java.io.FileInputStream;
import java.io.IOException;

public class DateiLesen {
    public static void main(String[] args) throws IOException {

        /* -Wie machen wir txt. Datei bekannt
         *  -Wie Können wir Daten Lesen und in Java die Daten dann zu manipulieren und zu nutzen wie wir es wollen
         * Wir nutzen die waren.txt Datei über content root path aus unserem Package für unser FileInputStream
         *-wir werden feststellen wie viele Auflistungen es von Beständen in unserer Liste der Datei gibt
         * -wir werden feststellen wie viele Bestandteile eine Auflistung hat
         *
         */

        String path = "src/probecode01_txtRead/DateiLesenTxtNutzung/waren.txt";

        // unser FileInputStream erreicht unsere Datei, im falle nicht add FileNotFoundException to method signature
        FileInputStream fis = new FileInputStream(path);  // Wir können die Ausnahme auch zur main hin zu deklarieren

        // ebenfalls add exception to method signature falls der FileInputStream nicht Datei lesen kann
        // die Oberklasse IOException für alle ausnahmen werden eingebunden
        // mit (char) den ASCII-Wert als Charakter erhalten
        // System.out.println((char) fis.read());
        // gibt je Aufruf den nächsten weiteren Charakter aus
        // System.out.println((char) fis.read());

        // fis.read(); ist kein boolean und arbeitet wie ein iterator, solange ein weiterer Charakter existiert eins vor iteriert,
        // bis zum Ende arbeitet und mit -1 für keine Charaktere abschließt

        // Leider iteriert der iterator eins weiter mit jedem Aufruf von fis.read in unserer while schleife
        // in unserem fall um zwei stellen, um dies zu verhindern, versuchen wir beides auf einmal in einem Aufruf
        // hier für geben wir eine Variable bekannt anstelle stattdessen immer wieder gegeben wird und dies hängen wir zu einem string
        int kontrol = 0;
        // der String der uns die Ergebnisse liefert
        String lyrik = "";
        while ((kontrol = fis.read()) != -1) {

            //System.out.print((char)kontrol);
            /*Jedoch müssen wir dies als richtigen String erhalten für gewünschte Manipulationen*/
            //System.out.print((char) fis.read());

            lyrik += (char) kontrol;
        }

        // nach dem die while Schleife durchlaufen ist, enthaltet der String unsere gesamte Liste System.out.println(lyrik);

        int warenAnzahl = 0;

        // da in unserer Warenbestandteile in unserer Liste mit einem "-" vor gekennzeichnet sind
        String arr[] = lyrik.split("-");
        System.out.println("Anzahl der Warenbestandteile : " + (arr.length - 1));

        // da wir nicht mit dem Charakter ")" oder "\)" arbeiten können, versuchen wir die Digits herauszufinden
        // und hier für brauchen wir nicht die Strings, denn hiermit arr= lyrik.split(""); würden wir nun jeden Charakter und jede leere Stelle bekommen

        int ueberschriftAnzahl=0;

        // wir erstellen eine Kontrolle fur zweistellige digits
        int index = 0;

        for (int i = 0; i < lyrik.length(); i++) {

            // wir nehmen jeden Charakter einzeln
            char charakter=lyrik.charAt(i);

            // wenn der Charakter digit ist dan werden wir das als Warenkategorie geltend Zählen,
            // dies gilt nur für dieses Datei-Dokument da es in jedem Dokument in ihrer Struktur anders gegliedert sein kann,
            // und alles dem entsprechend zur Bearbeitung angepasst werden muss

            // weil das erste charAt bereits in unserem Dokument digit ist, werden wir an der Stelle für das 0 Element unerwünschtes erwarten
            //char letzerCharakter = lyrik.charAt(i-1);
            if (i == 0){
                // wenn der erste Charakter digit ist
                if (Character.isDigit(charakter)){

                    ueberschriftAnzahl++;
                }
            } else {

                char letzerCharakter = lyrik.charAt(i-1);
                // dann wollen wir, wenn der letzerCharakter und folgende Charakter ebenfalls digit soll nicht hinzugefügt werden,
                // weil unsere auch erste Zahl digit ist müssen wir beides Kontrollieren
                if(Character.isDigit(charakter) && (!Character.isDigit(letzerCharakter))){
                    ueberschriftAnzahl++;
                }
            }

        }

            System.out.println("Anzahl der Waren Kategorien : " + ueberschriftAnzahl);
    }

}
