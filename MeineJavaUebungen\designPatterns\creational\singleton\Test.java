package designPatterns.creational.singleton;

public class Test {
    public static void main(String[] args) {

        // 10-mal `getSingleton()` aufrufen
        for (int i = 0; i < 10; i++) {
            Singleton singleton = Singleton.getSingleton();
        }

        // 4 weitere Aufrufe von `getSingleton()` aufrufen
        Singleton singleton11a = Singleton.getSingleton();
        Singleton singleton12b = Singleton.getSingleton();
        Singleton singleton13c = Singleton.getSingleton();
        Singleton singleton14d = Singleton.getSingleton();

        // Hashcode des Singleton-Objekts ausgeben
        // System.out.println(singleton14.hashCode());  // 1808253012
    }
}
