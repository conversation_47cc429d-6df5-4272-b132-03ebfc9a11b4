package com.example.mcpjavaapp.controller;

import com.example.mcpjavaapp.mcp.ModelContextProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller für die MCP-Integration (Model Context Protocol).
 * Bietet Endpunkte für die Kommunikation mit KI-Modellen unter Verwendung des MCP.
 */
@RestController
@RequestMapping("/mcp")
public class MCPController {

    private final ModelContextProtocol modelContextProtocol;

    @Autowired
    public MCPController(ModelContextProtocol modelContextProtocol) {
        this.modelContextProtocol = modelContextProtocol;
    }

    /**
     * Endpunkt für allgemeine Anfragen mit Kontextinformationen.
     *
     * @param prompt Der Prompt-Text für das Modell
     * @param contextMap Ein Map mit Kontextinformationen (optional)
     * @return Die Antwort des Modells
     */
    @PostMapping("/generate")
    public String generateWithContext(
            @RequestParam String prompt,
            @RequestBody(required = false) Map<String, Object> contextMap) {
        
        if (contextMap == null) {
            contextMap = new HashMap<>();
        }
        
        return modelContextProtocol.sendRequestWithContext(prompt, contextMap);
    }

    /**
     * Endpunkt für Code-Analyse mit Kontextinformationen.
     *
     * @param code Der zu analysierende Code
     * @param language Die Programmiersprache (Standard: "java")
     * @param projectType Der Projekttyp (Standard: "Spring Boot")
     * @return Die Analyse-Ergebnisse
     */
    @PostMapping("/analyze")
    public String analyzeCode(
            @RequestParam String code,
            @RequestParam(defaultValue = "java") String language,
            @RequestParam(defaultValue = "Spring Boot") String projectType) {
        
        return modelContextProtocol.analyzeCodeWithContext(code, language, projectType);
    }

    /**
     * Endpunkt für Dokumentationsgenerierung mit Kontextinformationen.
     *
     * @param code Der zu dokumentierende Code
     * @param language Die Programmiersprache (Standard: "java")
     * @param documentationStyle Der Dokumentationsstil (Standard: "JavaDoc")
     * @return Die generierte Dokumentation
     */
    @PostMapping("/document")
    public String generateDocumentation(
            @RequestParam String code,
            @RequestParam(defaultValue = "java") String language,
            @RequestParam(defaultValue = "JavaDoc") String documentationStyle) {
        
        return modelContextProtocol.generateDocumentationWithContext(code, language, documentationStyle);
    }
}
