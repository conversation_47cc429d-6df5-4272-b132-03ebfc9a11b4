package jders18_abstract.anwendung2Abstact;

public class Audi extends Fahrzeug {

    private int drehmoment;

    public Audi() {
    }

    @Override
    public double literVerbrauch() {
        return getGewicht() * 3;
    }

    public Audi(String farbe, String fahrzeugArt, double gewicht, int drehmoment) {
        super(farbe, fahrzeugArt, gewicht);
        this.drehmoment = drehmoment;
    }

    public int getDrehmoment() {
        return drehmoment;
    }

    public void setDrehmoment(int drehmoment) {
        this.drehmoment = drehmoment;
    }

    /*
    // Neue Methode für Liter den Verbrauch pro 100Km wir in der abstract Fahrzeug Klasse definiert
    public double kmVerbrauch(){

        return getGewicht() * 3;
    }
    */
    @Override
    public String toString() {
        return "Audi{" +
                "drehmoment='" + drehmoment + '\'' +
                ", farbe='" + getFarbe() + '\'' +
                ", Fahrzeug Art='" + getFahrzeugArt() + '\'' +
                ", gewicht=" + getGewicht() +
                '}';
    }
}
