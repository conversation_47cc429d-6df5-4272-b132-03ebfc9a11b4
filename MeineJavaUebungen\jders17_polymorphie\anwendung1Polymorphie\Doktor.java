package jders17_polymorphie.anwendung1Polymorphie;

public class Doktor extends Person {

    private String bereich;

    public Doktor() {
    }

    // Um es dieses mal uns es schnell und einfach zu machen, nehmen wir den constructor der alles nimmt anstatt 3 oder mehr
    public Doktor(String vorname, String nachname, int geburtsjahr, String bereich) {
        super(vorname, nachname, geburtsjahr);
        this.bereich = bereich;
    }

    public String getBereich() {
        return bereich;
    }

    public void setBereich(String bereich) {
        this.bereich = bereich;
    }

    @Override
    public String toString() {
        return "Doktor{" +
                " Bereich : '" + bereich + '\'' +
                ", Vorname : '" + getVorname() + '\'' +
                ", Nachname : '" + getNachname() + '\'' +
                ", Geburtsjahr : " + getGeburtsjahr() +
                '}';
    }
}
