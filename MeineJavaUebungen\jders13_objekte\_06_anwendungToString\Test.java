package jders13_objekte._06_anwendungToString;

public class Test {
    public static void main(String[] args) {

        // <PERSON><PERSON><PERSON><PERSON> von Studenten-Instanzen mit unterschiedlichen Werten
        Student student1 = new Student("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 1992, "12345");

        Student student2 = new Student("<PERSON><PERSON>", "Aygun", 1991, "12346");

        Student student3 = new Student("Berk", "Bulut", 1991, "12347");

        /* Die Variable stuInfos dient dazu, den Rückgabewert der Methode getInfos()
           für den spezifischen Studenten student1 zwischenzuspeichern. Dadurch kann dieser Wert
           in einer späteren Ausgabe oder in anderen Teilen des Codes verwendet werden,
           ohne dass die Methode erneut aufgerufen werden muss.

           Die Variable stuInfos ist unabhängig von den anderen Studentenobjekten
           und speichert nur den Wert zum Zeitpunkt der Zuweisung.
           Die Variable stuInfos enthält nur die Informationen des Studenten student1 zu dem Zeitpunkt,
           als die Zuweisung stattfand. Wenn sich die Eigenschaften des Studenten student1 nach der Zuweisung ändern,
           hat dies keine Auswirkungen auf den Wert von stuInfos.

           Wenn also der Vorname des Studenten student1 nach der Zuweisung 'zu'
           student1.setVorname("John") geändert wird auch die Ausgabe
           System.out.println("unter stuInfos gerufen " + stuInfos);
           jedoch immer noch den ursprünglichen Wert enthalten:
        */
        // Aufruf der Methode getInfos() für den Studenten student1 und Zuweisung des Rückgabewerts zu einer Variable
        String stuInfos = student1.getInfos();

        // Ausgabe der Informationen des Studenten student1, die durch die Methode getInfos() zurückgegeben wurden
        System.out.println("unter stuInfos gerufen " + stuInfos);

        // Direkter Aufruf der Methode getInfos() für den Studenten student2 und Ausgabe des Rückgabewerts
        System.out.println("unter student2.getInfos() gerufen " + student2.getInfos());

        // Direkter Aufruf der Methode getInfos() für den Studenten student3 und Ausgabe des Rückgabewerts
        System.out.println(student3.getInfos());

        // Ausgabe der Vornamen aller drei Studenten
        System.out.println(student1.getVorname() +
                " " + student2.getVorname() +
                " " + student3.getVorname());

        // Ausgabe des Studenten-Objekts student2, das die Standardimplementierung der toString()-Methode verwendet
        System.out.println(student2);


    }
}
