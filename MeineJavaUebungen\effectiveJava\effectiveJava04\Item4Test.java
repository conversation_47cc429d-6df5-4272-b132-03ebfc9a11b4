package effectiveJava.effectiveJava04;

public class Item4Test {
    public static void main(String[] args) {

      /* Da Beispiels Collections. nur Methoden beinhaltet, ist das Erstellen dessen Objektes nicht das Ziel einer Anwendungs-Logik.
         Utility-Klassen enthalten in der Regel nur Methoden, die von anderen Klassen verwendet werden können.
         Das Erstellen eines Objekts einer Utility-Klasse ist in der Regel nicht erforderlich, da die Methoden auch statisch aufgerufen werden können.
         Um zu verhindern, dass ein Objekt einer Utility-Klasse erstellt wird, wird der Konstruktor normalerweise als private deklariert.
         Dies bedeutet, dass der Konstruktor nur von innerhalb der Klasse selbst aufgerufen werden kann.
         so wird sichergestellt, dass wir diese nicht initialisieren können.
         Dies ist ein generelles vorgehen bei utility Klassen, hinzu können sie auch exceptions beinhalten,
         um Fehler zu behandeln, die bei der Verwendung der Methoden auftreten können.

         Es gibt einige Ausnahmen von diesem Vorgehen. In einigen Fällen kann es sinnvoll sein,
         einen Konstruktor für eine Utility-Klasse bereitzustellen. Dies kann zum Beispiel der Fall sein,
         wenn die Klasse Konfigurationsparameter oder andere Daten benötigt, die bei der Erstellung des Objekts festgelegt werden müssen.
         */

        // org.springframework.util.StringUtils hätte an der stelle jedoch abstract verwendet, auch wenn das Erstellen nicht direkt ermöglicht ist
        // kann über eine Vererbung jedoch von der Unterklasse ein Objekt Erstellen werden und uns so ungewolltes verhalten bieten.
        Versuch v = new Versuch();


        // Eine weitere Vorgehensweise wäre unsere Hilfsklassen als Enum zu verwenden.
        Enum.addieren();

    }

}