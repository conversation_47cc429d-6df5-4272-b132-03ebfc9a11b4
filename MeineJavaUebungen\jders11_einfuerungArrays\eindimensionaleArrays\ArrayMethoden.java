package jders11_einfuerungArrays.eindimensionaleArrays;

import java.util.Scanner;

public class ArrayMethoden {

    static Scanner sc = new Scanner(System.in);

    public static void main(String[] args) {

        //int[] serie = {5, 55, 1, 5, 8};

        int[] serie = serienWerteEingeben();

        serienWerteAusgeben(serie);

    }

    public static void serienWerteAusgeben(int[] z) {

        for (int i = 0; i < z.length; i++) {
            System.out.println(z[i]);
        }
    }

    public static int[] serienWerteEingeben() {

        int arrayLaenge;

        System.out.print("Wie viele Elemente soll die Array Serie enthalten : ");
        arrayLaenge = sc.nextInt();

        int[] zahlenSerie = new int[arrayLaenge];

        for (int i = 0; i < zahlenSerie.length; i++) {

            System.out.print((i + 1) + ". Element eingeben : ");

            zahlenSerie[i] = sc.nextInt();
        }

        return zahlenSerie;
    }

}
