package jders13_objekte._03_datenkapselung.Anwendung2SetterUndGetter;

public class Student {

    private String vorname;
    private String nachname;
    private int geburtsjahr;
    private String studentenNummer;

    /*
    Grundsätzlich ist es korrekt zu sagen, dass eine Methode mit dem Rückgabetyp "void" dazu dient,
    Werte zu setzen oder zu ändern, anstatt Werte zurückzugeben oder abzurufen.

    Eine Methode mit dem Rückgabetyp "void" führt in der Regel eine Aktion aus, wie zum Beispiel
    das Setzen eines Werts in einer Klasse oder das Ausführen einer bestimmten Operation,
    ohne dabei einen Wert zurückzugeben. Diese Art von Methode wird oft verwendet, um Zustände
    zu ändern, Daten zu aktualisieren oder bestimmte Aktionen auszuführen, die keine Rückgabewerte erfordern.

    Im Gegensatz dazu werden Methoden mit anderen Rückgabetypen, wie beispielsweise "int", "String"
    oder benutzerdefinierten Klassen, verwen<PERSON>, um bestimmte Werte zurückzugeben,
    die von anderen Teilen des Programms verwendet werden können.

    Es ist jedoch wichtig zu beachten, dass dies eine allgemeine Regel ist und es Ausnahmen geben kann.
    Es ist möglich, dass eine Methode mit dem Rückgabetyp "void" auch interne Werte abruft oder andere Aktionen ausführt,
    je nach Funktionalität und Anforderungen des Programms.
    */

    public void setVorname(String vn) {
        // Hier können Sie ihre gewünschte Validierungslogik auch im Konstruktor anwenden
        vorname = vn;
    }

    public String getVorname() {
        return vorname;
    }

    public void setNachname(String nn) {
        nachname = nn;
    }

    public String getNachname() {
        return nachname;
    }

    public void setGeburtsjahr(int gj) {
        geburtsjahr = gj;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setStudentenNummer(String sn) {
        studentenNummer = sn;
    }

    public String getStudentenNummer(){
        return studentenNummer;
    }

}
