package ajava_se.klassen.c03_lokaleKlassen;

public class C03_LocalMitVariableUndInstanz {

    // Instanzvariable der äußeren Klasse
    private String variable = "Ich bin nicht lokal";

    // Diese Methode demonstriert, wie eine lokale Klasse sowohl auf lokale Variablen der Methode
    // als auch auf Instanzvariablen der umgebenden Klasse zugreifen kann.
    public void doIt() {
        final String ausgabe = "Ich bin lokal";

        class LocalClass {
            public String toString(){
                // Hier greifen wir sowohl auf die lokale Variable "ausgabe" als auch auf die
                // Instanzvariable "variable" der umgebenden Klasse zu
                return ausgabe + ", " + variable;
            }
        }
        LocalClass local = new LocalClass();
        System.out.println(local.toString());
    }
}
