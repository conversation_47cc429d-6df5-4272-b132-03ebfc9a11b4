package jders06_01_einfuerungAnwendungDurchschnitt;

import java.util.Scanner;

public class EinfuerungsAnwendung3 {
    public static void main(String[] args) {

        /*
        In diesem Fall wird die ArithmeticException geworfen, um anzuzeigen,
        dass die Eingabe ungültig ist und der berechnete Durchschnitt nicht bestimmt werden kann.
        Wenn die eingegebenen Werte kleiner als 0 oder größer als 100 sind.
        ist, dass es eine Möglichkeit ist, die Ausführung des Programms zu beenden
        und dem Benutzer eine Fehlermeldung anzuzeigen, anstatt ein ungültiges Ergebnis zu liefern.
        */

        Scanner sc = new Scanner(System.in);

        double visum;
        double finall;
        double durchschnitt;

        System.out.print("Geben sie ihre Visum Note ein : ");
        visum = sc.nextDouble();

        System.out.print("Geben sie ihre Final Note ein : ");
        finall = sc.nextDouble();

        durchschnitt = visum * 0.4 + finall * 0.6;

        if (durchschnitt >= 0 && durchschnitt <= 100) {
            System.out.println("Ihr Durchschnitt: " + durchschnitt);

            if (durchschnitt >= 90) {
                System.out.println("Buchstaben note : AA ");
            } else if (durchschnitt >= 80) {
                System.out.println("Buchstaben note : BB ");
            } else if (durchschnitt >= 70) {
                System.out.println("Buchstaben note : CC ");
            } else {
                System.out.println("Buchstaben note : DD ");
            }
        } else {
            throw new ArithmeticException("Ungültiger Durchschnitt: " + durchschnitt);
        }

    }
}
