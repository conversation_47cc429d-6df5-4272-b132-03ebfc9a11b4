package jders05_kontrollstrukturen;

public class IfElseKontrolle {
    public static void main(String[] args) {

        char charakter1 = '$'; // Setze den Wert des ersten Zeichens auf '$'

        char charakter2 = '&'; // Setze den Wert des zweiten Zeichens auf '&'

        // Überprüfe, ob der Wert des ersten Zeichens gleich dem Wert des zweiten Zeichens ist
        if (charakter1 == charakter2) {

            // Gib eine entsprechende Nachricht aus, wenn die Bedingung erfüllt ist
            System.out.println("Charakter 1 ist gleich Charakter 2");
        }
        // Andernfalls, wenn die Zeichen nicht gleich sind
        else {
            // Gib eine entsprechende Nachricht aus, wenn die Bedingung nicht erfüllt ist
            System.out.println("Charakter 1 ist nicht gleich Charakter 2");
        }
    }
}
