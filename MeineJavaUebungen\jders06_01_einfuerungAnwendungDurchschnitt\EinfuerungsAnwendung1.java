package jders06_01_einfuerungAnwendungDurchschnitt;

import java.util.Scanner;

public class EinfuerungsAnwendung1 {
    public static void main(String[] args) {

        // Leider führt unser Code zu unerwünschten verhalten bei - eingaben unter 0

        Scanner sc = new Scanner(System.in);

        double visum;
        double finall;
        double durchschnitt;

        System.out.print("Geben sie ihre Visum Note ein : ");

        visum = sc.nextDouble();

        System.out.print("Geben sie ihre Final Note ein : ");

        finall = sc.nextDouble();

        durchschnitt = visum * 0.4 + finall * 0.6;

        System.out.println("Ihr Durchschnitt : " + durchschnitt);

        if (durchschnitt >= 90 && durchschnitt <= 100) {  // von 100 bis 90
            System.out.println("Buchstaben note : AA ");
        } else if (durchschnitt >= 80 && durchschnitt < 90) {  // von 90 bis 80
            System.out.println("Buchstaben note : BB ");
        } else if (durchschnitt >= 70 && durchschnitt <= 80) {  // von 80 bis 70
            System.out.println("Buchstaben note : CC ");
        } else if (durchschnitt < 70) {                          // von 70 bis 0
            System.out.println("Sie befinden sich unter dem Durchschnitt");
            System.out.println("Buchstaben note : DD ");
        } else {                                                // über 100
            System.out.println("Es konnte kein Passender Durchschnitt berechnet werden! ");
        }

    }
}
