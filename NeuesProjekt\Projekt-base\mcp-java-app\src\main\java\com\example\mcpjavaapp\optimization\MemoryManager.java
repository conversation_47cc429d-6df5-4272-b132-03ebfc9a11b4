package com.example.mcpjavaapp.optimization;

import org.springframework.stereotype.Component;

@Component
public class MemoryManager {

    private final Runtime runtime = Runtime.getRuntime();
    private final long maxMemory = runtime.maxMemory();
    private final double memoryThreshold = 0.8; // 80% Speicherauslastung als Schwellenwert

    public boolean isMemoryAvailable() {
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        double memoryUsage = (double) usedMemory / maxMemory;
        
        return memoryUsage < memoryThreshold;
    }

    public void runGarbageCollection() {
        System.gc();
    }

    public String getMemoryStatus() {
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        double memoryUsage = (double) usedMemory / maxMemory * 100;
        
        return String.format("Speichernutzung: %.2f%% (%.2f MB / %.2f MB)",
                memoryUsage,
                usedMemory / (1024.0 * 1024.0),
                maxMemory / (1024.0 * 1024.0));
    }
}
