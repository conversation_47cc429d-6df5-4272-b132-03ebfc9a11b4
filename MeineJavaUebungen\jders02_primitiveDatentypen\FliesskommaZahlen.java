package jders02_primitiveDatentypen;

import java.math.BigDecimal;

public class FliesskommaZahlen {
    public static void main(String[] args) {
        /*   Typ     Speicher           gültiger Wertebereich          Beispiel       Genauigkeit
            float    32 Bit Fließkomma  +/-1,4E-45 bis +/-3,4E+38      123.45f        6-7 signifikante Stellen
            double   64 Bit Fließkomma  +/-4,9E-324 bis +/-1,7E+308    123.45         15-16 signifikante Stellen
            
            Wichtiger Hinweis:
            Fließkommazahlen sind ungenau für exakte Berechnungen!
            Nicht geeignet für Währungsberechnungen.
        */

        // Basis-Demonstration
        float fValue = 5.1522f;
        double dValue = 78.21312312313;

        System.out.println("Der Wert von fValue ist: " + fValue);
        System.out.println("Der Wert von dValue ist: " + dValue);

        // Genauigkeitsvergleich demonstrieren
        float floatSum = 0.1f + 0.1f + 0.1f;
        double doubleSum = 0.1 + 0.1 + 0.1;
        System.out.println("\nGenauigkeitsvergleich:");
        System.out.println("Float-Summe (0.1+0.1+0.1):  " + floatSum);  // 0.30000004
        System.out.println("Double-Summe (0.1+0.1+0.1): " + doubleSum); // 0.30000000000000004

        // Typische Anwendungsfälle
        System.out.println("\nTypische Anwendungen:");
        System.out.println("float - geeignet für 3D-Grafik, Sensordaten");
        System.out.println("double - Standard für wissenschaftliche Berechnungen");
        
        // BigDecimal für exakte Berechnungen
        System.out.println("\nBigDecimal Alternative:");
        BigDecimal exact1 = new BigDecimal("0.1");
        BigDecimal exact2 = new BigDecimal("0.2");
        System.out.println("0.1 + 0.2 = " + exact1.add(exact2)); // Exakt 0.3
        
        // Warnhinweis
        System.out.println("\nWichtig: Für Währungsberechnungen immer BigDecimal verwenden!");
    }
}
