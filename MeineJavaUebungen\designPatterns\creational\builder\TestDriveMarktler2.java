package designPatterns.creational.builder;

public class TestDriveMarktler2 {

    public static void main(String[] args) {

        /*Nachteile des Builder-Musters:
        Komplexität: Das Builder-Muster führt zusätzlichen Code in Ihrer Anwendung ein, was die Komplexität erhöhen kann.
        Sie müssen nicht nur die Klassen für Ihre Objekte erstellen, sondern auch separate Builder-Klassen erstellen und verwalten.

        Overhead: Das Erstellen von Builder-Klassen und -Methoden erzeugt zusätzlichen Code und damit Overhead.
        Dies kann die Dateigröße und die Wartungskosten erhöhen.

        Schreibarbeit: <PERSON><PERSON><PERSON> <PERSON><PERSON>, die Sie mit einem Builder erstellen möchten, müssen Sie den entsprechenden Builder erstellen
        und die Methoden definieren. Dies kann zeitaufwändig sein, insbesondere wenn es viele Klassen und Builder gibt.

        Unveränderlichkeit nicht erzwungen: Das Builder-Muster erzwingt nicht zwangsläufig die Unveränderlichkeit von Objekten.
        Obwohl Sie Methoden hinzufügen können, um sicherzustellen, dass Objekte nach der Erstellung nicht mehr
        geändert werden können, liegt es immer noch in der Verantwortung des Entwicklers, sicherzustellen, dass dies nicht geschieht.

        Verzögerte Fehlererkennung: Fehler, die bei der Verwendung eines Builders auftreten, können erst zur Laufzeit erkannt werden.
        Zum Beispiel könnten Sie vergessen, eine erforderliche Eigenschaft festzulegen, und erst zur Laufzeit wird eine
        NullPointerException oder ein ähnlicher Fehler ausgelöst.

        Mehr Code: Für einfache Klassen oder Objekte kann das Verwenden eines Builders
        übertrieben erscheinen und mehr Code erfordern als notwendig.

        Lesbarkeit: Obwohl das Builder-Muster die Lesbarkeit des Codes in gewisser Weise verbessert,
        kann es auch zu einer übermäßigen Anzahl von Methodenaufrufen führen, was die Lesbarkeit
        beeinträchtigen kann, insbesondere wenn viele Optionen oder Eigenschaften festgelegt werden müssen.

        Performance: Das Erstellen eines Builder-Objekts und das Festlegen von Eigenschaften kann geringfügige
        Leistungseinbußen verursachen, insbesondere wenn Sie viele Instanzen erstellen.

        Abhängigkeit von der Programmiersprache: Die Verwendung des Builder-Musters kann von der Programmiersprache
        abhängen. In einigen Sprachen ist es einfacher, Builder zu implementieren, während es in anderen schwieriger sein kann.

        Trotz dieser Nachteile kann das Builder-Muster in vielen Situationen sehr nützlich sein, insbesondere wenn Sie komplexe
        Objekte erstellen oder eine bessere Lesbarkeit und Wartbarkeit Ihres Codes erreichen möchten. Es ist wichtig,
        die Vor- und Nachteile abzuwägen und das Muster nur dann zu verwenden, wenn es für Ihre spezifische Anwendung angemessen ist.*/

        // Bei Pflichtparametern wird die Übergabe als parameter verlangt
        Haus haus10 = HausBuilder.startNormalHausBuild("Karl-Marx-Allee","Friedrichshain-Kreuzberg","Berlin",2020,3)
                /*.setStadt("Berlin")
                .setBezirk("Friedrichshain-Kreuzberg")
                .setStrasse("Mollstrasse 6")
                .setGebaedeJahr(2020)
                .setZimmerZahl(3)*/
                .setParkplatz(true)
                .setSpielplatz(true)
                .build();

        // Appartments sind in unserer Anwendung möbliert und als optional vorbestimmt, in unserm Builder-Muster sind die Optionalen aber bekannt
        Haus haus11 = HausBuilder.startAppartmentHausBuild("Frankfurter-Allee 22","Friedrichshain-Kreuzberg","Berlin",2023,4)
                .setParkplatz(true)
                .setSpielplatz(true)
                .build();

        Haus haus12 = HausBuilder.startNormalHausBuild("Mollstrasse 20","Friedrichshain-Kreuzberg","Berlin",1977,3).build();

        System.out.println(haus10);
        System.out.println(haus11);
        System.out.println(haus12);

    }

    private static void printHaus(Haus haus) {

        System.out.println();

        System.out.println("Haus wurde hinzugefügt -> " + haus);

    }

}
