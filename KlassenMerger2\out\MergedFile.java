// Gemergter Code aus dem Verzeichnis: C:\Users\<USER>\JavaPlace\MeineJavaUebungen\ajava_se\klassen\c04_anonymeInnereKlassen

// package c04_anonymeInnereKlassen;

// --- BEGIN DES GEMERGTEN CODES AUS DEM VERZEICHNIS ---

public class C01_BesondererPunkt {

    // In diesem Beispiel wird eine anonyme Klasse verwendet, um die Schnittstelle toString() zu implementieren.
    // Die anonyme Klasse wird innerhalb des Ausdrucks new Point(17, 4) deklariert und implementiert
    // die Methode toString(), um den Punkt als x/y-Koordinaten auszugeben.
    public static void main(String[] args) {
        // Erzeugen eines neuen Punkts mit den Koordinaten 17, 4
        Point p = new Point(17,4){

            //Deklaration einer anonymen Klasse, die die Schnittstelle `toString()` implementiert
            private static final long serialVersionUID = 1L;

            // Überschreiben der Methode `toString()`, um den Punkt als x/y-Koordinaten auszugeben
            @Override
            public String toString() {
                return x+ "/"+y;
            }
        }; // Semikolon um den Konstruktoraufruf abzuschließen

        // dann können wir den Punkt einfach ausgeben und dieser eine Punkt hat dann eben eine andere toString Methode.
        System.out.println(p);
    }
}


public class C02_OuterClass {

    /*Anonyme Klassen sind eine nützliche Möglichkeit, Implementierungen von Interfaces oder abstrakten Klassen zu erstellen.
     Anonyme Klassen haben jedoch einige Einschränkungen, die bei der Verwendung berücksichtigt werden müssen.

    Zusätzliche Details zu anonymen Klassen:
    -Anonyme Klassen können nicht als Klassennamen verwendet werden.
    -Anonyme Klassen können nicht als Überladungsparameter verwendet werden.
    -Anonyme Klassen können nicht als Rückgabewerte von Methoden verwendet werden.
    Erweitere Erläuterung:
    -Anonyme Klassen sind nicht als Eigenschaften einer Klasse deklariert. Sie werden innerhalb eines Ausdrucks definiert
     und können nur innerhalb dieses Ausdrucks verwendet werden.
    -Anonyme Klassen können innerhalb von Anweisungsblöcken, Methoden und statischen Initialisierungsblöcken definiert werden.
    -Anonyme Klassen können nicht als Schnittstellen deklariert werden.
    -Anonyme Klassen können auf Methoden und Variablen der äußeren Klasse zugreifen, die final sind.
    -Anonyme Klassen, die in einer statischen Methode deklariert sind, können keine
     Methoden der äußeren Klasse aufrufen, die ein Objekt der äußeren Klasse benötigen.
    -Anonyme Klassen können nicht als public, private oder static deklariert werden.


    Beispiele für die Verwendung von anonymen Klassen:
    -Anonyme Klassen können verwendet werden, um Implementierungen von Interfaces zu erstellen.
    -Anonyme Klassen können verwendet werden, um Implementierungen von abstrakten Klassen zu erstellen.
    -Anonyme Klassen können verwendet werden, um anonyme Listener zu erstellen.
    -Anonyme Klassen können verwendet werden, um anonyme Funktionen zu erstellen.

    Vorteile von anonymen Klassen:
    -Anonyme Klassen sind eine kompakte Möglichkeit, Implementierungen von Interfaces oder abstrakten Klassen zu erstellen.
    -Anonyme Klassen sind eine flexible Möglichkeit, anonyme Listener oder Funktionen zu erstellen.

    Nachteile von anonymen Klassen:
    -Anonyme Klassen sind nicht wiederverwendbar.
    -Anonyme Klassen können die Lesbarkeit des Codes verringern.*/

    public void method() {

        // Deklaration einer anonymen Klasse vom Typ Runnable
        Runnable runnable = new Runnable() {

            // Die run()-Methode der anonymen Klasse gibt die Nachricht "Hello, world!" aus
            @Override
            public void run() {
                String thread1 = Thread.currentThread().getName(); // Thread-Namen abrufen
                System.out.println("Hello, world! " + Thread.currentThread().getName()); // Thread-Namen abrufen);
            }
        };
        /*In unserem Beispiel ist die anonyme Klasse die Klasse Runnable, die innerhalb des
          Ausdrucks new Thread(runnable).start() deklariert. Die anonyme Klasse hat keinen Namen,
          da wir sie nur innerhalb dieses Ausdrucks verwenden.*/
        // Starten eines neuen Threads mit der anonymen Klasse Runnable als Parameter
        new Thread(runnable).start();
    }

    public static void main(String[] args) {

        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String thread2 = Thread.currentThread().getName();
                System.out.println("Merhabalar " + Thread.currentThread().getName());

            }
        });

        thread.start();
        C02_OuterClass outer = new C02_OuterClass();
        outer.method();
    }
}

public class C02_OuterWiederholung {

    public void method() {

        // Deklaration einer anonymen Klasse vom Typ Runnable
        Runnable runnable = new Runnable() {

            // Die run()-Methode der anonymen Klasse gibt die Nachricht "Hello, world!" aus
            @Override
            public void run() {
                String thread1 = Thread.currentThread().getName(); // Thread-Namen abrufen
                System.out.println("Hello, world! " + Thread.currentThread().getName()); // Thread-Namen abrufen);
            }
        };
        /*In unserem Beispiel ist die anonyme Klasse die Klasse Runnable, die innerhalb des
          Ausdrucks new Thread(runnable).start() deklariert. Die anonyme Klasse hat keinen Namen,
          da wir sie nur innerhalb dieses Ausdrucks verwenden.*/
        // Starten eines neuen Threads mit der anonymen Klasse Runnable als Parameter
        new Thread(runnable).start();
    }

    public static void main(String[] args) {
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                String thread2 = Thread.currentThread().getName();
                System.out.println("Merhabalar " + Thread.currentThread().getName());
            }
        });
        thread.start();
        C02_OuterClass outer = new C02_OuterClass();
        outer.method();
    }
}


public class C02_OuterWiederholung2 {

    public void method() {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                String thread1 = Thread.currentThread().getName(); // Thread-Namen abrufen
                System.out.println("Hello world! " + thread1);
            }
        };
        new Thread(runnable).start();
    }

    public static void main(String[] args) {
        Thread thread = new Thread(new Runnable(){
            @Override
            public void run() {
                String thread2 = Thread.currentThread().getName();
                System.out.println("Merhabalar " + thread2);
            }
        });
        thread.start();
        C02_OuterClass outer = new C02_OuterClass();
        outer.method();
    }

                        // Merhabalar Thread-0
                        // Hello, world! Thread-1
}


public class C03_OuterClassImproved {

    public void method() {
        // Erstelle einen neuen Thread für die "Hello, world!"-Nachricht
        Thread helloWorldThread = new Thread(new Runnable() {
            @Override
            public void run() {
                // Thread-Namen und Nachricht ausgeben
                System.out.println(Thread.currentThread().getName() + ": Hello, world!");
            }
        });

        // Starte den Thread für die "Hello, world!"-Nachricht
        helloWorldThread.start();
    }

    public static void main(String[] args) {
        // Erstelle einen neuen Thread für die "Merhabalar" -Nachricht
        Thread merhabalarThread = new Thread(new Runnable() {
            @Override
            public void run() {
                // Thread-Namen und Nachricht ausgeben
                System.out.println(Thread.currentThread().getName() + ": Merhabalar");
            }
        });

        // Starte den Thread für die "Merhabalar"-Nachricht
        merhabalarThread.start();

        // Erstelle eine Instanz der OuterClass und rufe die Methode auf
        C02_OuterClass outer = new C02_OuterClass();

        outer.method();

    }
                        // Thread-0: Merhabalar
                        // Hello, world! Thread-1
}


// --- ENDE DES GEMERGTEN CODES ---
