package ders05_wrapperClass_MathematischeVorgänge;

import java.util.Scanner;

/**
 * <PERSON>se K<PERSON>e demonstriert, wie man die Quersumme einer Zahl berechnet.
 *
 * Die Quersumme ist die Summe aller Ziffern einer Zahl. Zum Beispiel ist die
 * Quersumme von 257 gleich 2 + 5 + 7 = 14.
 *
 * Der Algorithmus verwendet Modulo-Operation (%) und ganzzahlige Division (/),
 * um die einzelnen Ziffern zu extrahieren und zu addieren.
 *
 * Wichtige Konzepte:
 * - Modulo-Operation (%) zur Extraktion der letzten Ziffer einer Zahl
 * - Ganzzahlige Division (/) zur Entfernung der letzten Ziffer
 * - Schrittweise Verarbeitung einer Zahl von rechts nach links
 */
public class C02_GesamtZahlAllerZiffern {

    /**
     * Die Hauptmethode berechnet die Quersumme einer vom Benutzer eingegebenen Zahl.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // In diesem Beispiel lesen wir eine positive Zahl vom Benutzer ein
        // und berechnen die Summe aller ihrer Ziffern (Quersumme).

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scanner = new Scanner(System.in);

        // Benutzer zur Eingabe auffordern
        System.out.println("Bitte geben Sie eine positive ganze Zahl ein:");

        // Zahl einlesen
        int eingegebeneZahl = scanner.nextInt();

        // Variablen zur Berechnung der Quersumme initialisieren
        int gesamtZiffern = 0;  // Speichert die Summe aller Ziffern
        int zahl = 0;           // Speichert die aktuell extrahierte Ziffer

        System.out.println("\nBerechnung der Quersumme für die Zahl " + eingegebeneZahl + ":");

        // Kopie der Eingabe für die Ausgabe am Ende
        int originalZahl = eingegebeneZahl;

        // SCHRITT 1: Erste Ziffer extrahieren (von rechts)
        System.out.println("\nSCHRITT 1: Erste Ziffer extrahieren");
        System.out.println("Ausgangszustand: eingegebeneZahl = " + eingegebeneZahl +
                          ", zahl = " + zahl + ", gesamtZiffern = " + gesamtZiffern);

        // Modulo 10 gibt die letzte Ziffer einer Zahl zurück
        zahl = eingegebeneZahl % 10;  // Beispiel: 257 % 10 = 7
        System.out.println("zahl = eingegebeneZahl % 10 = " + eingegebeneZahl + " % 10 = " + zahl);

        // Die extrahierte Ziffer zur Gesamtsumme addieren
        gesamtZiffern = gesamtZiffern + zahl;  // Beispiel: 0 + 7 = 7
        System.out.println("gesamtZiffern = gesamtZiffern + zahl = " + (gesamtZiffern - zahl) +
                          " + " + zahl + " = " + gesamtZiffern);

        // Die letzte Ziffer entfernen durch ganzzahlige Division durch 10
        eingegebeneZahl = eingegebeneZahl / 10;  // Beispiel: 257 / 10 = 25 (ganzzahlige Division)
        System.out.println("eingegebeneZahl = eingegebeneZahl / 10 = " + (eingegebeneZahl * 10) +
                          " / 10 = " + eingegebeneZahl);

        // SCHRITT 2: Zweite Ziffer extrahieren
        System.out.println("\nSCHRITT 2: Zweite Ziffer extrahieren");
        System.out.println("Aktueller Zustand: eingegebeneZahl = " + eingegebeneZahl +
                          ", zahl = " + zahl + ", gesamtZiffern = " + gesamtZiffern);

        zahl = eingegebeneZahl % 10;  // Beispiel: 25 % 10 = 5
        System.out.println("zahl = eingegebeneZahl % 10 = " + eingegebeneZahl + " % 10 = " + zahl);

        gesamtZiffern = gesamtZiffern + zahl;  // Beispiel: 7 + 5 = 12
        System.out.println("gesamtZiffern = gesamtZiffern + zahl = " + (gesamtZiffern - zahl) +
                          " + " + zahl + " = " + gesamtZiffern);

        eingegebeneZahl = eingegebeneZahl / 10;  // Beispiel: 25 / 10 = 2
        System.out.println("eingegebeneZahl = eingegebeneZahl / 10 = " + (eingegebeneZahl * 10) +
                          " / 10 = " + eingegebeneZahl);

        // SCHRITT 3: Dritte Ziffer extrahieren
        System.out.println("\nSCHRITT 3: Dritte Ziffer extrahieren");
        System.out.println("Aktueller Zustand: eingegebeneZahl = " + eingegebeneZahl +
                          ", zahl = " + zahl + ", gesamtZiffern = " + gesamtZiffern);

        zahl = eingegebeneZahl % 10;  // Beispiel: 2 % 10 = 2
        System.out.println("zahl = eingegebeneZahl % 10 = " + eingegebeneZahl + " % 10 = " + zahl);

        gesamtZiffern = gesamtZiffern + zahl;  // Beispiel: 12 + 2 = 14
        System.out.println("gesamtZiffern = gesamtZiffern + zahl = " + (gesamtZiffern - zahl) +
                          " + " + zahl + " = " + gesamtZiffern);

        eingegebeneZahl = eingegebeneZahl / 10;  // Beispiel: 2 / 10 = 0
        System.out.println("eingegebeneZahl = eingegebeneZahl / 10 = " + (eingegebeneZahl * 10) +
                          " / 10 = " + eingegebeneZahl);

        // Ergebnis ausgeben
        System.out.println("\nERGEBNIS:");
        System.out.println("Die Quersumme von " + originalZahl + " ist: " + gesamtZiffern);

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scanner.close();

        /*
         * HINWEIS: Dieser Algorithmus funktioniert für Zahlen beliebiger Länge,
         * wenn er in einer Schleife implementiert wird. Beispiel:
         *
         * int zahl = 12345;
         * int quersumme = 0;
         *
         * while (zahl > 0) {
         *     quersumme += zahl % 10;  // Letzte Ziffer addieren
         *     zahl /= 10;              // Letzte Ziffer entfernen
         * }
         *
         * System.out.println("Quersumme: " + quersumme);  // Ausgabe: 15 (1+2+3+4+5)
         */
    }


}
