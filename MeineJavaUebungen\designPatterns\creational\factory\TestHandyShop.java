package designPatterns.creational.factory;

public class TestHandyShop {


    public static void main(String[] args) {

        Handy s22 = HandyFactory.getHandy("s22", "S", 118, 89);

        Handy s23 = HandyFactory.getHandy("s23", "S", 122, 76);

        System.out.println("s22 Technische Eigenschaften ");
        System.out.println(s22);
        System.out.println();
        System.out.println("s23 Technische Eigenschaften ");
        System.out.println(s23);
    }
}
