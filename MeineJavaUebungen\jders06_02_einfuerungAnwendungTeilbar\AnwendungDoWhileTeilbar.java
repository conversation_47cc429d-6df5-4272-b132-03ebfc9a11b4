package jders06_02_einfuerungAnwendungTeilbar;

import java.util.Scanner;

public class AnwendungDoWhileTeilbar {
    public static void main(String[] args) {
        /*
        Die do-while-Schleife funktioniert ähnlich wie die while-<PERSON><PERSON><PERSON><PERSON>,
        aber der Unterschied besteht darin, dass sie den Codeblock mindestens einmal ausführt,
        bevor die Bedingung überprüft wird. Das bedeutet, dass der Code innerhalb der do-while-Schleife
        mindestens einmal ausgeführt wird, unabhäng<PERSON> davon, ob die Bedingung erfüllt ist oder nicht.
        */

        Scanner sc = new Scanner(System.in);

        int anfang;
        int ende;

        System.out.print("Bei welcher Zahl möchten Sie beginnen: ");
        anfang = sc.nextInt();

        System.out.print("Bei welcher Zahl möchten Sie enden: ");
        ende = sc.nextInt();

        int i = anfang;
        do {
            if ((i % 3 == 0) && (i % 4 == 0)) {
                System.out.print(i + " ");
            }
            i++;
        } while (i <= ende);
    }
}
