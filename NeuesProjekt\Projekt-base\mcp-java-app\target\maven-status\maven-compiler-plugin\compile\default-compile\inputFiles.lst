C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\OllamaTest.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\OllamaController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\optimization\MemoryManager.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\CodeAssistantController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\DirectOllamaController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\mcp\ModelContextProtocol.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\model\CodeRequest.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\TestApp.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\config\OllamaConfig.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\config\WebClientConfig.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\McpJavaAppApplication.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\TestController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\LlmController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\service\OllamaService.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\MCPController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\model\PromptRequest.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\optimization\OnnxModelOptimizer.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\optimization\BatchProcessor.java
