C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\OllamaTest.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\optimization\MemoryManager.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\SimpleOllamaController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\model\CodeRequest.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\TestApp.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\EnhancedMcpController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\service\SpringAIOllamaService.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\tools\DocumentationTools.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\config\WebClientConfig.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\McpJavaAppApplication.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\service\SimpleOllamaService.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\controller\TestController.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\config\McpConfig.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\model\PromptRequest.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\optimization\OnnxModelOptimizer.java
C:\Users\<USER>\JavaPlace\NeuesProjekt\Projekt-base\mcp-java-app\src\main\java\com\example\mcpjavaapp\optimization\BatchProcessor.java
