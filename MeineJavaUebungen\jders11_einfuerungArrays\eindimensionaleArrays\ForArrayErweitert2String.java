package jders11_einfuerungArrays.eindimensionaleArrays;

public class ForArrayErweitert2String {
    public static void main(String[] args) {

        String [] namen = {"Ford", "Nike", "Shell", "Nestle", "<PERSON><PERSON>"};

        /*
        * Ohne Index können wir nicht die Elemente einzeln erreichen!
        * Jedoch alle nacheinander mit for ausgeben.
        * for (String i : namen){ System.out.println(name); }
        * */

        for (String name : namen){
            System.out.println(name);
        }

    }
}
