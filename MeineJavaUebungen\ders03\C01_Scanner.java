package ders03;

import java.util.Scanner;

/**
 * Diese Klass<PERSON> demonstriert die grundlegende Verwendung der Scanner-Klasse in Java,
 * um Benutzereingaben von der Konsole einzulesen.
 *
 * Die Scanner-Klasse ist Teil des java.util-Pakets und bietet Methoden zum Einlesen
 * verschiedener Datentypen wie Strings, Zahlen usw.
 */
public class C01_Scanner {
    /**
     * Die Hauptmethode demonstriert das Einlesen eines Benutzernamens mit der Scanner-Klasse.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Wir wollen den Namen des Nutzers einlesen und wieder ausgeben

        // Schritt 1: Scanner-Objekt erstellen
        // Der Scanner wird mit System.in initialisiert, was dem Standard-Eingabestrom (Tastatur) entspricht
        Scanner scan = new Scanner(System.in);

        // Schritt 2: Dem Nutzer mitteilen, was wir erwarten
        System.out.println("Bitte Namen eingeben:");

        // Schritt 3: Mit dem Scanner-Objekt die Nutzereingabe einlesen und in einer Variable speichern
        // nextLine() liest eine komplette Textzeile ein (bis zum Drücken der Enter-Taste)
        String nutzerName = scan.nextLine();

        // Schritt 4: Die eingelesene Information ausgeben
        System.out.println("Eingegebener Nutzername lautet: " + nutzerName);

        // Schritt 5: Scanner schließen, um Ressourcenlecks zu vermeiden
        // Dies ist eine gute Praxis, besonders bei größeren Programmen
        scan.close();
    }
}
