package effectiveJava.effectiveJava01.item1;

public class FCBarcelona {

    private String neuZugangSpieler;
    private int alter;

    public FCBarcelona(String neuZugangSpieler, int alter) {
        this.neuZugangSpieler = neuZugangSpieler;
        this.alter = alter;
    }

    // mit einem Override Vorgang
    public FCBarcelona(String neuZugangTrainer) {
        this.neuZugangSpieler = neuZugangSpieler;
    }

    /* Factory-Methoden werden in der Regel in der Klasse implementiert, die die Eigenschaften
       des zu erstellenden Objekts enthält. Dies ermöglicht es der Factory-Methode,
       auf die privaten Konstruktoren und Eigenschaften der Klasse zuzugreifen und
       somit Objekte auf eine kontrollierte Art und Weise zu erstellen.
       Wie in unserem Beispiel konstant nur 37 Jahre alte Spieler als Neuzugang in die Mannschaft,
       die einen Namen kriegen.*/

    public static FCBarcelona neuSpieler37Produzieren(String neuZugangSpieler){

        // und hier in der Methode erstellen/produzieren wir unseren neuen Fußballer
        // wo der String gegeben wird und wir per Hand auf 37 setzten
        return new FCBarcelona(neuZugangSpieler,37);
    }

    /* Es ist jedoch auch möglich, Factory-Methoden in einer separaten Klasse zu implementieren,
       die dann auf die öffentlichen Konstruktoren und Methoden der Zielklasse zugreift.
       Dies kann beispielsweise sinnvoll sein, wenn mehrere Klassen ähnliche Factory-Methoden haben
       und diese in einer zentralen Factory-Klasse zusammengefasst werden sollen*/
}
