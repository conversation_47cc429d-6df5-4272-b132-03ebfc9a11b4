package ders10_nestedIfElseStatements;

import java.util.Scanner;

public class C02_NestedIfElseFirstSolution {
    public static void main(String[] args) {
         /*
            Beispiel: Nutzer Eingabe Geschlecht und Alter verarbeiten wo,
            <PERSON><PERSON><PERSON>, 60 Jahre und über, Männer 65 und über können in Rente.
            Mit Bezug auf Geschlecht und Alter "Du kannst in Rente" Oder "Um Rente zu Beziehen, musst du ..Jahre noch Arbeiten"
         */

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie ihr Geschlecht an");
        String geschlecht = scan.nextLine();
        System.out.println("Bitte geben sie ihr Alter an");
        Double alter = scan.nextDouble();

        // Erst wählen wir unsere, Haupt variable
        // Beispiel wählen wir das Geschlecht und erstellen unsere Hauptkonstruktion
        // im Fall Geschlecht gibt es die Eingabe Möglichkeiten Männlich, <PERSON><PERSON><PERSON>, ungültig in unserem Beispiel

        if (geschlecht.equalsIgnoreCase("Weiblich")) {
            // Weiblicher teil
            if (alter < 0 || alter > 90) {      // invalid
                System.out.println("Angegebenes Alter ist ungültig");
            } else if (alter < 60) {            // false
                System.out.println("Um Rente zu Beziehen, musst du " + (60 - alter) + " Jahre noch Arbeiten");
            } else {                            // true
                System.out.println("Sie können Rente Beziehen");
            }
        } else if (geschlecht.equalsIgnoreCase("Männlich")) {
            // Männlicher teil
            if (alter < 0 || alter > 90) {      // invalid
                System.out.println("Angegebenes Alter ist ungültig");
            } else if (alter < 65) {            // false
                System.out.println("Um Rente zu Beziehen, musst du " + (65 - alter) + " Jahre noch Arbeiten");
            } else {                            // true
                System.out.println("Sie können Rente Beziehen");
            }
        } else {
            // Ungültiger teil
            System.out.println("Geschlecht Eingabe ungültig");
        }
    }
}
