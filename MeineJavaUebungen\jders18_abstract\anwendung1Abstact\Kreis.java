package jders18_abstract.anwendung1Abstact;

public class Kreis extends Form {

    private double radius;

    public Kreis(double radius) {
        this.radius = radius;
    }

    public double getRadius() {
        return radius;
    }

    public void setRadius(double radius) {
        this.radius = radius;
    }

    @Override
    public double getInhalt() {
        return Math.PI * radius * radius;
    }

    @Override
    public double getUmfang() {
        return 2 * Math.PI * radius;
    }
}
