package jders11_einfuerungArrays.arrayList;

import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

public class ArrayListBearbeitung {
    public static void main(String[] args) {

        Scanner sc = new Scanner(System.in);

        String name;

        // Erstellen einer ArrayList für die Namen
        ArrayList<String> namensListe = new ArrayList<>();

        namensListe.add("pele");
        namensListe.add("messi");
        namensListe.add("tito");
        namensListe.add("diego");
        namensListe.add("silva");

        // Hinzufügen eines weiteren Namens zur Liste und Speichern des Zustands
        boolean addZustand = namensListe.add("ali");

        //System.out.println(addZustand); // true
        //System.out.println(namensListe.get(3));
        System.out.print("Gesuchte name in der Liste : ");
        name = sc.next();

        // Überprüfung, ob der Name in der Liste vorhanden ist
        boolean zustand = namensListe.contains(name);

        if (zustand) {

            System.out.println("Index ihrer suche lautet: " + namensListe.indexOf(name));
        } else {

            System.out.println("Gesuchte name nicht vorhanden!");
        }

        namensListe.remove(0);

        namensListe.remove("diego");

        // Mit for-each die gesamte namensListe ausgeben
        for (String ganzeListe : namensListe) {
            System.out.println(ganzeListe);
        }
        System.out.println();

        // Mit der Methode subList() wird eine neue Liste neueListe erstellt,
        // die nur die ersten beiden Elemente der ursprünglichen Liste enthält.
        // Erstellt eine neue Liste neueListe, die die ersten beiden Elemente der ursprünglichen Liste enthält
        List<String> neueListe = (List<String>) namensListe.subList(0, 2);

        // Iteration über alle Elemente in neueListe und Ausgabe jedes Elements
        for (String string : neueListe) {
            /* Wenn man den Inhalt der neueListe ausgibt, anstatt den splitListe zu verwenden.
             * Durch die Verwendung von splitListe in der System.out.println()-Anweisung wird
             * jeder einzelne Name aus der neueListe separat ausgegeben.
             * Die eckigen Klammern "[messi, tito]" zeigen an, dass es sich um eine Liste handelt.
             * In Java wird die toString()-Methode aufgerufen, um den Inhalt einer Liste auszugeben.
             * Standardmäßig wird der Inhalt der Liste von eckigen Klammern umgeben und die Elemente durch Kommas getrennt.
             * Das ist der Grund, warum [messi, tito] in eckigen Klammern ausgegeben wird.*/
            System.out.println(string);

        }
        
        // Bei Eingabe eines nicht existieren namens in der Liste bekommen wir -1 geliefert
        System.out.println("Index ihrer suche lautet nun  am ende jetzt : " + namensListe.indexOf(name));
        System.out.println();
        System.out.println(namensListe.indexOf(name) == -1 ? "Ausgabe durch ternary Operator: Gesuchte name nicht vorhanden!" : "Ausgabe durch ternary Operator: Index ihrer suche lautet nun  am ende jetzt : " + namensListe.indexOf(name));

    }


}
