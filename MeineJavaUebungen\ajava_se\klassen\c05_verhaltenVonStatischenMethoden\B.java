package ajava_se.klassen.c05_verhaltenVonStatischenMethoden;

/**
 * Unterklasse B, die von A erbt.
 * Diese Klasse demonstriert das "Verstecken" (hiding) einer statischen Methode der Oberklasse.
 */
class B extends A {
    /**
     * Statische Methode, die die gleichnamige Methode aus der Oberklasse A versteckt (hiding).
     * Diese Methode überschreibt NICHT die Methode der Oberklasse im Sinne von Polymorphismus,
     * sondern versteckt sie nur. Welche Methode aufgerufen wird, hängt vom deklarierten Typ
     * der Referenzvariable ab, nicht vom tatsächlichen Objekttyp.
     */
    static void printMessage() {
        System.out.println("Hallo von B");
    }

}
