package jders19_interface.anwendung1Einfuerung;

public interface Printable {

    // Bei Interfaces können alle Methoden von implementierenden Klassen überschrieben werden.

    // Um beides zu Demonstrieren
    void print();

    // Die statische Methode print() ist eine zusätzliche Methode des Interfaces Printable.
    // Diese Methode ermöglicht es, die Methode print() eines Objekts des Interfaces Printable ohne ein Objekt aufzurufen.
    static void print(Printable printable) {  // Sie nimmt ein Objekt des Interfaces Printable als Argument.
        printable.print();
    }
}
