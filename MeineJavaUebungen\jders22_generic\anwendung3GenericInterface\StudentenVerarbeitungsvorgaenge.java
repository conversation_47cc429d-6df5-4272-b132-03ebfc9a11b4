package jders22_generic.anwendung3GenericInterface;

public class StudentenVerarbeitungsvorgaenge implements Verarbeitungsvorgaenge<StudentenVerarbeitungsvorgaenge> {
    @Override
    public boolean speichern(StudentenVerarbeitungsvorgaenge studentenVerarbeitungsvorgaenge) {
        return false;
    }

    @Override
    public boolean loeschen(StudentenVerarbeitungsvorgaenge studentenVerarbeitungsvorgaenge) {
        return false;
    }

    @Override
    public void auflisten() {

    }
}
