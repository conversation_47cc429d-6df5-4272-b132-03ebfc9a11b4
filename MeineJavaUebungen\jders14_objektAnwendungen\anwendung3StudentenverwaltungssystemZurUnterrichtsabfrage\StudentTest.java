package jders14_objektAnwendungen.anwendung3StudentenverwaltungssystemZurUnterrichtsabfrage;

import java.util.ArrayList;

public class StudentTest {
    public static void main(String[] args) {
        // in der Anwendungsform werden hauptsächlich Fächer erstellt.

        ArrayList<String> unterrichtsFaecher = new ArrayList<>();
        ArrayList<String> unterrichtsFaecher2 = new ArrayList<>();

        // Hier erstellen wir unsre vordefinierten Unterrichts Fächer
        unterrichtsFaecher.add("Mathematik");
        unterrichtsFaecher.add("Physik");
        unterrichtsFaecher.add("Chemie");
        unterrichtsFaecher.add("Biologie");

        // Hinzufügen der vordefinierten unterrichtsFaecher2 zur zweiten ArrayList
        unterrichtsFaecher2.add("Englisch");
        unterrichtsFaecher2.add("Französisch");
        unterrichtsFaecher2.add("Russisch");
        unterrichtsFaecher2.add("Japanisch");
        unterrichtsFaecher2.add("Spanisch");
        unterrichtsFaecher2.add("Deutsch");

        // Erstellt Instanz mit diesen Werten
        Student student1 = new Student("Jörn", "Decker", 1995, "2333", unterrichtsFaecher);

        Student student2 = new Student("Björn", "Neckar", 1994, "2332", unterrichtsFaecher2);

        // Erstellung einer Instanz der Klasse StudentenVerarbeitungsVorgaenge, um darauf Methoden aufrufen zu können
        StudentenVerarbeitungsvorgaenge verarbeitungsVorgaenge = new StudentenVerarbeitungsvorgaenge();

        // Abfrage Fach für unsere Verarbeitungs-Vorgänge. Überprüft wird dieser String an der Stelle.
        String fach9 = "Mathematik"; // "Elektrotechnik"

        // Sendet zur Überprüfung eine Instanz und das gesuchte Fach als "String" oder auch fach9 variable in unserem fall:Mathematik,
        // an unsere unterrichtsAbfrage Methode in der Klasse StudentenVerarbeitungsVorgaenge
        verarbeitungsVorgaenge.unterrichtsAbfrage(student1, fach9);  // "Chemie"
        verarbeitungsVorgaenge.unterrichtsAbfrage(student2, fach9);// "Deutsch"

        /*
        // Wir erreichen direkt eine andere Methode mit der Instanz, gültig für jede Student-Instanz
        verarbeitungsVorgaenge.studentenInfosZeigen(student1);
        System.out.println();
        verarbeitungsVorgaenge.studentenInfosZeigen(student2);
        */
    }

}
