package designPatterns.creational.prototype.violation.prototypeWiederholung1;

public class Dokument {

    private long id;
    private String name;
    private DokumentTyp dokumentTyp;
    private Kategorie kategorie;
    private String inhalt;

    public Dokument() {
    }

    public Dokument(long id, String name, DokumentTyp dokumentTyp, Kate<PERSON>ie kategorie, String inhalt) {
        this.id = id;
        this.name = name;
        this.dokumentTyp = dokumentTyp;
        this.kategorie = kategorie;
        this.inhalt = inhalt;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public DokumentTyp getDokumentTyp() {
        return dokumentTyp;
    }

    public void setDokumentTyp(DokumentTyp dokumentTyp) {
        this.dokumentTyp = dokumentTyp;
    }

    public Kategorie getKategorie() {
        return kategorie;
    }

    public void setKategorie(Kategorie kategorie) {
        this.kategorie = kategorie;
    }

    public String getInhalt() {
        return inhalt;
    }

    public void setInhalt(String inhalt) {
        this.inhalt = inhalt;
    }

    @Override
    public String toString() {
        return "Dokument{" +
                "\nid=" + id +
                "\n, name='" + name + '\'' +
                "\n, dokumentTyp=" + dokumentTyp +
                "\n, kategorie=" + kategorie +
                "\n, inhalt='" + inhalt + '\'' +
                '}';
    }
}
