package jders11_einfuerungArrays.eindimensionaleArrays;

public class ArrayFortlaufendeBerechnung {
    public static void main(String[] args) {

        int[] nums = {1, 2, 3, 4}; // Beispiel-Eingabe

        int[] runningSum = new int[nums.length]; // Array zur Speicherung der Running Sum erstellen
        runningSum[0] = nums[0]; // Der erste Wert der Running Sum ist gleich dem ersten Wert im Input-Array

        for (int i = 1; i < nums.length; i++) {
            runningSum[i] = runningSum[i - 1] + nums[i]; // Laufende Summe berechnen: Summe des vorherigen Elements in der Running Sum und des aktuellen Elements im Input-Array
        }

        // Ausgabe der Running Sum
        System.out.print("Output: [");
        for (int i = 0; i < runningSum.length; i++) {
            System.out.print(runningSum[i]);
            if (i < runningSum.length - 1) {
                System.out.print(", ");
            }
        }
        System.out.println("]");
    }
}
