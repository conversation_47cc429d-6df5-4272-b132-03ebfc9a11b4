package ders07_concatenation_operators;

public class C02_02_Relational_Operators {
    public static void main(String[] args) {

        int a = 10;
        int b = 15;

        System.out.println(a == b);  // false
        System.out.println(a == b - 5);  // true
        boolean c;
        System.out.println(c = 15 == b);  // zuerst wird Rechts der wert gefunden und dan findet Links die Zuweisung statt boolean = true
        c = 15 * a == 10 * b;
        System.out.println(c);  // immer true

        /*
        !true -> false.     wandelt den wert der Logischen aussage zum Gegensatz um
        !(5==5) -> false.   immer wenn zu Beginn ein! steht, wandelt der wert zum Gegensatz um
        5 !=5 -> false.     fünf ist nicht fünf
         */

        System.out.println(a != b);  // true
        System.out.println(a != b - 5);  // false
        System.out.println(c = 15 != b);  // false
        c = 15 * a != 10 * b;  // c = false
        System.out.println(c);  // immer false

        /*
        Logische Operatoren &&(and), &
        && Nur wenn beide Bedingungen oder auch alle weiteren && true sind, ist Ergebnis = true
        & ist langsam, weil wer weiter vergleichen tut und am Ende bewertet false, während && beim ersten false abbricht und
        bewertet aber im anderen fall auch bis zum Ende vergleichen muss
         */
        System.out.println(a > b && b > 0);  // 10 > 15 && 15 > 0 f+t=false
        System.out.println(a <= b - 5 && a > b - 8);  // 10 <= 10 && 10 > 7 t+t=true
        System.out.println(c = 15 >= b && a < 0);  // 15 <= 15 && 10 < 0 t+f=false
        c = a >= b && 3 * a < 4 * b;  // f
        System.out.println(c);  // false

        System.out.println(a<b && b<10 && b>=a && a<0);  // t && f -- -- = false
        System.out.println(a<b &b<10 & b>=a & a<0);  // t & f & t & f = false

        /*
        ||(or) wenn nur eine Bedingung erfüllt ist = true;
        nur wenn alle Bedingungen false sind bekommen wir false
         */
    }
}
