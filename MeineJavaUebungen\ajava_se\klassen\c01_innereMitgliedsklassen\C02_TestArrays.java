package ajava_se.klassen.c01_innereMitgliedsklassen;

public class C02_TestArrays {
    public static void main(String[] args) {
        String[] namen = {"<PERSON>", "<PERSON>", "<PERSON>"};
        int[] werte = {42, 68, 95};

        C02_ArraysAussenKlasse ak = new C02_ArraysAussenKlasse(namen);
        C02_ArraysAussenKlasse.InnereKlasse ik = ak.new InnereKlasse(werte);

        ak.gebeNamenAus();
        ik.gebeWerteAus();
    }
}
/*  Die Verwendung von inneren Klassen in Java kann in verschiedenen Anwendungsfällen nützlich sein.
    Einige der häufigsten Anwendungen sind:

   -Kapselung und Abstraktion: Sie können innere Klassen verwenden, um Teile Ihrer Implementierung zu kapseln
    und zu abstrahieren. Dies ist hilfreich, wenn Sie Teile einer Klasse verstecken und nur
    für den internen Gebrauch sichtbar machen möchten. Andere Klassen können nicht auf diese inneren Klassen zugreifen.

   -Event-Handling: In grafischen Benutzeroberflächenanwendungen, wie Swing oder JavaFX, werden oft
    innere Klassen verwendet, um Event-Handler zu implementieren. Diese Klassen können auf Ereignisse
    reagieren und auf die Zustände der umgebenden Klasse zugreifen.

   -Iterator-Implementierung: Sie können innere Klassen verwenden, um Iteratoren für Sammlungen zu implementieren.
    Innere Klassen können leicht auf die Elemente und Struktur der umgebenden Klasse zugreifen.

   -Datenkapselung: Wenn Sie in einer Klasse spezialisierte Datenstrukturen benötigen, die nur
    in diesem Kontext sinnvoll sind, können Sie innere Klassen verwenden, um diese zu definieren. Dies ist nützlich,
    um den Code aufzuräumen und sicherzustellen, dass diese speziellen Strukturen nicht von außen manipuliert werden.

   -Implementierung von Schnittstellen: Wenn eine innere Klasse eine bestimmte Schnittstelle implementiert,
    können Sie eine Instanz dieser Klasse erstellen, die diese Schnittstelle erfüllt. Dies ist nützlich,
    wenn Sie eine bestimmte Implementierung benötigen.

   -Vermeidung von Klassenvermüllung: Wenn die innere Klasse nur in einem sehr begrenzten Kontext sinnvoll ist
    und keinen größeren Anwendungsbereich hat, können Sie sie als innere Klasse deklarieren, um Ihren Code
    organisierter zu gestalten und den Namensraum zu entlasten.

   -Callback-Mechanismen: Sie können innere Klassen als Callbacks verwenden, um asynchrone Vorgänge zu behandeln,
    beispielsweise bei der Verwendung von Threads oder bei asynchronen Schnittstellen.

   -Verwendung von Helper-Klassen: Wenn eine Klasse Hilfsmethoden benötigt, die nur in dieser Klasse benötigt werden,
    können Sie diese als innere Hilfsklassen erstellen.

   Die Verwendung von inneren Klassen hängt stark von den Anforderungen Ihres Projekts ab. Sie sind besonders nützlich,
   wenn Sie zusätzliche Kapselung und Struktur in Ihrem Code benötigen, um die Wartbarkeit und Lesbarkeit zu verbessern.*/
