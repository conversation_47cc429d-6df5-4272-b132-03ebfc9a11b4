package jders13_objekte._02_objektZugriffsmodifikatoren.protectedAnwendung;


import jders13_objekte._02_objektZugriffsmodifikatoren.Auto;

public class ProtectedAnwendung1 extends Auto {

    /* 1. Um den Konstruktor einer Klasse zu verwenden: Das super-Schlüsselwort wird verwendet,
     *um den Konstruktor der Oberklasse aufzurufen. Dies ist nütz<PERSON>, wenn eine Unterklasse
     * die Eigenschaften der Oberklasse erben und initialisieren möchte.
     *
     * Um die Methode einer Oberklasse aufzurufen: Das super-Schlüsselwort wird verwendet,
     * um auf eine geerbte Methode der Oberklasse zuzugreifen und diese aufzurufen.
     * Dies ermöglicht es einer Unterklasse, die Implementierung der Oberklasse zu erweitern
     * oder zu überschreiben, während sie dennoch auf die Funktionalität der Oberklasse zugreifen kann.
     *
     * Um Namenskonflikte in einer Vererbungshierarchie zu vermeiden: Wenn in
     * einer Vererbungshierarchie Methoden oder Eigenschaften mit demselben Namen
     * sowohl in der Oberklasse als auch in der Unterklasse vorhanden sind,
     * kann das super-Schlüsselwort verwendet werden, um auf die Version der Oberklasse zuzugreifen.
     * Dies ist hilfreich, um sicherzustellen, dass die gewünschte Methode oder
     * Eigenschaft verwendet wird und keine Verwechslungen auftreten.
     *
     * Es ist jedoch wichtig zu beachten, dass die Verwendung von super kontextabhängig
     * ist und von der jeweiligen Situation abhängt. Die genaue Verwendung von super hängt von den
     * spezifischen Anforderungen und dem Design des Programms ab.
     */

    public static void main(String[] args) {
        ProtectedAnwendung1 car1 = new ProtectedAnwendung1("BMW", "528i", 2013, "Blau");
        System.out.println(car1.marke);
        System.out.println(car1.model);
        System.out.println(car1.jahr);
        System.out.println(car1.farbe);
    }

    /*
     * super-Schlüsselwort verweist auf die Oberklasse und dient dazu,
     *  den Konstruktor der Oberklasse aufzurufen und die entsprechenden Argumente zu übergeben.
     *  Mit dem Aufruf super(mrk, mdl, j, frb); wird also der Konstruktor der Oberklasse aufgerufen,
     *  um sicherzustellen, dass die geerbten Eigenschaften initialisiert werden,
     *  bevor die spezifischen Initialisierungen der Unterklasse ProtectedAnwendung1 stattfinden.
     */
    public ProtectedAnwendung1(String mrk, String mdl, int j, String frb) {
        super(mrk, mdl, j, frb);
    }
}
