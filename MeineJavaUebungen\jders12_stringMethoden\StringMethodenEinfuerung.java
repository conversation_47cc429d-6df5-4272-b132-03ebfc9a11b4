package jders12_stringMethoden;

public class StringMethodenEinfuerung {
    public static void main(String[] args) {

        String nameNachname = "Özer Özözbek";

        char c = nameNachname.charAt(6);

        System.out.println("Charakter an 6. stelle lautet : " + c);

        int i = nameNachname.indexOf("e");
        System.out.println("Index von des Buchstaben 'e' (Suche beginnt von Anfang) lautet : " + i);

        i = nameNachname.lastIndexOf("e");
        System.out.println("Index von des Buchstaben 'e' (Suche beginnt vom Ende) lautet : " + i);

        System.out.println("Der Name und Der Nachname hat insgesamt " + nameNachname.length() + " Asci Charakter");

        String kleinBst = nameNachname.toLowerCase();
        System.out.println("name nachname in klein Buchstaben : " + kleinBst);
        String grossBst = nameNachname.toUpperCase();
        System.out.println("NAME UND NACHNAME IN GROß BUCHSTABEN : " + grossBst);
        // Beliebig den Sting Bearbeiten
        String inputWechsel = nameNachname.replace('ö', 'o').replace('Ö', 'O');
        System.out.println("Ohne Groß und klein Ö : " + inputWechsel);

        // Setzt alle Werte von (String) nameNachname array in (char) buchstabenSerie array
        char[] buchstabenSerie = nameNachname.toCharArray();

        for (char cc : buchstabenSerie) {
            System.out.println(cc);
        }

    }
}
