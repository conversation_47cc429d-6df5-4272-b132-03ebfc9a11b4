package jders14_objektAnwendungen.anwendung5StudentenVararbeitungen;

import java.util.ArrayList;

public class Student {

    private String vorname;

    private String nachname;

    private int geburtsjahr;

    private String studentenNummer;

    private ArrayList<String> unterrichtsFaecher;

    public Student() {

    }

    // Konstruktor zur Initialisierung der Attribute
    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer, ArrayList<String> unterrichtsFaecher) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
        this.studentenNummer = studentenNummer;
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    // Wichtig ist nur die Reihenfolge in der die Methoden an einer bestimmten Stelle aufgerufen werden.

    // Setzt die Liste der Unterrichtsfächer
    public void setUnterrichtsFaecher(ArrayList<String> unterrichtsFaecher) {
        // in dieser Klasse die Fächer sind this.unterrichtsFaecher = über parameter gegebene Liste "Gesendet"
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    // Parameter gibt eine ArrayList<String> zurück, und nimmt keine Parameterliste() an, deshalb (leer)
    public ArrayList<String> getUnterrichtsFaecher() {
        // return private unterrichtsFaecher aus der klasse
        return unterrichtsFaecher;
    }

    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }
    
    @Override
    public String toString() {
        // Gibt eine String-Repräsentation des Studenten-Objekts zurück
        StringBuilder sb = new StringBuilder();
        sb.append("Student{"); // Fügt den Anfang der Zeichenkette hinzu
        sb.append("vorname='").append(vorname).append('\''); // Fügt den Vornamen hinzu
        sb.append(", nachname='").append(nachname).append('\''); // Fügt den Nachnamen hinzu
        sb.append(", geburtsjahr=").append(geburtsjahr); // Fügt das Geburtsjahr hinzu
        sb.append(", studentenNummer='").append(studentenNummer).append('\''); // Fügt die Studentennummer hinzu
        sb.append(", unterrichtsFaecher=["); // Fügt den Anfang der Unterrichtsfächer-Liste hinzu

        if (unterrichtsFaecher != null) { // Überprüft, ob die Liste der Unterrichtsfächer nicht null ist
            for (int i = 0; i < unterrichtsFaecher.size(); i++) {
                String fach = unterrichtsFaecher.get(i); // Aktuelles Fach an der Indexposition i abrufen

                if (fach != null) { // Überprüft, ob das Fach nicht null ist
                    sb.append(fach); // Fügt das Fach zur Zeichenkette hinzu
                } else {
                    sb.append("N/A"); // Fügt einen Platzhalterwert "N/A" für null-Fächer hinzu
                }

                if (i != unterrichtsFaecher.size() - 1) {
                    sb.append(", "); // Fügt ein Komma und ein Leerzeichen hinzu, wenn es weitere Fächer gibt
                }
            }
        }

        sb.append("leer]}"); // Fügt das Ende der Zeichenkette hinzu
        return sb.toString(); // Konvertiert den StringBuilder in einen String und gibt ihn zurück
    }

}
