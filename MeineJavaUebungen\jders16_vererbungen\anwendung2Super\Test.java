package jders16_vererbungen.anwendung2Super;

import java.util.ArrayList;

public class Test {
    public static void main(String[] args) {

        //Ausgabe von dem Constructor mit 5 Parametern, erfolgt ohne die toString Methode

        // Student Objekt erstellen
        Student student1 = new Student("Sam", "Wolle", 1998, "2380", null);

        ArrayList<String> faecher = new ArrayList<>();
        faecher.add("Mathematik");
        faecher.add("Physik");
        faecher.add("Chemie");

        student1.setUnterrichtsFeacher(faecher);

        System.out.println("Name : "+ student1.getVorname());
        System.out.println("Nachname : "+ student1.getNachname());
        System.out.println("Geburtsjahr : "+ student1.getGeburtsjahr());
        System.out.println("Studenten Nummer : "+ student1.getStudentenNummer());
        System.out.println("Unterrichts Fächer : " + student1.getUnterrichtsFeacher());


    }

}
