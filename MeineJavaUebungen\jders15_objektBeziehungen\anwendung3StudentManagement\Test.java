package jders15_objektBeziehungen.anwendung3StudentManagement;

import java.util.ArrayList;

public class Test {
    public static void main(String[] args) {

        // Name der Schule
        Student.setSchule("University of Oxford");

        Student student1 = new Student("<PERSON>j<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 1995, "2351");

        Adressen adresse1 = new Adressen("2", "Hauptstraße", "10247", "Berlin", "Deutschland");

        // Zuweisung der adresse1 zu student1 Beziehungsverbindung
        student1.setAdresse(adresse1);

        // Erstellung der Fahrzeugdaten
        Fahrzeug fahrzeug1 = new Fahrzeug("Mercedes", "CLA", 2016);
        Fahrzeug fahrzeug2 = new Fahrzeug("Toyota", "Yaris", 2023);

        // Wir erstellen eine Liste für Fahrzeug Objekte, um mehrere Fahrzeuge dieser Liste zuzuweisen
        ArrayList<Fahrzeug> fahrzeuge = new ArrayList<>();
        fahrzeuge.add(fahrzeug1);
        fahrzeuge.add(fahrzeug2);

        // um den studenten1 mehrere Fahrzeuge zuzuweisen, erhält er die Liste fahrzeuge
        student1.setFahrzeuge(fahrzeuge);

/*        ArrayList<String> faecher = new ArrayList<>();
        faecher.add("Mathematik");
        faecher.add("Physik");
        faecher.add("Chemie");

        // Zuweisung auf der Seite, wo die fächer gesetzt werden*/


        ArrayList<String> telefonnummer1 = new ArrayList<>();
        telefonnummer1.add("03075525500");
        telefonnummer1.add("01779767235");
        telefonnummer1.add("01762805018");

        // Zuweisung auf der Seite, wo die Telefonnummern übergeben werden
        student1.setTelefonnummern(telefonnummer1);

        System.out.println("----------------------------Student 1 Werte erstellt & zugewiesen.----------------------------");
        // --------------------Student2 Erstellen--------------------
        // Anschließend könnte "Bruder vom Student1" auch weiters gleiche an Fahrzeug, Telefon oder
        // Adress Datensätzen erhalten. In diesem Fall erstellen wir neue Datensätze für jemand anderes.
        Student student2 = new Student("Björn", "Hausse", 1998, "2352");
        // Neue Adresse erstelln
        Adressen adressen2 = new Adressen("10d", "Berlinerstrasse", "10100", "Berlin", "Deutschland");
        // Ein neues zu erstellendes Fahrzeug
        Fahrzeug fahrzeug3 = new Fahrzeug("Ford", "Ka", 2012);
        // Einzelnes Fahrzeug muss ebenfalls in eine ArrayList für die Ausgabe einer ArrayList mit einem Produkt
        ArrayList<Fahrzeug> fahrzeuge2 = new ArrayList<>();
        fahrzeuge2.add(fahrzeug3);

        student2.setAdresse(adressen2);
        student2.setFahrzeuge(fahrzeuge2);
        ArrayList<String> telefonnummer2 = new ArrayList<>();
        telefonnummer2.add("0308002550");
        telefonnummer2.add("01579080200");

        student2.setTelefonnummern(telefonnummer2);
        System.out.println("----------------------------Student 2 Werte erstellt & zugewiesen.----------------------------");

        // System.out.println(student1);

        studentInfoAusgabe(student1);

        System.out.println();

        studentInfoAusgabe(student2);

    }

    public static void studentInfoAusgabe(Student std) {

        System.out.println("Name : " + std.getVorname() + '\'');
        System.out.println("Nachname  : " + std.getNachname());
        System.out.println("Geburtsjahr  : " + std.getGeburtsjahr());
        System.out.println("Studenten Nummer : " + std.getStudentenNummer());
        // ArrayList Werte werden nebeneinander ausgegeben
        System.out.println("Telefonnummern : " + std.getTelefonnummern());
        System.out.println("Unterrichts Fächer  : " + std.getUnterrichtsFaecher());
        // Static Wert
        System.out.println("Schule : " + Student.getSchule());
        // Zu beachten das die Fahrzeuge und Adresse Klassen eine eigene toString Methode besitzen,
        // die Hier and der Stelle mit einwirkt. Zum Format der Ausgabe ist die jeweilige Klasse der Methode zuständig.
        System.out.println("Fahrzeuge : " + std.getFahrzeuge());
        System.out.println("Adresse : " + std.getAdresse());

        // Wir nehmen die Fahrzeug liste von std.
        ArrayList<Fahrzeug> fahrzeuge = std.getFahrzeuge();

        // Jedes Fahrzeug oder Fahrzeug-Werte in eigener Zeile ausgeben
        System.out.println("Fahrzeuge : ");
        for (Fahrzeug fahrzeug : fahrzeuge) {
            // System.out.println(fahrzeug.getMarke());
            System.out.println(fahrzeug);
        }


    }


}
