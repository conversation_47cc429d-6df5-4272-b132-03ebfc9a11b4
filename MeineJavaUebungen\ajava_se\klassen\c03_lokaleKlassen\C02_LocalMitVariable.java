package ajava_se.klassen.c03_lokaleKlassen;

public class C02_LocalMitVariable {

    // Diese K<PERSON>e demonstriert, wie eine lokale Klasse auf lokale Variablen der umgebenden Methode zugreifen kann.
    // Die Variable muss final oder "effectively final" sein (seit Java 8).
    public static void main(String[] args) {

        final String ausgabe = "Ich bin Lokal";

        class LocalClass {
            public String toString() {
                // Hier greifen wir auf die finale Variable "ausgabe" aus der umgebenden Methode zu
                return ausgabe;
            }
        }

        LocalClass local = new LocalClass();
        System.out.println(local.toString());
    }
}