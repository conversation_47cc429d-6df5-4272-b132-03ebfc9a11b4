package jders20_tryCatch.anwendung3AutoClosable;

public class MyResource implements java.lang.AutoCloseable {
    // Konstruktor und andere Methoden

    @Override
    public void close() throws Exception {
        // Führen Sie hier die Aufräumarbeiten durch, z.B. das Schließen einer Datei.
        System.out.println("Die close()-Methode wurde aufgerufen.");
    }

    public static void main(String[] args) {
        try (MyResource resource = new MyResource()) {
            // Ressourcenarbeit
        } catch (Exception e) {
            // Fehlerbehandlung
        }
    }

}

