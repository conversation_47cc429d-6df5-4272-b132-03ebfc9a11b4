package effectiveJava.effectiveJava02;

public class KaffeHausBuilder {

    // Obligatorischer Parameter für die Kaffee-Größe
    private final String kaffeeGroesse;

    // Optionale Parameter für individuelle Auswahl
    private String laktoseFreiMilch;
    private String kakao;
    private String vanille;

    public static class KHBuilder {

        private final String kaffeeGroesse;
        private String laktoseFreiMilch;
        private String kakao;
        private String vanille;

        // Konstruktor für den Builder mit dem obligatorischen Parameter für die Kaffee-Größe
        public KHBuilder(String kaffeeGroesse) {
            // Überprüfen, ob die Kaffee-Größe nicht null oder nur aus Leerzeichen besteht
            if (kaffeeGroesse == null || kaffeeGroesse.trim().isEmpty()) {
                // Die Methode trim() entfernt alle Leerzeichen am Anfang und am Ende des Strings, sodass wir sicherstellen
                // können, dass der Parameter nicht nur aus Leerzeichen besteht.
                throw new IllegalArgumentException("Kaffee-Größe darf nicht leer sein");
            }

            this.kaffeeGroesse = kaffeeGroesse;
        }

        // Methoden zum Setzen optionaler Parameter
        public KHBuilder laktosefreiMilchGruppe(String ohneLaktose) {
            this.laktoseFreiMilch = ohneLaktose;
            return this;
        }

        public KHBuilder kakaoGruppe(String mitKakao) {
            this.kakao = mitKakao;
            return this;
        }

        public KHBuilder vanilleGruppe(String mitVanille) {
            this.vanille = mitVanille;
            return this;
        }

        // Methode zum Erstellen eines KaffeHausBuilder-Objekts
        public KaffeHausBuilder build() {
            // Rückgabe eines neuen KaffeHausBuilder-Objekts, indem der Builder verwendet wird
            return new KaffeHausBuilder(this);
        }
    }

    // Konstruktor der KaffeHausBuilder-Klasse, der den Builder verwendet
    public KaffeHausBuilder(KHBuilder khBuilder) {
        kaffeeGroesse = khBuilder.kaffeeGroesse;
        laktoseFreiMilch = khBuilder.laktoseFreiMilch;
        kakao = khBuilder.kakao;
        vanille = khBuilder.vanille;
    }
}
