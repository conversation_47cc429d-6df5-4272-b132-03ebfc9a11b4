package probecode03_concurrency.threads;

public class PubSub extends Thread {
    // Es gibt derzeitig keine besonderen Sicherheitsbedenken in diesem Codeausschnitt

    /* Die<PERSON> zeigt, wie Threads in Java asynchron arbeiten können und wie wir die Ausgabe steuern können,
       wenn mehrere Threads beteiligt sind. Der Code definiert eine PubSub-Klasse, die die Thread-Klasse erweitert.
       Der Hauptthread gibt "Sleep is over." und "Pubsub is terminated." aus.
       Der erstellte Thread gibt "Message empfangen" aus, nachdem notified auf true gesetzt wurde.
       Dies zeigt, wie Threads in Java asynchron arbeiten können und wie wir die Ausgabe steuern können,
       wenn mehrere Threads beteiligt sind.
    */

    volatile boolean notified = false;

    @Override
    public void run() {
        // Wir nutzen eine Schleife mit einem leeren Körper die zur einer endlosen Schleife führt.
        // Unter anderem könnte dies zu einer unerwünschten CPU-Auslastung führen.
        while (!notified){

        }
        System.out.println("Message empfangen");
    }

    /*In der main-Methode wird eine Instanz der Klasse PubSub erzeugt.
    Die start-Methode wird auf dem pubSub-Objekt aufgerufen, um den Thread zu starten.
    Der Haupt-thread schläft dann für 2000 Millisekunden mit Thread.sleep.
    Nach dem Schlaf wird "Sleep is over." ausgegeben und die notified-Variable des pubSub-Objekts wird auf true gesetzt.
    Die join-Methode wird für das pubSub-Objekt aufgerufen, um auf die Beendigung des pubSub-Threads zu warten.
    Schließlich wird "Pubsub is terminated." gedruckt*/
    public static void main(String[] args) throws InterruptedException {

        PubSub pubSub = new PubSub();
        pubSub.start();
        Thread.sleep(2000);
        System.out.println("Sleep is over.");
        pubSub.notified = true;
        // wartet bis der Pubsub tread zu Ende ist
        pubSub.join();
        System.out.println("Pubsub is terminated.");


        /* Mögliche Fehler:
          -Die benachrichtigte Variable ist in der PubSub-Klasse nicht als flüchtig deklariert.
           Dies kann in einer Multi-Thread-Umgebung zu Sichtbarkeitsproblemen führen und dazu führen,
           dass die Schleife in der Run-Methode nicht beendet wird, auch wenn die benachrichtigte Variable auf true gesetzt wurde
          -Die Schleife in der Run-Methode ist nicht effizient und kann zu einer hohen CPU-Auslastung führen,
           da sie den Wert der benachrichtigten Variablen kontinuierlich und ohne Verzögerung überprüft.

           Leistungsmetriken
           Besetztes Warten: Die Run-Methode der PubSub-Klasse verwendet Busy Waiting, was ineffizient sein kann,
           da sie kontinuierlich und ohne Verzögerung das notified flag überprüft. Dies kann unnötig CPU-Ressourcen verbrauchen.
           Thread-Schlaf: Der Aufruf Thread.sleep(2000) in der main-Methode führt eine feste Verzögerung von 2 Sekunden ein.
           Dies kann die Leistung beeinträchtigen, wenn die Verzögerung länger als nötig ist.

           Mögliche Verbesserungen
          -Wir könnten ein Lock oder Semaphore anstelle eines flüchtigen Booleans nutzen, um die Sicherheit des Threads
           und die richtige Synchronisation zu gewährleisten.
          -Implementierung eines Mechanismus zur Benachrichtigung des PubSub-Threads,
           anstatt eine Busy-Wait-Schleife zu verwenden. Dies kann mit wait- und notify-Methoden
           oder höheren Konstrukten wie CountDownLatch oder CyclicBarrier erreicht werden.
          -Wir könnten auch eine Verzögerung oder einen Ruhezustand innerhalb der Schleife in der Run-Methode einsetzen,
           um die CPU-Auslastung zu verringern und die Leistung zu verbessern.

          */
    }
}
