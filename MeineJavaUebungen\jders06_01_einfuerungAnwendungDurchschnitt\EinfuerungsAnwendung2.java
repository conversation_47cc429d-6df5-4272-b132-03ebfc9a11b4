package jders06_01_einfuerungAnwendungDurchschnitt;

import java.util.Scanner;

public class EinfuerungsAnwendung2 {
    public static void main(String[] args) {

        /*Im ursprünglichen Code wurde die Überprüfung innerhalb der Bedingungen für die Buchstaben-Note
          durchgeführt, was zu einem unerwünschten Verhalten führte.
          Überprüfung vor den Bedingungen des Buchstaben wertes für D.<=0 sowohl auch D.>=100
        */
        Scanner sc = new Scanner(System.in);

        double visum;
        double finall;
        double durchschnitt;

        System.out.print("Geben Sie ihre Visum Note ein: ");
        visum = sc.nextDouble();

        System.out.print("Geben Sie ihre Final Note ein: ");
        finall = sc.nextDouble();

        durchschnitt = visum * 0.4 + finall * 0.6;

        if (durchschnitt >= 0 && durchschnitt <= 100) {
            System.out.println("Ihr Durchschnitt: " + durchschnitt);

            if (durchschnitt >= 90 && durchschnitt <= 100) {
                System.out.println("Buchstaben note: AA");
            } else if (durchschnitt >= 80 && durchschnitt < 90) {
                System.out.println("Buchstaben note: BB");
            } else if (durchschnitt >= 70 && durchschnitt < 80) {
                System.out.println("Buchstaben note: CC");
            } else {
                System.out.println("Buchstaben note: DD");
            }

        } else {
            System.out.println("Es konnte kein Passender Durchschnitt berechnet werden!");
        }


    }
}
