package jders11_einfuerungArrays.eindimensionaleArrays;

public class ArrayEinfuerung {
    public static void main(String[] args) {

        /*      DEFAULT VALUES of Primitive Data Types
                Data Type	            Default Value (for fields)
                byte	                0
                short	                0
                int	                    0
                long	                0L
                float	                0.0f
                double	                0.0d
                char	                '\u0000'
                String (or any object)  null
                boolean	                false
         */

        int [] zahlen = new int [5];

        zahlen[0] = 12;

        zahlen[1] = 7 ;

        zahlen[2] = 3 ;

        zahlen[3] = 25 ;

        zahlen[4] = 15 ;

        System.out.println("Das 0. Index beinhaltet : " + zahlen[0]); // 12
        System.out.println("Das 4. Index beinhaltet : " + zahlen[4]);
        System.out.println("Das 1. Index beinhaltet : " + zahlen[1]);
        System.out.println("Das 2. Index beinhaltet : " + zahlen[2]);
        System.out.println("Das 3. Index beinhaltet : " + zahlen[3]);  // zahlen[5] = arrayIndexOutOfBoundsException: 5

    }
}
