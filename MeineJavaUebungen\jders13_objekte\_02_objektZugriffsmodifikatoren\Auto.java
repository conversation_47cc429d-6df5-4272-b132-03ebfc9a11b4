package jders13_objekte._02_objektZugriffsmodifikatoren;

public class Auto {

    /* Variablen Elemente mit (public) von überall im Programmcode zu erreichen von eigener Klasse, anderen Klassen und Paketen.
     * Variablen Elemente mit (private) sind im Programmcode nur innerhalb der Klasse zugänglich, in der sie deklariert wurden zu erreichen.
     * Beim <PERSON>ellen eines Objekts kann mann den Constructor auswählen welchen man benutzen möchte
     * oder die IDE nutzt automatisch den Constructor der in jeweiligen Klasse gegeben ist.
     */

    protected String marke;
    protected String model;
    protected int jahr;
    protected String farbe;

    /*
    * Der Zugriffsmodifikator gilt auch für ein constructor
    *
    * Private gekennzeichnete Konstruktoren dienen in der Regel dazu,
    * die Instanziierung einer Klasse von außerhalb der Klasse selbst zu verhindern.
    * Sie werden verwendet, um die Kontrolle über die Erstellung von Objekten einer Klasse zu behalten
    * und sicherzustellen, dass bestimmte Regeln oder Bedingungen erfüllt sind, bevor ein Objekt erstellt werden kann.
    *
    * Hier sind einige mögliche Gründe und Anwendungsfälle für private Konstruktoren:
    *
    * Singleton-Entwurfsmuster: Wenn eine Klasse als Singleton implementiert wird,
    * d.h. es soll nur eine einzige Instanz dieser Klasse existieren, kann der Konstruktor als privat
    * markiert werden, um zu verhindern, dass externe Klassen neue Instanzen erstellen.
    * Stattdessen kann die Klasse eine statische Methode haben, die die einzige Instanz zurückgibt.
    *
    * Factory-Methode: Manchmal wird ein privater Konstruktor verwendet, um eine Factory-Methode zu unterstützen.
    * Die Factory-Methode ist eine Methode in einer Klasse, die die Erstellung von Objekten dieser Klasse steuert.
    * Der private Konstruktor stellt sicher, dass die Factory-Methode verwendet werden muss, um ein neues Objekt zu erstellen.
    *
    * Utility-Klasse: In einigen Fällen werden Klassen erstellt, die nur statische Methoden enthalten und keine Instanz benötigen.
    * Solche Klassen werden manchmal als Utility-Klassen bezeichnet. Da sie keine Instanzen erfordern,
    * kann der Konstruktor als privat markiert werden, um zu verhindern, dass versehentlich Instanzen dieser Klasse erstellt werden.
    *
    * Insgesamt bieten private Konstruktoren eine Möglichkeit, die Kontrolle über die Instanziierung
    * einer Klasse zu haben und sicherzustellen, dass bestimmte Entwurfsmuster oder Anforderungen erfüllt sind.
    * Sie ermöglichen eine feinere Steuerung darüber, wie und wann Objekte erstellt werden können.
    */

    // Wenn kein constructor definiert ist in einer Klasse
    public Auto() {
        
    }

    // Objekt mit zwei Parametern erstellen, restlichen werte sind für int 0 und für String null
    public Auto(String mrk, String mdl){

        marke = mrk;
        model = mdl;
    }

    /*
    public Auto(){
        marke = "Mercedes";
        model = "E 390";
        farbe = "Schwarz";
        jahr = 2002;
    }
    */

    // Objekt mit allen Parametern
    public Auto(String mrk, String mdl, int j, String frb){

        marke = mrk;
        model = mdl;
        jahr = j;
        farbe = frb;
    }


}