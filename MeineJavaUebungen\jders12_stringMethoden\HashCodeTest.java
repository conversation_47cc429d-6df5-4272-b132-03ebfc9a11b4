package jders12_stringMethoden;

public class HashCodeTest {
    public static void main(String[] args) {

       /* String Hashcode Test :
        3254818
        2003320502
        StringBuffer Hashcode Test :
        1808253012
        1808253012*/

        System.out.println("String Hashcode Test : ");

        String str = "java"; // Erstellt ein neues String-Objekt mit dem Wert "java"

        System.out.println(str.hashCode()); // Gibt den Hashcode des String-Objekts aus

        str = str + "immutable programmieren"; // Fügt den String "programmieren" zum vorhandenen String hinzu, hierbei handelt
        // es sich um ein neuen String mit einer neuen uniqueId

        System.out.println(str.hashCode()); // Gibt einen anderen neuen Hashcode vom neuen String-Objekts aus


        System.out.println("StringBuffer Hashcode Test : ");

        StringBuffer sb = new StringBuffer("java"); // Erstellt ein neues StringBuffer-Objekt mit dem Wert "java"

        System.out.println(sb.hashCode()); // Gibt den Hashcode des StringBuffer-Objekts aus

        sb.append("mutable programmieren"); // Fügt den String "programmieren" zum vorhandenen StringBuffer hinzu

        System.out.println(sb.hashCode()); // Gibt selben Hashcode des aktualisierten StringBuffer-Objekts aus
    }
}
