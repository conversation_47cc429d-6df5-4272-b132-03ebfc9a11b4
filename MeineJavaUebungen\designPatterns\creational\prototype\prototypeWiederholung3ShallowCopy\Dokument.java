package designPatterns.creational.prototype.prototypeWiederholung3ShallowCopy;


public class Dokument implements Cloneable {


    private long id;
    private String name;
    private String inhalt;
    // Im Objekt Dokument sind die´weiteren Objekte innerhalb des Dokument-Objektes nicht clone-bar
    private DokumentTyp dokumentTyp;
    private Kategorie kategorie;


    public Dokument() {
    }

    public Dokument(long id, String name, DokumentTyp dokumentTyp, Kategorie kategorie, String inhalt) {
        this.id = id;
        this.name = name;
        this.dokumentTyp = dokumentTyp;
        this.kategorie = kategorie;
        this.inhalt = inhalt;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public DokumentTyp getDokumentTyp() {
        return dokumentTyp;
    }

    public void setDokumentTyp(DokumentTyp dokumentTyp) {
        this.dokumentTyp = dokumentTyp;
    }

    public Kategorie getKategorie() {
        return kategorie;
    }

    public void setKategorie(Kategorie kategorie) {
        this.kategorie = kategorie;
    }

    public String getInhalt() {
        return inhalt;
    }

    public void setInhalt(String inhalt) {
        this.inhalt = inhalt;
    }

    /**
     * Shallow Copy
     *
     * @return
     * @throws CloneNotSupportedException
     */
    @Override
    protected Dokument clone() throws CloneNotSupportedException {
        return (Dokument) super.clone();
    }

    @Override
    public String toString() {
        return "Dokument{" +
                "\nid=" + id +
                "\n, name='" + name + '\'' +
                "\n, dokumentTyp=" + dokumentTyp +
                "\n, kategorie=" + kategorie +
                "\n, inhalt='" + inhalt + '\'' +
                '}';
    }
}