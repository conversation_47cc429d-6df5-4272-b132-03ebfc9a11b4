package jders21_ordnerVerarbeitungen.anwendung2ScannerZeilenAusgabe;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Scanner;

public class DateiLesen {
    public static void main(String[] args) {

        /* Modifizierte Version des Codes: In diesem Code werden die Zeilen zuerst in der ArrayList zeilen gespeichert
           und dann außerhalb der Schleife durchlaufen und einzeln ausgegeben. Dies ermöglicht es den Print-Befehl
           außerhalb der Schleife zu haben, während jede Zeile dennoch einzeln untereinander ausgegeben wird.*/

        File datei = new File("C:/Users/<USER>/Desktop/Projeler/Dokument2.txt");

        try {

            Scanner sc = new Scanner(datei);

            ArrayList<String> zeilen = new ArrayList<>(); // Eine ArrayList, um Zeilen zu speichern

            while (sc.hasNextLine()) { // Überprüfe, ob es eine weitere Zeile gibt
                zeilen.add(sc.nextLine()); // Füge die nächste Zeile zur ArrayList hinzu
            }

            for (String zeile : zeilen) {
                System.out.println(zeile); // Gib jede Zeile einzeln aus
            }

        } catch (FileNotFoundException e) {
            System.err.println("Fehler : " + e);
            ;
        }
    }
}
