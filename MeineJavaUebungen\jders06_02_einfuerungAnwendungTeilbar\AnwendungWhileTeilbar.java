package jders06_02_einfuerungAnwendungTeilbar;

import java.util.Scanner;

public class AnwendungWhileTeilbar {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);

        int anfang;
        int ende;

        System.out.print("Bei welcher Zahl möchten Sie beginnen: ");
        anfang = sc.nextInt();

        System.out.print("Bei welcher Zahl möchten Sie enden: ");
        ende = sc.nextInt();

        int i = anfang;
        while (i <= ende) {
            if ((i % 3 == 0) && (i % 4 == 0)) {
                System.out.print(i + " ");
            }
            i++;
        }
    }
}
