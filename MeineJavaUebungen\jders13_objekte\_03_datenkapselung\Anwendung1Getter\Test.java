package jders13_objekte._03_datenkapselung.Anwendung1Getter;

public class Test {
    public static void main(String[] args) {

        // Erstellung einer Instanz von Student in der Klasse Test
        Student student1 = new Student();

        // Werte werden gesetzt
        student1.setVorname("aa");
        student1.setNachname("");
        student1.setStudentenNummer("123");
        student1.setGeburtsdatum(-18);

        // Werte werden aus der Klasse Student abgerufen und in die Klasse Test zugewiesen
        String name = student1.getName();
        String nachname = student1.getNachname();
        int geburtsdatum = student1.getGeburtsdatum();
        String studentenNummer = student1.getStudentenNummer();


        // Die Werte von student1 werden in der Klasse Test ausgegeben
        System.out.println("Name : " + name);
        System.out.println("Name : " + student1.getName());
        System.out.println("Nachname : " + nachname);
        System.out.println("Geburtsdatum : " + geburtsdatum);
        System.out.println("StudentenNummer : " + studentenNummer);

        System.out.println();

        // Neue Instanz Student2 mit erfüllten Bedingungen
        Student student2 = new Student();

        // Werte werden gesetzt mit Validierungslogik
        student2.setVorname("ali");
        student2.setNachname("can");
        student2.setStudentenNummer("12345611");
        student2.setGeburtsdatum(1999);

        // Die Werte von student2 werden in der Klasse Test abgerufen und ausgegeben
        System.out.println("Name : " + student2.getName());
        System.out.println("Nachname : " + student2.getNachname());
        System.out.println("Geburtsdatum : " + student2.getGeburtsdatum());
        System.out.println("StudentenNummer : " + student2.getStudentenNummer());
        System.out.println();

        // 3. Student3 Instanz wird erstellt mit dem Constructor public Student(String vn, String nn, int gd, String sn)
        // ohne Validierungslogik
        Student student3 = new Student("Ben","Boner",1999,"123456");
        System.out.println("Name : " + student3.getName());
        System.out.println("Nachname : " + student3.getNachname());
        System.out.println("Geburtsdatum : " + student3.getGeburtsdatum());
        System.out.println("StudentenNummer : " + student3.getStudentenNummer());

        /* Die Werte von student2 werden nicht explizit in der Klasse Test zugewiesen,
         * sondern in der Klasse Student gesetzt und dann in der Klasse Test abgerufen und ausgegeben.*/

        /* Es gibt Situationen, in denen es Sinn machen kann, Werte einer Klasse in einer anderen Klasse zuzuweisen.
         * Hier sind einige mögliche Szenarien:
         *
         * Datenvalidierung: Wenn die Klasse, die die Werte enthält, über Methoden zur Datenvalidierung verfügt,
         * kann es sinnvoll sein, die Werte in dieser Klasse zuzuweisen. Dadurch wird sichergestellt,
         * dass die Validierungslogik angewendet wird und die Werte korrekt sind,
         * bevor sie in anderen Klassen verwendet werden.
         *
         * Datenkapselung: In einigen Fällen möchten Sie möglicherweise den direkten
         * Zugriff auf die Eigenschaften einer Klasse beschränken und den Zugriff
         * über Getter- und Setter-Methoden steuern. In solchen Fällen kann es sinnvoll sein,
         * die Werte in der Klasse selbst zuzuweisen, um die Datenkapselung beizubehalten.
         *
         * Verhalten der Klasse: Wenn die Klasse über spezifisches Verhalten oder Methoden verfügt,
         * die auf den zugewiesenen Werten basieren, kann es sinnvoll sein,
         * die Werte in dieser Klasse zuzuweisen. Dadurch wird das Verhalten der Klasse genutzt
         * und die Funktionalität wird an einem zentralen Ort zusammengefasst.
         *
         * Beziehungen zwischen Klassen: Wenn es eine Beziehung oder Abhängigkeit zwischen zwei Klassen gibt
         * und eine Klasse die Werte einer anderen Klasse verwenden muss, kann es sinnvoll sein,
         * die Werte in der Klasse zuzuweisen, die sie verwenden wird. Dadurch werden die Beziehungen
         * zwischen den Klassen deutlicher und die Klassen können besser zusammenarbeiten.
         *
         * Es ist wichtig, den Kontext und die Anforderungen Ihrer spezifischen Anwendung zu berücksichtigen,
         * um zu entscheiden, ob es sinnvoll ist, die Werte in eine andere Klasse zuzuweisen.
         * Es gibt keine allgemeingültige Regel, und die beste Vorgehensweise hängt von den spezifischen
         * Anforderungen und dem Design Ihrer Anwendung ab.
         *
         * Noch mal die Möglichkeiten mit Zuweisung in einer fremden Klasse:
         * 1. Verwendung von Setter-Methoden: Sie können die Werte einer Klasse mithilfe von Setter-Methoden
         * in einer anderen Klasse zuweisen. Dies ermöglicht eine externe Kontrolle über die Werte
         * und ermöglicht es Ihnen, Validierungen oder bestimmte Aktionen auszuführen, bevor die Werte zugewiesen werden.
         * 2. Konstruktor: Sie können die Werte einer Klasse mithilfe des Konstruktors
         * einer anderen Klasse zuweisen. Dies ist besonders nützlich, um eine Instanz einer Klasse
         * mit vordefinierten Werten zu erstellen.
         *
         * Möglichkeiten OHNE Zuweisung in einer fremden Klasse:
         * 1. Direkte Zuweisung: Sie können die Werte einer Klasse direkt in der Klasse selbst festlegen,
         * ohne eine separate Klasse für die Zuweisung zu verwenden. Dies ist sinnvoll,
         * wenn Sie die Werte bereits zur Kompilierungszeit kennen oder
         * wenn Sie keine spezifischen Validierungen oder Operationen durchführen müssen.
         * 2. Verwendung von Initialisierungsblöcken: Sie können Initialisierungsblöcke verwenden,
         * um den Werten einer Klasse bei der Instanziierung einen bestimmten Wert zuzuweisen.
         * Dies ist nützlich, wenn die Werte bereits zur Kompilierungszeit bekannt sind oder
         * wenn Sie komplexe Initialisierungslogik haben, die nicht in einem Konstruktor enthalten sein sollte.
         * */

    }
}
