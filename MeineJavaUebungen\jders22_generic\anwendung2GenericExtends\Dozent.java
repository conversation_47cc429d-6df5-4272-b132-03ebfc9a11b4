package jders22_generic.anwendung2GenericExtends;

public class <PERSON>zent extends Person {

    private String branche;

    public Dozent() {
    }

    public Dozent(String vorname, String nachname, int geburtsjahr) {
        super(vorname, nachname, geburtsjahr);
    }

    public Dozent(String vorname, String nachname, int geburtsjahr, String branche) {
        super(vorname, nachname, geburtsjahr);
        this.branche = branche;
    }

    public String getBranche() {
        return branche;
    }

    public void setBranche(String branche) {
        this.branche = branche;
    }

    @Override
    public String toString() {
        return "Dozent [" + "Branche='" + branche + ", Vorname=" + getVorname() +
        ", Nachname=" + getNachname() + ", Geburtsjahr=" + getGeburtsjahr() + "]";
    }
}
