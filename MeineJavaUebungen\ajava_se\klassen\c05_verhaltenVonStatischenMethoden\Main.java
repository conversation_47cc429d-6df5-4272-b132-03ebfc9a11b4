package ajava_se.klassen.c05_verhaltenVonStatischenMethoden;

/**
 * Hauptklasse, die das Verhalten von statischen Methoden in Vererbungshierarchien demonstriert.
 * Dieses <PERSON> zeigt, dass statische Methoden nicht polymorphisch sind und
 * dass der deklarierte Typ der Referenzvariable bestimmt, welche statische Methode aufgerufen wird.
 */
public class Main {
    /**
     * Hauptmethode, die das Verhalten von statischen Methoden demonstriert.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Wir erstellen ein Objekt vom Typ B, speichern es aber in einer Referenzvariable vom Typ A
        A a = new B();

        // Wenn wir nun printMessage() auf a aufrufen, wird die Methode der Superklasse A ausgeführt,
        // nicht die der Unterklasse B, obwohl das tatsächliche Objekt vom Typ B ist.
        // HINWEIS: Die IDE warnt hier, dass statische Methoden besser direkt über den Klassennamen
        // aufgerufen werden sollten (A.printMessage() statt a.printMessage()).
        a.printMessage();  // Gibt "Hallo von A" aus

        // Zum Vergleich: Direkter Aufruf über die Klassen
        System.out.println("\nDirekter Aufruf über die Klassen:");
        A.printMessage();  // Gibt "Hallo von A" aus
        B.printMessage();  // Gibt "Hallo von B" aus
    }
    /**
     * Erklärung des Verhaltens:
     *
     * 1. Statische Methoden gehören zur Klasse selbst, nicht zu Instanzen der Klasse.
     * 2. In diesem Fall ist die Klasse des Objekts B, aber die Klasse der Referenzvariable ist A,
     *    daher wird die statische Methode von A aufgerufen.
     * 3. Statische Methoden können nicht überschrieben, sondern nur versteckt werden (method hiding).
     * 4. Der Aufruf einer statischen Methode wird zur Kompilierzeit entschieden und basiert auf dem
     *    deklarierten Typ der Referenzvariable, nicht auf dem tatsächlichen Objekttyp.
     * 5. Im Gegensatz dazu werden nicht-statische (virtuelle) Methoden zur Laufzeit aufgerufen und
     *    basieren auf dem tatsächlichen Objekttyp, was Polymorphismus ermöglicht.
     *
     * Beste Praxis: Statische Methoden sollten immer über den Klassennamen aufgerufen werden,
     * nicht über eine Instanz (z.B. A.printMessage() statt a.printMessage()).
     */
}
