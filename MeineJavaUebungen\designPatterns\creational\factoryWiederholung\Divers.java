package designPatterns.creational.factoryWiederholung;

public class Divers implements Person{

    private String getGeschlecht;

    private String getVorname;

    private String getNachname;

    private int getGeburtsjahr;

    public Divers(String getGeschlecht, String getVorname, String getNachname, int getGeburtsjahr) {
        this.getGeschlecht = getGeschlecht;
        this.getVorname = getVorname;
        this.getNachname = getNachname;
        this.getGeburtsjahr = getGeburtsjahr;
    }

    @Override
    public String getGeschlecht() {
        return null;
    }

    @Override
    public String getVorname() {
        return null;
    }

    @Override
    public String getNachname() {
        return null;
    }

    @Override
    public int getGeburtsjahr() {
        return 0;
    }

    @Override
    public String toString() {
        return "Divers{" +
                "getGeschlecht='" + getGeschlecht + '\'' +
                ", getVorname='" + getVorname + '\'' +
                ", getNachname='" + getNachname + '\'' +
                ", getGeburtsjahr=" + getGeburtsjahr +
                '}';
    }
}
