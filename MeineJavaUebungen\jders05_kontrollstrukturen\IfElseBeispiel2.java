package jders05_kontrollstrukturen;

public class IfElseBeispiel2 {
    public static void main(String[] args) {
        int a=5, b=6, c=7;

        if(a>b) // Wenn a größer als b ist
        {
            a=b; // setze den Wert von a auf den Wert von b
        }
        else // Andernfalls, wenn a nicht größer als b ist
        {
            if(a<c) // Wenn a kleiner als c ist
            {
                a=c; // setze den Wert von a auf den Wert von c
            }
        }
        System.out.println("" + a + b + c);  // Gib die Konkatenation der Werte von a, b und c aus (767)
    }
}
