package ders08_einfache_if_Saetze;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert die Verwendung einer einfachen if-Anweisung in Java.
 *
 * Die if-Anweisung ist eine bedingte Anweisung, die einen Codeblock nur dann ausführt,
 * wenn eine bestimmte Bedingung erfüllt ist (d.h. wenn die Bedingung als true ausgewertet wird).
 *
 * In diesem Beispiel wird überprüft, ob eine vom Benutzer eingegebene Zahl durch 5 teilbar ist.
 */
public class C01_ifStatements {
    /**
     * Die Hauptmethode fragt den Benutzer nach einer Zahl und überprüft,
     * ob diese durch 5 teilbar ist.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Wir wollen eine Zahl vom Benutzer einlesen und überprüfen,
        // ob sie durch 5 teilbar ist. Falls ja, geben wir eine entsprechende Meldung aus.

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scan = new Scanner(System.in);

        // Benutzer zur Eingabe auffordern
        System.out.println("Bitte geben Sie eine positive ganze Zahl ein:");

        // Eingabe einlesen
        int eingabeZahl = scan.nextInt();

        // Überprüfen, ob die Zahl durch 5 teilbar ist
        // Der Modulo-Operator (%) berechnet den Rest einer Division
        // Wenn der Rest der Division durch 5 gleich 0 ist, ist die Zahl durch 5 teilbar
        if (eingabeZahl % 5 == 0) {
            System.out.println("Die eingegebene Zahl " + eingabeZahl + " ist durch 5 teilbar.");
        } else {
            // Optional: Wir könnten auch eine Meldung ausgeben, wenn die Zahl nicht durch 5 teilbar ist
            System.out.println("Die eingegebene Zahl " + eingabeZahl + " ist nicht durch 5 teilbar.");
        }

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scan.close();
    }
}
