package jders09_methoden.ohneParameterVoid;

public class ParameterlosesBeispiel2 {
    public static void main(String[] args) {

        multiplizieren();
        addieren();
        multiplizieren();

    }

    public static void multiplizieren() {

        int a = 5;
        int b = 10;
        int multipliziert = a * b;

        System.out.println("Gesamt : " +multipliziert);

    }

    public static void addieren() {

        int a = 5;
        int b = 10;
        int addiert = a + b;

        System.out.println("Gesamt : " +addiert);

    }

}
