package ders11_ternaryOperator;

import java.util.Scanner;

public class C02_Ternary {
    public static void main(String[] args) {
        // Buchstaben der Nutzereingabe einlesen,
        // wenn Eingabe Buchstabe klein beginnt wird er in Groß gewandelt ausgegeben
        // anders falls, wird in Groß der eingegebene Buchstabe Ausgegeben

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte geben sie einen Buchstaben ein");
        char buchstabe=scan.next().charAt(0);

        System.out.println(buchstabe>='a' && buchstabe<='z' ? buchstabe-32 : buchstabe);        // Ausgabe ascii wert -32 von klein zu Großbuchstaben

        System.out.println(buchstabe>='a' && buchstabe<='z' ? (char) (buchstabe-32) : buchstabe);        // Casting für Ausgabe von Buchstaben aus ascii

    }
}
