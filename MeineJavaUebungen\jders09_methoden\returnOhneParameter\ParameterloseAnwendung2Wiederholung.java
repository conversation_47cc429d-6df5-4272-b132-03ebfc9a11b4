package jders09_methoden.returnOhneParameter;

public class ParameterloseAnwendung2Wiederholung {
    public static void main(String[] args) {

        int x;
        int y;

        boolean vergleicheErgebnis = vergleichen();
        if (vergleicheErgebnis) {
            x = 10;
            y = 100;
            System.out.println("Alles richtig gemacht!");
        } else {
            x = 20;
            y = 200;
            System.out.println("Sicherlich hast Du ein Fehler gemacht!");
        }

        System.out.println(x + " " + y);    // 10 100
        /*
        System.out.print(x);
        System.out.print(" ");
        System.out.print(y);                // 10 100
        */

    }

    public static Boolean vergleichen() {

        int a = 20;
        int b = 15;

        if (a > b) {
            // bei Boolean true oder false vom int a, b Vergleich
            return true;            // Bei einer Rückgabe wird der weitere code nicht beachtet und die Methode beendet
        } else {
            return false;
        }
        /*
        * Die Anmerkung @contract(pure=true) gibt an, dass die Methode keine Seiteneffekte hat
        * und ihre Ausgabe nur auf ihren Eingaben basiert. Die Anmerkung @notnull zeigt an,
        * dass die Methode keinen Nullwert zurückgibt.
        * Dies bedeutet, dass die Methode als "rein" oder "unverändert" markiert wurde und dass
        * sie keine Auswirkungen auf andere Teile des Codes hat und nur von ihren Eingabeparametern abhängt.
        * Die Methode gibt wahrscheinlich einen Wert zurück, der niemals null sein wird,
        * daher ist sie auch mit @notnull gekennzeichnet.
        * Diese Anmerkungen können als Hinweise für den Entwickler dienen, um sicherzustellen,
        * dass der Code sauber und fehlerfrei ist, und um sicherzustellen, dass die
        * Methode ihren beabsichtigten Zweck erfüllt.
        * */

    }
}