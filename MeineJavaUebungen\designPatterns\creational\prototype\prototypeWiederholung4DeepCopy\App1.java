package designPatterns.creational.prototype.prototypeWiederholung4DeepCopy;

import java.util.Date;

public class App1 {
    public static void main(String[] args) {

        /*Der Code implementiert das Prototypenmuster, um Dokumente zu erstellen.
        Das Muster verwendet einen Prototyp, der eine Vorlage für ein Dokument ist.
        Um ein neues Dokument zu erstellen, wird der Prototyp kopiert und die gewünschten
        Änderungen vorgenommen die Klasse AllgemeinEntitaetsService stellt einen Service zur Verfügung, der ganz schlecht nur so tut,
        als ob Dokumente aus einer Datenbank abrufen werden.*/

        AllgemeinEntitaetsService allgemeinEntitaetsService = new AllgemeinEntitaetsService();

        long id1 = 1L;

        Date startZeit = new Date();
        Dokument dokument1 = allgemeinEntitaetsService.findDokumentById(id1);
        dokumentUndProzessDauerAusgeben(startZeit, dokument1);

        /*
        // anschließend wird ein Weiters Dokument bearbeitet
        Date startZeit2 = new Date();
        // ein zweites Dokument erstellen über die id
        Dokument dokument2 = allgemeinEntitaetsService.findDokumentById(2L);
        dokumentUndProzessDauerAusgeben(startZeit, dokument2);
        */

        // System.out.println(dokument2);
        // System.out.println(prozessDauer2);
        /*Alternative */

        // Die Methode dokument1.clone() ist Protected um dies zu nutzen müssen wir zur Klasse Dokument das Clonable interface implementieren.
        Date startZeit3 = new Date();

        Dokument dokumentClone = null;

        try {
            dokumentClone = dokument1.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }

        dokumentClone.setName("Daily");
        dokumentClone.setDatei("SprintFeature 230258...");
        dokumentClone.getDokumentTyp().setName("Arbeit 2");
        dokumentClone.getKategorie().setName("Beruflich");


        dokumentUndProzessDauerAusgeben(startZeit3, dokumentClone);
        System.out.println(dokument1);
        System.out.println(dokument1.hashCode());

    }

    private static void dokumentUndProzessDauerAusgeben(Date startZeit, Dokument dokument1) {
        Date endZeit = new Date();

        Long prozessDauer = getProzessDauer(startZeit, endZeit);

        System.out.println(dokument1 + " " + dokument1.hashCode());
        System.out.println(prozessDauer); // 4

        System.out.println("\n");
    }

    // Methode zur Berechnung der Dauer zwischen zwei Zeitpunkten in Sekunden. mis = millisekundenInSekunden.
    private static Long getProzessDauer(Date startZeit, Date endZeit) {

        long mis = 1000;

        // Berechnung der Prozessdauer in Sekunden.
        long prozessDauer = (endZeit.getTime() / mis) - (startZeit.getTime() / mis);

        return prozessDauer;
    }

}
