package jders20_tryCatch.anwendung2Exception;

public class Test {
    public static void main(String[] args) {

        Adresse adresse1 = new Adresse("Bahnhofstrasse", "10220", "Berlin");

        Student student1 = new Student("<PERSON><PERSON><PERSON>", "<PERSON>pf", "2391", null);
        Student student2 = null;

        // der Aufruf von student2.getVorname wird nicht durch die Methode behandelt und so landet die Ausnahme nicht im try-catch block der Methode selbst
        // student2.getVorname();

        studentInfoAusgeben(student1);
        studentInfoAusgeben(student2);

        System.out.println("Test-Durchgang");

        // verursacht Ausnahme von Java
        student2.getVorname();

    }

    public static void studentInfoAusgeben(Student student){

        try {

            System.out.println("Name : " + student.getVorname());
            System.out.println("Nachname : " + student.getNachname());
            System.out.println("Studenten Nummer : " + student.getStudentenNummer());
            // nur den toString von Klasse Adresse ausgeben
            // System.out.println("Adresse : " + student.getAdresse());

            Adresse adresse = student.getAdresse();

            System.out.println("Straße : " + adresse.getStrasse());
            System.out.println("Postleitzahl : " + adresse.getPostleitzahl());
            System.out.println("Stadt : " + adresse.getStadt());

        }catch (Exception e){

            System.out.println("Fehler : " + e);  // Fehler : java.lang.NullPointerException
            System.out.println("Fehler : " + e.toString());  // Fehler : java.lang.NullPointerException
            System.out.println("Fehler : " + e.getMessage());  // Fehler : null
        }
    }

    /* Diese art von try-catch Blöcken werden wir z.b. beim Verbinden zur Datenbank, beim Senden von Werten in die Datenbank */

}
