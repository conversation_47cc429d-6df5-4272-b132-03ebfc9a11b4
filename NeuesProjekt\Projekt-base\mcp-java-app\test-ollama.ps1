Write-Host "Teste Ollama API..."
$response = Invoke-RestMethod -Uri "http://localhost:11434/api/version" -Method Get
Write-Host "Ollama Version: $($response.version)"

Write-Host "`nTeste Ollama Chat API..."
$body = @{
    model = "tinyllama:latest"
    messages = @(
        @{
            role = "user"
            content = "Erzähle mir einen kurzen Witz"
        }
    )
} | ConvertTo-Json

$response = Invoke-RestMethod -Uri "http://localhost:11434/api/chat" -Method Post -Body $body -ContentType "application/json"
Write-Host "Antwort: $($response.message.content)"

Read-Host "Drücken Sie eine Taste, um fortzufahren..."
