package designPatterns.creational.prototype.solution.prototypeWiederholung2;

import java.util.Date;

public class AppAnwendung {
    public static void main(String[] args) {

        // Beim <PERSON>ellen der Dokumente verlieren wir 4 Sekunden bis das nächste Dokumennt erstellt werden kann.
        // Alternative um dies zu umgehen bietet uns das Prototype-Muster eine Verwendung, um Dokumente effizienter zu klonen.

        PublicEntityService publicEntityService = new PublicEntityService();

        long id1 = 1L;

        Date startZeit = new Date();
        Dokument dokument1 = publicEntityService.findDokumentById(id1);
        dokumentUndProzessDauerAusgeben(startZeit, dokument1);


        Date startZeit1 = new Date();
        Dokument dokument2 = publicEntityService.findDokumentById(2L);
        dokumentUndProzessDauerAusgeben(startZeit1, dokument2);

        Date startZeit2 = new Date();

        // Hier erstellen wir eine Kopie des ersten Dokuments mithilfe der `clone()`-Methode.
        Dokument dokumentClone3 = null;
        try {
          dokumentClone3 = dokument1.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }

        // Wir ändern den Namen und den Inhalt der geklonten Dokumentkopie.
        dokumentClone3.setName("Daily");
        dokumentClone3.setInhalt("Daily sprint 5400");



        dokumentUndProzessDauerAusgeben(startZeit2, dokumentClone3);


    }

    private static void dokumentUndProzessDauerAusgeben(Date startZeit, Dokument dokument1) {
        Date endZeit = new Date();

        Long prozessDauer = getProzessDauer(startZeit, endZeit);

        System.out.println(dokument1);
        System.out.println(prozessDauer);
        System.out.println(dokument1.hashCode());

        System.out.println("\n");
    }

    private static Long getProzessDauer(Date startZeit, Date endZeit) {

        long milliseconds = 1000;

        // Berechnung der Prozessdauer in Sekunden.
        long prozessDauer = (endZeit.getTime() / milliseconds) - (startZeit.getTime() / milliseconds);

        return prozessDauer;
    }
}
