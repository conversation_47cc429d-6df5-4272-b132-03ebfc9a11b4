package ders14_scope;

public class C01_Scope {

    // Scope beschreibt den Bereich der Erreichbarkeit von einer Variable

    // 1. Klass Level Variable, werden außerhalb von Methoden und Codeblöcken erstellt
    // static Mitglied kann von überall aus erreicht werden
    static int classLevelStatic = 12; // Static könnten wir für die Variablen NUR im Klass Level setzen, und in keinen Methoden oder Funktionen

    // Instanz Variable, von überall innerhalb der Klasse erreichbar,
    // außer in static Methoden nicht direkt, sondern über das Objekt der Klasse zu erreichen
    String strClassLevelOhneStatic = "Java ist schön"; // Im Klass Level können wir auch Variable definieren, so wie überall

    public static void main(String[] args) {
        // Locale Variablen müssen nicht deklariert werden, außer wenn wir sie nicht gleich ohne eine Zuweisung benutzen wollen!
        int zahlMain = 20;              // 2. Local Variable,
        System.out.println(classLevelStatic);

        // Loop Variable können nur inner halb des Loops genutzt werden
        for (int i = 0; i < 10; i++) {
            int zahlForLoop = 5; // Locale Local Variable innerhalb der Loop, Loop Variable
        }
    }

    public static void staticMethod() {
        // 2. Local Variable
        String strStaticMethod = "Java ist Leben";

    }

    // 2. Local Variable
    public void blOhneStaticMethode() {

        boolean blOhneStaticMethode = true;

    }

}
