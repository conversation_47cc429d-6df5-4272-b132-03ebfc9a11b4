package jders04_operatoren;

public class RelationaleOperatoren {
    public static void main(String[] args) {
        // == , != , < , > , <= , >=
        // output : true , false

        int zahl1 = 15;
        int zahl2 = 25;

        boolean zustand = zahl1 == zahl2;

        System.out.println("Zustand : " + zustand); // false

        System.out.println("Zustand : " + (zahl1<zahl2));  // true

        System.out.println("Multiplikation : " + (zahl1 * zahl2));
    }
}
