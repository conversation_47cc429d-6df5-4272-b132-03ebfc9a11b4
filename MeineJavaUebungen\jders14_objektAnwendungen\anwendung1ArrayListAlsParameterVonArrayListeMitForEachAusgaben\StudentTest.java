package jders14_objektAnwendungen.anwendung1ArrayListAlsParameterVonArrayListeMitForEachAusgaben;

import java.util.ArrayList;

public class StudentTest {
    public static void main(String[] args) {
        /*Wir Weißen in dem Programm Listen einer Liste zu, die einer Instanz
         * hinzugefügt wird, die ebenfalls Bestand einer ArrayList Namens studentenListe,
         * in der alle Studentenobjekte gespeichert werden.
         * Anschließend wird eine foreach-Schlei<PERSON> verwendet, um über die studentenListe
         * zu iterieren und jeden Studenten mit System.out.println(student) auszugeben.
         * Dabei wird die toString()-Methode des Studentenobjekts aufgerufen, um die Informationen
         * des Studenten formatiert auszugeben. */


        // Hier erstellen wir unsre Leeren ArrayLists
        ArrayList<String> unterrichtsFaecher1 = new ArrayList<>();

        ArrayList<String> unterrichtsFaecher2 = new ArrayList<>();

        // Hier erstellen wir unsre vordefinierten Unterrichts Fächer
        unterrichtsFaecher1.add("Mathematik");
        unterrichtsFaecher1.add("Physik");
        unterrichtsFaecher1.add("Chemie");
        unterrichtsFaecher1.add("Biologie");

        // Hier erstellen wir unsre vordefinierten 2. Unterrichts Fächer
        unterrichtsFaecher2.add("Englisch");
        unterrichtsFaecher2.add("Französisch");
        unterrichtsFaecher2.add("Deutsch");
        unterrichtsFaecher2.add("Spanisch");

        // Student student1 = new Student("Jörn", "Decker",1995,"2333",null);
        Student student1 = new Student("Jörn", "Decker",1995,"2333",unterrichtsFaecher1);

        // Wir können auch auf diese weise eine Instanz erstellen
        Student student2 = new Student();
        // Wir können auch auf diese weise einer Instanz Werte zuweisen
        student2.setName("Ron");
        student2.setNachname("Naldo");
        student2.setGeburtsjahr(1996);
        student2.setSchuhlNummer("2342");
        // student2.setUnterrichsFach(unterrichtsFaecher1); // Student2 erhaltet alle Fächer
        // Student2 bekommt die vordefinierten 2. Unterrichts Fächer
        student2.setUnterrichtsFach(unterrichtsFaecher2);

        // Neue Instanz Student3 erhaltet mit unter, liste unterrichtsFaecher1
        Student student3 = new Student("Ruyu", "Yaki", 1993, "2341", unterrichtsFaecher1);

        // Mit unserer toString Methode erhalten wir auch die gewünschte Ausgabe anstatt im @Id Format
     /* System.out.println(student3);
        System.out.println(student2);
        System.out.println(student1);
*/

        // ArrayList zum Speichern aller Studenten
        ArrayList<Student> studentenListe = new ArrayList<>();
        studentenListe.add(student1);
        studentenListe.add(student2);
        studentenListe.add(student3);

        // Ausgabe aller Studenten mit ihren Informationen
        for (Student student : studentenListe) {
            System.out.println(student);
        }


    }
}
