package jders20_tryCatch.anwendung2Exception;

public class Student {

    private String vorname;

    private String nachname;

    private String studentenNummer;

    private Adresse adresse;

    public Student() {
    }

    public Student(String vorname, String nachname, String studentenNummer, Adresse adresse) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.studentenNummer = studentenNummer;
        this.adresse = adresse;
    }

    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    public Adresse getAdresse() {
        return adresse;
    }

    public void setAdresse(Adresse adresse) {
        this.adresse = adresse;
    }

    @Override
    public String toString() {
        return "Student{" +
                "vorname='" + vorname + '\'' +
                ", nachname='" + nachname + '\'' +
                ", studentenNummer='" + studentenNummer + '\'' +
                ", adresse=" + adresse +
                '}';
    }
}
