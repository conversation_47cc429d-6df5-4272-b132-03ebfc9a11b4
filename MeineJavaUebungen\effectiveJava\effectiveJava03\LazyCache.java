package effectiveJava.effectiveJava03;

import java.util.HashMap;

public class Lazy<PERSON>ache implements Cache {

    // Eine statische Variable, um die einzige Instanz dieser Klasse zu speichern.
    private static LazyCache instance;

    // Eine HashMap, um die Schlüssel-Wert-Paare zu speichern.
    private HashMap<Object, Object> map;

    // Ein privater Konstruktor, um die Instanziierung der Klasse von außen zu verhindern.
    private LazyCache() {
        map = new HashMap<Object, Object>();
    }

    // Die put-Methode implementiert das Hinzufügen eines Schlüssel-Wert-Paares zur Map.
    @Override
    public void put(Object key, Object value) {
        map = new HashMap<Object, Object>();
    }

    // Die get-Methode implementiert das Abrufen eines Wertes anhand eines Schlüssels aus der Map.
    @Override
    public Object get(Object key) {
        return map.get(key);
    }

    // Die getInstance-Methode liefert die einzige Instanz dieser Klasse. Wenn noch keine Instanz existiert, wird eine erstellt.
    public static LazyCache getInstance() {
        if (instance == null) {
            instance = new LazyCache();
        }
        return instance;
    }

}
