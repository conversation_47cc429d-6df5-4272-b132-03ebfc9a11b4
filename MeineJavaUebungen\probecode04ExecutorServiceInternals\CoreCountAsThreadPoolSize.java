package probecode04ExecutorServiceInternals;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class CoreCountAsThreadPoolSize {
    public static void main(String[] args) {

        int cores = Runtime.getRuntime().availableProcessors();

        // Erstellen eines ExecutorService mit Anzahl der Kerne, aber können sie auf bedarf all unserer use cases anpassen.
        /* Hier sind einige gängige Arten von Pool<PERSON>rößen (Thread-Pool-Größen) und eine kurze Erläuterung für jeden Typ

         - FixedThreadPool (Fester Thread-Pool): Fest Anzahl in einer LinkedBlockingQueue.
           Bei dieser Art von Thread-Pool wird die Größe des Pools zu Beginn festgelegt und ändert sich während der Laufzeit nicht.
           Es werden immer genau n Threads im Pool gehalten, wobei n normalerweise die Anzahl der verfügbaren Prozessorkerne (CPU-Kerne) ist.
           <PERSON>s ist sinnvoll, wenn Sie eine feste Anzahl von Aufgaben in parallelen Threads ausführen möchten.

         - CachedThreadPool (Dynamischer Thread-Pool): SynchronousQueue
           Bei einem dynamischen Thread-Pool können Threads je nach Bedarf erstellt und wiederverwendet werden.
           Wenn eine neue Aufgabe eingereicht wird und keine freien Threads vorhanden sind, erstellt der Pool einen neuen Thread.
           Wenn ein Thread für eine Weile nicht verwendet wurde, wird er beendet, um Ressourcen freizugeben.
           Dies ist nützlich für Aufgaben, die kurzlebig sind und unterschiedliche Lasten haben.
           Bsp.: Einzusetzen bei Anwendungen die eine Peek Time besitzen

         - SingleThreadExecutor (Einzel thread-Pool):
           Dies ist ein Pool mit nur einem einzigen Thread.
           Alle Aufgaben werden sequenziell in diesem Thread ausgeführt, unabhängig davon, wie viele Aufgaben Sie einreichen.
           Dies kann nützlich sein, wenn Sie sicherstellen möchten, dass Aufgaben in der Reihenfolge ihrer Einreichung ausgeführt werden.

          -ScheduledThreadPool (Geplanter Thread-Pool): Führt Aufgaben nach einer bestimmten Verzögerung aus,
           wenn es darum geht, Aufgaben zeitgesteuert oder mit Verzögerungen auszuführen.
           Ein geplanter Thread-Pool eignet sich gut für Aufgaben, die zu bestimmten Zeitpunkten oder in regelmäßigen Abständen ausgeführt werden sollen.
           Sie können Aufgaben mit Verzögerung ausführen oder periodisch wiederholen lassen.
           Dies ist nützlich für geplante Aufgaben, wie das Periodische Ausführen von Wartungsaufgaben oder das Aktualisieren von Daten.

         - WorkStealingPool (Arbeitsdieb-Thread-Pool):
           In einem Arbeitsdieb-Thread-Pool haben alle Threads die gleiche Priorität und stehlen Aufgaben voneinander,
           wenn sie keine eigenen Aufgaben mehr haben. Dies ermöglicht eine effiziente
           Nutzung der verfügbaren Threads und eignet sich gut für Anwendungen mit vielen kleinen Aufgaben.

         - Custom Thread-Pools (Benutzerdefinierte Thread-Pools):
           Je nach den Anforderungen Ihrer Anwendung können Sie auch benutzerdefinierte Thread-Pool-Größen und -Konfigurationen erstellen.
           Dies ist nützlich, wenn keine der vordefinierten Poolgrößen Ihren Anforderungen entspricht.

           Die Auswahl des richtigen Thread-Pool-Typs hängt von den Anforderungen Ihrer Anwendung ab.
           Es ist wichtig zu berücksichtigen, wie lange Aufgaben dauern, wie viele gleichzeitige Aufgaben Sie haben
           und wie die Ressourcen (CPU, Speicher) auf Ihrem System verteilt sind. Durch das sorgfältige Abwägen dieser Faktoren
           können Sie die Leistung Ihrer Anwendung optimieren.





*/
        // Bei FixedThreadPool wird die Größe des Pools zu Beginn festgelegt
        ExecutorService executorService = Executors.newFixedThreadPool(cores);

        // Eine Schleife, die 1000 Mal ausgeführt wird, um 1000 Runnable-Objekte zu erstellen und sie im ExecutorService auszuführen.
        for (int i = 0; i < 1000; i++) {
            // Mit executorService.submit() wird ein Runnable-Objekt zur Ausführung in den Thread-Pool übergeben.{
            executorService.submit(new Runnable() {

                @Override
                public void run() {
                    // Dieser Code wird in einem der Threads im Pool ausgeführt.
                    // Er gibt "Hello from" gefolgt vom Namen des ausführenden Threads aus.
                    System.out.println("Hello from " + Thread.currentThread().getName());
                }
            });
        }

    }
}
