package jders09_methoden.mitParameterVoid;

public class ParameterAnwendung1 {
    public static void main(String[] args) {
        sendMassage("<PERSON><PERSON>", "<PERSON>lma<PERSON>", 19);

        sendMassage("<PERSON><PERSON>", "<PERSON><PERSON>", 21);

        sendMassage("", "A", 100);

        charakter('a');
        charakter('+');

    }

    public static void sendMassage(String name, String nachname, int alter) {

        System.out.println("Hallo");
        System.out.println("Ihr Name : " + name);
        System.out.println("Ihr Nachname : " + nachname);
        System.out.println("Ihr Alter : " + alter);
        System.out.println();

    }

    public static void charakter(char c) {

        System.out.println("Eingegebener Charakter : " + c);

    }

}
