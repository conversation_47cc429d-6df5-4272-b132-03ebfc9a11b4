package effectiveJava.effectiveJava01.item1Wiederholung;

public class Product {

    private String name;

    private double price;

    // Private Konstruktor, um direkte Instanziierung zu verhindern
    private Product(String name, double price) {
        this.name = name;
        this.price = price;
    }

    // Factory-Methode, um ein neues Produkt zu erstellen
    public static Product createProduct(String name, double price) {

        // Überprüfe, ob der Produktname nicht null ist, nicht leer ist
        // (d.h., er enthält mindestens ein Zeichen) und der Preis größer als 0 ist.
        if (name != null && !name.isEmpty() && price > 0) {
            return new Product(name, price);
        } else {

            // Wenn alle Bedingungen erfüllt sind, erstelle ein neues Produkt-Objekt
            // mit den angegebenen Namen und Preiswerten und gib es zurück.
            throw new IllegalArgumentException("Ungültige Parameter für die Produkt-Erstellung");
        }
    }

    // Getters und Setters für name und price
    /* Getter und Setter sind insbesondere dann sinnvoll, wenn bestimmte Bedingungen erfüllt sind oder
    spezifische Anforderungen an den Zugriff auf die Felder einer Klasse gestellt werden.
    Hier sind einige Fälle, in denen die Verwendung von Getter und Setter empfohlen wird:

    -Validierung und Datenintegrität: Wenn Sie sicherstellen müssen, dass die eingegebenen Daten gültig sind,
    bevor sie einem Feld zugewiesen werden, sollten Sie Setter verwenden.
    Beispielsweise können Sie sicherstellen, dass der Preis eines Produkts immer positiv ist.

    -Verdecken der Implementierung: Wenn die interne Implementierung eines Feldes geändert werden muss,
    aber der Zugriff auf das Feld von außen unverändert bleiben soll,
    verwenden Sie Getter und Setter, um die Änderungen zu isolieren.

    -Zugriffskontrolle: Wenn Sie steuern möchten, wer auf die Felder zugreifen kann, indem
    Sie den Zugriff auf lesend oder schreibend beschränken, verwenden Sie Getter und Setter.
    Zum Beispiel können Sie sicherstellen, dass ein bestimmtes Feld nur lesbar ist.

    -Zusätzliche Logik: Wenn Sie in Zukunft zusätzliche Logik oder Verarbeitung in die Datenzuweisung
    oder -Rückgabe integrieren müssen, können Sie dies in den Getter- und Setter-Methoden tun.

    -Frameworks und Schnittstellen: Wenn Sie Frameworks oder Schnittstellen verwenden, die den Zugriff auf
    Felder über Methoden erfordern (z. B. JavaBeans-Konventionen), sollten Sie Getter und Setter verwenden.

    -Datenkapselung: Wenn Sie die Datenkapselung wahren und direkten Zugriff auf Felder verhindern möchten, verwenden Sie Getter und Setter.

    Ganz elegant sind für uns noch die Punkte:
    -Zugriffskontrolle: Durch Verwendung von Gettern und Settern können Sie den Zugriff auf die Felder kontrollieren.
    Sie können festlegen, ob der Zugriff nur lesend oder schreibend sein soll, je nach den Anforderungen Ihrer Anwendung.

    -Flexibilität: Die Verwendung von Getter und Setter ermöglicht es Ihnen, in der Zukunft zusätzliche Logik
    oder Überprüfungen zu den Feldern hinzuzufügen, ohne den restlichen Code ändern zu müssen, der auf diese Felder zugreift.
    Dies ermöglicht eine bessere Wartbarkeit und Erweiterbarkeit Ihrer Klassen.

    -Refaktorisierung: Wenn Sie feststellen, dass die interne Implementierung eines Feldes geändert werden muss,
    können Sie dies in den Getter- und Setter-Methoden tun, ohne dass die Benutzer des Codes davon betroffen sind.
    Dies hilft, die Auswirkungen von Änderungen auf den bestehenden Code zu minimieren und erhöht die Robustheit Ihrer Anwendung.

    Es ist jedoch wichtig zu beachten, dass in einfachen Datenklassen, die nur dazu dienen, Daten zu speichern
    und keine zusätzliche Logik erfordern, Getter und Setter möglicherweise überflüssig sind.
    In solchen Fällen können die Felder direkt zugänglich sein, da sie keine spezielle Verarbeitung erfordern.
    Die Verwendung von Getter und Setter hängt also von den spezifischen Anforderungen und Zielen Ihrer Anwendung ab.

   In vielen Fällen sind Getter und Setter jedoch nicht zwingend erforderlich, insbesondere wenn wir nur einfache Datenklassen haben,
   die nur Daten speichern. Es hängt von den Anforderungen und der Komplexität unserer Anwendung ab, ob wir Getter und Setter verwenden möchten,
   da wir bereits auch eine toString-Methode haben, um die Details des Produkts anzuzeigen,
   können wir diese Werte direkt über die toString-Methode abrufen.*/

    @Override
    public String toString() {
        return "Product{" +
                "name='" + name + '\'' +
                ", price=" + price +
                '}';
    }

}
