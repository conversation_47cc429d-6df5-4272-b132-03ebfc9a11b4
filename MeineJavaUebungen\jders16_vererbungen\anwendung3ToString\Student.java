package jders16_vererbungen.anwendung3ToString;

import java.util.ArrayList;

public class Student extends Person {

    private String studentenNummer;

    public ArrayList<String> unterrichtsFaecher;


    public Student() {

    }

    // Wenn wir keine Werte zur Oberklasse senden wollen, müssen wir nicht, können auch einzeln später mit Set-Methoden gesetzt werden
    public Student(String studentenNummer, ArrayList<String> unterrichtsFaecher) {
        // oder super(ConstructorAuswählen);
        this.studentenNummer = studentenNummer;
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    // Erhält alle parameter von der Oberklasse, an die gesendet wird
    public Student(String vorname, String nachname, int geburtsjahr) {
        super(vorname, nachname, geburtsjahr);
    }

    public Student(String vorname, String nachname, int geburtsjahr, String studentenNummer, ArrayList<String> unterrichtsFaecher) {
        // Als Erstes müssen wir in die Klasse aus der wir erben, die Attribute mit super laden
        // super, weil wir private vorname, nachname, geburtsjahr selbst in der Unterklasse nicht erreichen
        super(vorname, nachname, geburtsjahr);
        // Anders könnten wir ohne super auch das nutzen.
        // setVorname(vorname);
        // setNachname(nachname);
        // setGeburtsjahr(geburtsjahr);
        this.studentenNummer = studentenNummer;
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    public String getStudentenNummer() {
        return studentenNummer;
    }

    public void setStudentenNummer(String studentenNummer) {
        this.studentenNummer = studentenNummer;
    }

    public ArrayList<String> getUnterrichtsFaecher() {
        return unterrichtsFaecher;
    }

    public void setUnterrichtsFaecher(ArrayList<String> unterrichtsFaecher) {
        this.unterrichtsFaecher = unterrichtsFaecher;
    }

    /*
    @Override
    public String toString() {

        return "Name : " + getVorname() + ", Nachname : " + getNachname() + ", Geburtsjahr : " + getGeburtsjahr()
                + ", Studenten Nummer : " + studentenNummer + ", Unterrichts Fächer : " + unterrichtsFeacher;

    }
    */
    /*
    @Override
    // Student{studentenNummer='2380', unterrichtsFeacher=[Mathematik, Physik,
    // Chemie]} Person{vorname='Sam', nachname='Wolle', geburtsjahr=1998}
    public String toString() {
        return "Student{" +
                "studentenNummer='" + studentenNummer + '\'' +
                ", unterrichtsFeacher=" + unterrichtsFeacher +
                "} " + super.toString();
    }
    */

    @Override
    public String toString() {
        // Nur andere Reihenfolge
        return "Student [Studenten Nummer : " + studentenNummer + ", Unterrichts Fächer " + unterrichtsFaecher +
                ", Name : " + getVorname() + ", Nachname : " + getNachname() + ", Geburtsjahr : " + getGeburtsjahr() + "]";
    }

}
