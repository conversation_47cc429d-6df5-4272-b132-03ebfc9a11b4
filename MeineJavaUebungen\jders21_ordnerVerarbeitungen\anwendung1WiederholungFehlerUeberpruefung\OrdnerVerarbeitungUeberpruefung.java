package jders21_ordnerVerarbeitungen.anwendung1WiederholungFehlerUeberpruefung;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;

public class OrdnerVerarbeitungUeberpruefung {
    public static void main(String[] args) {

        /* Im gegebenen Code wird die Datei im aktuellen Arbeitsverzeichnis des Java-Programms geschrieben.
        Das aktuelle Arbeitsverzeichnis ist das Verzeichnis, von dem aus das Java-Programm gestartet wird.
        Der genaue Pfad hängt also davon ab, von welchem Verzeichnis aus das Programm ausgeführt wird.*/
        writeToFile("Hello, world!", "output.txt");
    }

    public static void writeToFile(String data, String fileName) {
        PrintWriter writer = null; // Deklariere den PrintWriter außerhalb des Try-Blocks
        try {
            writer = new PrintWriter(new File(fileName)); // Initialisiere den PrintWriter
            writer.println(data); // Schreibe die Daten in die Datei
            writer.flush(); // Optional, um sicherzustellen, dass die Daten geschrieben werden
            if (writer.checkError()) { // Überprüfe auf Fehlerstatus des Writers
                System.out.println("Fehler beim Schreiben der Datei.");
            } else {
                System.out.println("Daten erfolgreich in die Datei geschrieben.");
            }
        } catch (IOException e) { // Behandlung von IOException
            System.out.println("Fehler beim Öffnen oder Schließen der Datei.");
        } finally {
            if (writer != null) {
                writer.close(); // Schließe den PrintWriter im Finally-Block
            }
        }
    }
}