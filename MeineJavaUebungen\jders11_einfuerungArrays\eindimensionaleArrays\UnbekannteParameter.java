package jders11_einfuerungArrays.eindimensionaleArrays;

public class UnbekannteParameter {
    public static void main(String[] args) {

        // intArrayAusgeben(5, 15, 25, 35, 1, 3);
        stringArrayAusgeben("<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>");
    }

    public static void stringArrayAusgeben(String... serie) {

        for (String s : serie) {

            System.out.println(s);
        }
    }

    public static void intArrayAusgeben(int... a) {
        /*
            for (int i : a){
            System.out.println(i);
        }*/

        for (int i = 0; i < a.length; i++) {
            System.out.println(a[i]);
        }

    }

}
