package jders09_methoden.returnOhneParameter;

public class ParameterloseAnwendung2 {
    public static void main(String[] args) {
        /*
         * Wenn eine Methode innerhalb der main-Methode aufgerufen wird, muss die Variable
         * nicht in der main-Methode deklariert werden, da sie bereits innerhalb
         * der Methode vergleichen deklariert ist. Die Variable a und b sind
         * in der Methode vergleichen definiert und werden innerhalb dieser Methode verwendet.
         *
         * J<PERSON>, die Methode kann erneut innerhalb der main-Methode aufgerufen werden,
         * solange sie korrekt aufgerufen wird und die erforderlichen Argumente bereitgestellt werden.
         * Das Ergebnis der Methode kann in einer Variablen gespeichert oder direkt verwendet werden.
         * */

        System.out.println("Zustandsvergleich : " + vergleichen());
        /*
        if (vergleichen()) {  // Das Ergebnis der Methode kann auch direkt verwendet werden
            System.out.println("Richtig gemacht");
        } else {
            System.out.println("Du hast einen Fehler gemacht");
        }*/

        boolean vergleicheErgebnis = vergleichen();
        if (vergleicheErgebnis) {
            System.out.println("Alles richtig gemacht!");
        } else {
            System.out.println("Sicherlich hast du ein Fehler gemacht!");
        }
    }


    public static Boolean vergleichen() {

        int a = 20;
        int b = 15;

        if (a > b) {
            // bei Boolean true oder false vom int a, b Vergleich
            return true;            // Bei einer Rückgabe wird der weitere code nicht beachtet und die Methode beendet
        } else {
            return false;
        }

    }


}
