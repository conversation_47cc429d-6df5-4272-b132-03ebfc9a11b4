package com.example.mcpjavaapp.mcp;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Implementierung des Model Context Protocols (MCP) für die Integration mit KI-Modellen.
 * MCP ermöglicht eine standardisierte Kommunikation zwischen der Anwendung und KI-Modellen.
 */
@Component
public class ModelContextProtocol {

    private static final Logger log = LoggerFactory.getLogger(ModelContextProtocol.class);
    private final ChatClient chatClient;

    @Autowired
    public ModelContextProtocol(ChatClient chatClient) {
        this.chatClient = chatClient;
    }

    /**
     * Sendet eine Anfrage an das Modell mit Kontextinformationen.
     *
     * @param prompt Der Prompt-Text für das Modell
     * @param context Ein Map mit Kontextinformationen
     * @return Die Antwort des Modells
     */
    public String sendRequestWithContext(String prompt, Map<String, Object> context) {
        log.info("Sende MCP-Anfrage mit Kontext: {}", context);
        
        // Erstelle einen erweiterten Prompt mit Kontextinformationen
        StringBuilder enhancedPrompt = new StringBuilder();
        enhancedPrompt.append("### Kontext:\n");
        
        for (Map.Entry<String, Object> entry : context.entrySet()) {
            enhancedPrompt.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        
        enhancedPrompt.append("\n### Anfrage:\n").append(prompt);
        
        try {
            String response = chatClient.prompt().user(enhancedPrompt.toString()).call().content();
            log.info("MCP-Antwort erfolgreich erhalten");
            return response;
        } catch (Exception e) {
            log.error("Fehler bei der MCP-Kommunikation: ", e);
            return "Fehler bei der MCP-Kommunikation: " + e.getMessage();
        }
    }

    /**
     * Sendet eine Code-Analyse-Anfrage mit Kontextinformationen.
     *
     * @param code Der zu analysierende Code
     * @param language Die Programmiersprache
     * @param projectType Der Projekttyp (z.B. "Spring Boot", "Android")
     * @return Die Analyse-Ergebnisse
     */
    public String analyzeCodeWithContext(String code, String language, String projectType) {
        Map<String, Object> context = new HashMap<>();
        context.put("language", language);
        context.put("projectType", projectType);
        context.put("task", "code-analysis");
        
        String prompt = "Analysiere den folgenden Code unter Berücksichtigung des Kontexts. " +
                "Gib Verbesserungsvorschläge und identifiziere potenzielle Probleme:\n\n```" + 
                language + "\n" + code + "\n```";
        
        return sendRequestWithContext(prompt, context);
    }

    /**
     * Generiert Dokumentation für Code mit Kontextinformationen.
     *
     * @param code Der zu dokumentierende Code
     * @param language Die Programmiersprache
     * @param documentationStyle Der Dokumentationsstil (z.B. "JavaDoc", "Markdown")
     * @return Die generierte Dokumentation
     */
    public String generateDocumentationWithContext(String code, String language, String documentationStyle) {
        Map<String, Object> context = new HashMap<>();
        context.put("language", language);
        context.put("documentationStyle", documentationStyle);
        context.put("task", "documentation-generation");
        
        String prompt = "Erstelle eine detaillierte Dokumentation für den folgenden Code " +
                "im " + documentationStyle + "-Format:\n\n```" + language + "\n" + code + "\n```";
        
        return sendRequestWithContext(prompt, context);
    }
}
