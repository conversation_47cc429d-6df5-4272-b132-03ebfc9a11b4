package probecode04ExecutorServiceInternals;

import org.junit.jupiter.api.Test;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class StaticExecutorTest {

    /*In diesem Beispiel gibt es zwei Testmethoden:

      testThreadPoolSize: Diese Methode überprüft, ob der ThreadPool die erwartete Größe von 10 Threads hat.

      testThreadExecution: Diese Methode überprüft, ob alle Threads erfolgreich ausgeführt wurden,
      indem sie ein Array von Flags verwendet, um den Ausführungszustand jedes Threads zu verfolgen.

      Um diese Tests auszuführen, müssen wir das JUnit-Framework ins Projekt mit einbeziehen.
      Wir stellen sicher, dass wir die JUnit-Bibliothek in unserer Entwicklungsumgebung konfiguriert haben,
      */

    @Test
    public void testThreadPoolSize() {
        ExecutorService executorService = Executors.newFixedThreadPool(10);

        // Überprüfen Sie, ob der ThreadPool die erwartete Größe von 10 Threads hat.
        assertEquals(10, ((java.util.concurrent.ThreadPoolExecutor) executorService).getMaximumPoolSize());
    }

    @Test
    public void testThreadExecution() {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        final int expectedThreadCount = 1000;
        final boolean[] threadFlags = new boolean[expectedThreadCount];

        for (int i = 0; i < expectedThreadCount; i++) {
            final int index = i;
            executorService.submit(() -> {
                String threadName = Thread.currentThread().getName();
                System.out.println("Hello from " + threadName);

                // Setzen Sie das entsprechende Flag im Array, um anzuzeigen, dass der Thread ausgeführt wurde.
                threadFlags[index] = true;
            });
        }

        // Warten Sie, bis alle Threads beendet sind.
        executorService.shutdown();
        while (!executorService.isTerminated()) {}

        // Überprüfen Sie, ob alle Threads erfolgreich ausgeführt wurden.
        assertTrue(allThreadsExecuted(threadFlags));
    }

    private boolean allThreadsExecuted(boolean[] flags) {
        for (boolean flag : flags) {
            if (!flag) {
                return false;
            }
        }
        return true;
    }
    /* Die Variable flag in diesem Kontext ist eine einzelne boolesche Variable in einem Array von booleschen Werten.
       In den Tests wird ein boolesches Array threadFlags verwendet, um den Ausführungszustand der Threads zu verfolgen.

    Hier ist, wie es funktioniert:

    Ein boolesches Array threadFlags wird erstellt, wobei jedes Element des Arrays einen Thread darstellt.
    Wenn ein Thread erfolgreich ausgeführt wird (d.h., er erreicht den "Hello from..."-Druck
    und beendet seine Aufgabe), wird das entsprechende Flag im Array auf true gesetzt.
    Dies bedeutet, dass dieser Thread erfolgreich ausgeführt wurde.

    Die Methode allThreadsExecuted überprüft, ob alle Threads erfolgreich ausgeführt wurden.
    Sie durchläuft das threadFlags-Array und prüft, ob alle Flags auf true gesetzt sind.
    Wenn dies der Fall ist, bedeutet dies, dass alle Threads erfolgreich ausgeführt wurden, andernfalls nicht.

    Die Verwendung von threadFlags ermöglicht es Ihnen, zu überprüfen, ob alle Threads ordnungsgemäß ausgeführt wurden, bevor Sie die Tests als erfolgreich bestätigen.*/

}
