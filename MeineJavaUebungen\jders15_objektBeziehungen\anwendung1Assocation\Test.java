package jders15_objektBeziehungen.anwendung1Assocation;

import java.util.ArrayList;

public class Test {
    public static void main(String[] args) {

         /*Has-a relationship, In dem gegebenen Beispiel wird die Assoziation in der "Test"-Klasse konkret
         umgesetzt, indem ein Student erstellt wird und ihm eine Adresse zugewiesen wird.
         Hier wird eine "Adressen"-Instanz erstellt und dann einem "Student"-Objekt zugewiesen.
         Dies zeigt die Assoziation zwischen den beiden Klassen, da der Student eine Adresse hat, die durch die Zuweisung festgelegt wird.
         */
        Adressen adresse1 = new Adressen("2", "Hauptstraße", "10247", "Berlin", "Deutschland");

        Student student1 = new Student("Jörn", "Hemme", 1992, null,
                "2350", null, null);

        // Zuweisung auf der Seite, wo werte für die Adresse übergeben werden
        student1.setAdresse(adresse1);

        ArrayList<String> faecher = new ArrayList<>();

        faecher.add("Mathematik");
        faecher.add("Physik");
        faecher.add("Chemie");

        // Zuweisung auf der Seite, wo die fächer gesetzt werden
        student1.setUnterrichtsFaecher(faecher);

        ArrayList<String> telefonnummer = new ArrayList<>();

        telefonnummer.add("03075525500");
        telefonnummer.add("01779767235");
        telefonnummer.add("01762805018");

        // Zuweisung auf der Seite, wo die Telefonnummern übergeben werden
        student1.setTelefonnummern(telefonnummer);

        System.out.println(student1);
    }
}
