package ajava_se.klassen.c03_lokaleKlassen;

public class C03_TestDrive {

    public static void main(String[] args) {

        C03_LocalMitVariableUndInstanz instance = new C03_LocalMitVariableUndInstanz();
        instance.doIt();

 /*
 Lokale Klassen - Zusammenfassung

  Was ist eine lokale Klasse?
  Eine lokale Klasse ist eine Klasse, die innerhalb einer Methode, eines Konstruktors oder eines Initialisierungsblocks definiert wird.
 
  Wofür werden lokale Klassen verwendet?
  Lokale Klassen werden verwendet, um Code zu kapseln, der nur innerhalb einer bestimmten Methode oder eines Konstruktors benötigt wird.
  Sie sind nützlich für die Implementierung komplexer Datenstrukturen oder Algorithmen mit begrenztem Gültigkeitsbereich.

 Eigenschaften und Einschränkungen lokaler Klassen:
 
 - Gültigkeitsbereich(Scope): Lokale Klassen sind nur innerhalb der Methode, des Konstruktors oder des Blocks sichtbar,
   in dem sie definiert wurden. Außerhalb dieses Bereichs nicht referenzierbar.
 
   Zugriff auf äußere Umgebung:
  - Lokale Variablen der umgebenden Methode: Seit Java 8 müssen diese effectively final sein (d. h. nach der ersten Zuweisung nicht mehr verändert werden).
 
  - Instanzvariablen und -methoden der äußeren Klasse: Voller Zugriff, sofern die lokale Klasse in einer Instanzmethode definiert ist.

  - Einschränkungen bei Statische Mitglieder der äußeren Klasse: Lokale Klassen in statischen Methoden dürfen nur auf statische Felder und Methoden der äußeren Klasse zugreifen.
 
 - Modifikatoren: Lokale Klassen können nicht als (public/protected/private/static) deklariert werden,
   da sie nur im lokalen Kontext sichtbar sind.
 
 - Keine Deklaration als interface oder enum.

 -Innerhalb der lokalen Klasse sind nur static final Konstanten zulässig; sonst keine statischen Member.
 
 Best Practices:
 - Verwenden Sie lokale Klassen, wenn der Code nur in einem begrenzten Kontext benötigt wird.
 - Halten Sie lokale Klassen klein und fokussiert auf eine spezifische Aufgabe.
 - Ziehen Sie anonyme Klassen in Betracht, wenn die Klasse nur einmal verwendet wird.
 - Ziehen Sie innere Klassen in Betracht, wenn die Klasse an mehreren Stellen innerhalb der äußeren Klasse verwendet wird.*/


    }
}
