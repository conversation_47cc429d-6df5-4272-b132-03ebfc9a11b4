package jders05_kontrollstrukturen;

public class WhileKontrolle {
    public static void main(String[] args) {

        /* Solange unsere Bedingung als Ergebnis wahr liefert,
         wird die Schleife durchlaufen.
         Sobald die Bedingung falsch ergibt, wird der Code innerhalb der Schleife komplett übersprungen.
         */

        int a = 0;

        while (a < 20) {
            System.out.println("Wert von a : " + a);
            a++;
        }
    }
}
