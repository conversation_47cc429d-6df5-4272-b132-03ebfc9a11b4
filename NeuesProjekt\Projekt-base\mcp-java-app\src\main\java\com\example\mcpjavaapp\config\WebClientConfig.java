package com.example.mcpjavaapp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class WebClientConfig {

    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }

    @Bean
    public WebClient ollamaWebClient(WebClient.Builder webClientBuilder,
                                     @org.springframework.beans.factory.annotation.Value("${ollama.base.url:http://localhost:11434}") String ollamaBaseUrl) {
        return webClientBuilder.baseUrl(ollamaBaseUrl).build();
    }
}
