package oca_05_nullPointer;

public class NullPointerMean {
    public static void main(String[] args) {
        Integer zahl = null;
        Boolean bl = null;

        String str1;                // stack
        String str2 = new String(); // stack->pointer->heap->createObject
        String str3 = "";           // stack->pointer->heap->createObject=""
        String str4 = null;         // stack->pointer->

        /*
        Wir können allen 4 fällen einen String einen wert zuweisen
        str1="java";
        System.out.println(str1);    java
        str2="java";
        System.out.println(str2);    java
        str3="java";
        System.out.println(str3);    java
        str4="java";
        System.out.println(str4);    java
        */

        // System.out.println(str1);  da kein wert zugewiesen wurde startet der code nicht
        // System.out.println(str2);  gibt nur Zeile aus. Finished with exit code 0
        // System.out.println(str3);  gibt nur Zeile aus. Finished with exit code 0
        // System.out.println(str4);  printed null Zuweisung als Ergebnis aus kein String. Finished with exit code 0

        // String manipulation mittels StringMethods
        // System.out.println("JAVA" + str1); da kein wert zugewiesen wurde startet der code nicht
        // System.out.println("JAVA " + str2);  gibt Java aus. Finished with exit code 0
        // System.out.println("JAVA " + str3);  gibt Java aus. Finished with exit code 0
        // System.out.println("JAVA " + str4);  gibt Java aus und printed null Zuweisung als Ergebnis von str4. Finished with exit code 0

        // System.out.println(str1.length());  da kein wert zugewiesen wurde startet der code nicht
        // System.out.println(str2.length());  gibt 0 aus. Finished with exit code 0
        // System.out.println(str3.length());  gibt 0 aus. Finished with exit code 0
        // System.out.println(str4.length());  wirft für str4 NullPointerException

        // NullPointer ist kein wert, es ist nur der Kennzeichner der kennzeichnet das kein wert besteht!
    }
}
