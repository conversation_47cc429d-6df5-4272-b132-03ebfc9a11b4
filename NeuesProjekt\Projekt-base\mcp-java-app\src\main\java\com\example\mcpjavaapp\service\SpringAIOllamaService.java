package com.example.mcpjavaapp.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.http.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * Spring AI 1.0 GA Style Service
 * Simuliert die Spring AI ChatClient API bis die echten Dependencies verfügbar sind
 */
@Service
public class SpringAIOllamaService {

    private static final Logger log = LoggerFactory.getLogger(SpringAIOllamaService.class);
    
    private final WebClient webClient;
    private final String modelName;
    private final Double temperature;
    private final Integer topK;

    public SpringAIOllamaService(
            @Value("${spring.ai.ollama.base-url:http://localhost:11434}") String baseUrl,
            @Value("${spring.ai.ollama.chat.options.model:phi4-mini-reasoning:3.8b}") String modelName,
            @Value("${spring.ai.ollama.chat.options.temperature:0.7}") Double temperature,
            @Value("${spring.ai.ollama.chat.options.top-k:40}") Integer topK) {
        
        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .build();
        this.modelName = modelName;
        this.temperature = temperature;
        this.topK = topK;
        
        log.info("SpringAIOllamaService initialisiert mit Spring AI 1.0 GA Style");
        log.info("Modell: {}, Temperature: {}, TopK: {}", modelName, temperature, topK);
    }

    /**
     * Spring AI 1.0 GA Style ChatClient Simulation
     * In der echten Implementierung: chatClient.prompt().user(prompt).call().content()
     */
    public String generateCompletion(String prompt) {
        log.info("Spring AI ChatClient Style - Sende Prompt an Ollama");
        log.debug("Prompt: {}", prompt);
        
        return sendChatRequest(prompt);
    }

    /**
     * Erweiterte Prompt-Verarbeitung mit System-Kontext
     */
    public String generateWithSystemContext(String systemPrompt, String userPrompt) {
        log.info("Spring AI ChatClient Style - Sende Prompt mit System-Kontext");
        
        Map<String, Object> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", systemPrompt);

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", userPrompt);

        List<Map<String, Object>> messages = new ArrayList<>();
        messages.add(systemMessage);
        messages.add(userMessage);

        return sendChatRequestWithMessages(messages);
    }

    /**
     * Code-spezifische Methoden mit Spring AI Style
     */
    public String analyzeCodeWithAI(String code) {
        String systemPrompt = "Du bist ein erfahrener Java-Entwickler und Code-Reviewer. " +
                             "Analysiere Code auf Bugs, Performance-Probleme und Verbesserungsmöglichkeiten.";
        String userPrompt = "Analysiere den folgenden Java-Code:\n\n```java\n" + code + "\n```";
        
        return generateWithSystemContext(systemPrompt, userPrompt);
    }

    public String generateDocumentationWithAI(String code, String format) {
        String systemPrompt = "Du bist ein erfahrener Java-Entwickler, der ausführliche Dokumentation erstellt.";
        String userPrompt = String.format(
            "Erstelle eine detaillierte Dokumentation für den folgenden Java-Code im %s-Format:\n\n```java\n%s\n```",
            format, code
        );
        
        return generateWithSystemContext(systemPrompt, userPrompt);
    }

    public String improveCodeWithAI(String code) {
        String systemPrompt = "Du bist ein erfahrener Java-Entwickler. " +
                             "Verbessere Code für bessere Lesbarkeit und Performance.";
        String userPrompt = "Verbessere den folgenden Java-Code:\n\n```java\n" + code + "\n```";
        
        return generateWithSystemContext(systemPrompt, userPrompt);
    }

    /**
     * Interne Methode für Chat-Anfragen
     */
    private String sendChatRequest(String prompt) {
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);

        List<Map<String, Object>> messages = new ArrayList<>();
        messages.add(message);

        return sendChatRequestWithMessages(messages);
    }

    /**
     * Interne Methode für Chat-Anfragen mit mehreren Nachrichten
     */
    private String sendChatRequestWithMessages(List<Map<String, Object>> messages) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", modelName);
            requestBody.put("messages", messages);
            requestBody.put("stream", false);
            
            // Spring AI Style Options
            Map<String, Object> options = new HashMap<>();
            options.put("temperature", temperature);
            options.put("top_k", topK);
            requestBody.put("options", options);

            String response = webClient.post()
                    .uri("/api/chat")
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            log.info("Spring AI Style - Erfolgreiche Antwort von Ollama erhalten");
            return extractContentFromResponse(response);
            
        } catch (Exception e) {
            log.error("Spring AI Style - Fehler bei der Kommunikation mit Ollama: ", e);
            return "Fehler bei der Spring AI Kommunikation: " + e.getMessage();
        }
    }

    /**
     * Extrahiert den Inhalt aus der Ollama-Antwort
     */
    private String extractContentFromResponse(String response) {
        try {
            if (response.contains("\"content\":")) {
                int startIndex = response.indexOf("\"content\":\"") + 11;
                int endIndex = response.indexOf("\"", startIndex);
                if (endIndex > startIndex) {
                    return response.substring(startIndex, endIndex)
                            .replace("\\n", "\n")
                            .replace("\\\"", "\"")
                            .replace("\\\\", "\\");
                }
            }
            return response;
        } catch (Exception e) {
            log.warn("Fehler beim Extrahieren des Inhalts: ", e);
            return response;
        }
    }
}
