package jders08_mathMethoden;

public class MathMethodenRandom {
    public static void main(String[] args) {

        double zufZahl;
        int count = 1;

        for (int i = 0; i < 10; i++) {
            zufZahl = Math.random() * 10 + 1;  //*10 zwischen 0 und 10 & wenn die 0 nicht einbezogen werden soll: Math.random()*10+1
            System.out.println(count + ". Zufallszahl : " + (int) zufZahl);
            count++;
        }

    }
}
