package ajava_se.klassen.c01_innereMitgliedsklassen;

public class C02_ArraysAussenKlasse {

    /* Array in der äußeren Klasse (namen): Dieses Array ist eine Membervariable der äußeren Klasse (AussenKlasse).
       Das bedeutet, dass es von allen Methoden innerhalb der äußeren Klasse und auch von anderen Klassen,
       die eine Instanz der äußeren Klasse haben, verwendet werden kann.*/
    private String[] namen; // Ein Array zur Speicherung von Namen

    public C02_ArraysAussenKlasse(String[] namen) {
        this.namen = namen;
    }

    public void gebeNamenAus() {
        for (String name : namen) {
            System.out.println("Name: " + name);
        }
    }

    /* Array in der inneren Klasse (werte): Dieses Array ist eine Membervariable der inneren Klasse (InnereKlasse).
       Es ist nur von Instanzen der inneren Klasse aus sichtbar und kann nicht direkt von außen aufgerufen werden.
       Die innere Klasse hat jedoch Zugriff auf die Membervariable namen der äußeren Klasse,
       da sie Teil des äußeren Klassenkontexts ist.*/
    class InnereKlasse {
        private int[] werte; // Ein Array zur Speicherung von Werten

        public InnereKlasse(int[] werte) {
            this.werte = werte;
        }

        public void gebeWerteAus() {
            for (int wert : werte) {
                System.out.println("Wert: " + wert);
            }
        }
    }

}
