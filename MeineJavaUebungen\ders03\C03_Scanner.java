package ders03;

import java.util.Scanner;

/**
 * Diese Klasse demonstriert die Kombination von Scanner-Methoden mit String-Methoden.
 *
 * Das Programm:
 * 1. Liest ein Wort vom Benutzer ein
 * 2. Extrahiert den ersten Buchstaben des Wortes
 * 3. Gib<PERSON> diesen Buchstaben aus
 *
 * Wichtige Konzepte:
 * - Verwendung von next() zum Einlesen eines Wortes (bis zum nächsten Leerzeichen)
 * - Verwendung von charAt() zum Zugriff auf einzelne Zeichen eines Strings
 * - Verkettung von Methodenaufrufen (Method Chaining)
 */
public class C03_Scanner {
    /**
     * Die Hauptmethode liest ein Wort vom Benutzer ein und gibt den ersten Buchstaben aus.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Wir wollen vom Benutzer ein Wort einlesen und den ersten Buchstaben ausgeben

        // Scanner-Objekt zur Eingabe erstellen
        Scanner scan = new Scanner(System.in);

        // Benutzer zur Eingabe auffordern
        System.out.println("Bitte geben Sie ein Wort ein:");

        // Ein Wort einlesen und sofort den ersten Buchstaben extrahieren (Method Chaining)
        // 1. scan.next() liest ein Wort bis zum nächsten Leerzeichen ein
        // 2. .charAt(0) extrahiert das erste Zeichen (an Index 0) aus diesem Wort
        char ersterBuchstabe = scan.next().charAt(0);

        /*
         * Erklärung zur Indizierung von Strings:
         *
         * Die Zeichen eines Strings werden in einem nullbasierten Index gespeichert.
         * Beispiel für den String "HALLO":
         * - 0. Index: H
         * - 1. Index: A
         * - 2. Index: L
         * - 3. Index: L
         * - 4. Index: O
         *
         * Die Scanner-Klasse bietet keine direkte Methode wie nextChar() zum Einlesen einzelner Zeichen.
         * Stattdessen kombinieren wir:
         * - scan.next(): Liest die Benutzereingabe bis zum nächsten Leerzeichen als String
         * - charAt(0): Extrahiert das erste Zeichen (Index 0) aus diesem String
         */

        // Ausgabe des ersten Buchstabens
        System.out.println("Der erste Buchstabe des eingegebenen Wortes lautet: " + ersterBuchstabe);

        // Scanner schließen, um Ressourcenlecks zu vermeiden
        scan.close();

        /*
         * Hinweis: Bei der Eingabe mehrerer Wörter (z.B. "Hallo Welt") würde nur
         * der erste Buchstabe des ersten Wortes ("H") extrahiert werden, da next()
         * nur bis zum nächsten Leerzeichen liest.
         */
    }
}
