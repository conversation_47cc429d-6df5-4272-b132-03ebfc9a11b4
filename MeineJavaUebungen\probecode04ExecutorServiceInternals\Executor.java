package probecode04ExecutorServiceInternals;

public class Executor {

    public static void main(String[] args) {

        // Da die For-Schleife iterativ ist, sind nicht alle Threads exakt zur selben Zeit erstellt.
        // Nachteil: bei 1000 Threads hätten wir Schwierigkeiten beim Verwalten und Performance Einbußen.
        for (int i = 0; i < 10; i++){
            new Thread(()->{
                System.out.println("Hello from " + Thread.currentThread().getName());
            }).start();

        }
    }
}
