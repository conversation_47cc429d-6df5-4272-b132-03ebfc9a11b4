package jders17_polymorphie.anwendung1Polymorphie;

public class Person {

    /*
     * Jede Klasse die erstellt wird, erbt von Java Object
     * */

    private String vorname;

    private String nachname;

    private int geburtsjahr;

    public Person() {

    }

    // Die Objekt-Klasse besitzt unter super ein Parameterlosen constructor was wir nicht nutzen werden
    public Person(String vorname, String nachname, int geburtsjahr) {
        this.vorname = vorname;
        this.nachname = nachname;
        this.geburtsjahr = geburtsjahr;
    }

    public String getVorname() {
        return vorname;
    }

    public void setVorname(String vorname) {
        this.vorname = vorname;
    }

    public String getNachname() {
        return nachname;
    }

    public void setNachname(String nachname) {
        this.nachname = nachname;
    }

    public int getGeburtsjahr() {
        return geburtsjahr;
    }

    public void setGeburtsjahr(int geburtsjahr) {
        this.geburtsjahr = geburtsjahr;
    }

    @Override
    public String toString() {
        return "Person{" +
                "vorname='" + vorname + '\'' +
                ", nachname='" + nachname + '\'' +
                ", geburtsjahr=" + geburtsjahr +
                '}';
    }
}
