package jders05_kontrollstrukturen;

public class IfKontrolle {
    public static void main(String[] args) {
        int zahl1 = 45;
        int zahl2 = 35;

        //boolean zustand = zahl1 > zahl2;
    /*
            if (zustand) {      // true
                System.out.println("Zahl 1. ist Größer als Zahl 2.");
            }*/

        if (zahl1 > zahl2) { // Überprüfe, ob zahl1 größer ist als zahl2
            System.out.println("Zahl 1. ist Größer als Zahl 2."); // Gib eine entsprechende Nachricht aus, wenn die Bedingung erfüllt ist

            if ((zahl1 - zahl2) > 5) { // Überprüfe, ob die Differenz zwischen zahl1 und zahl2 größer als 5 ist
                System.out.println("Der Wert von Zahl 1. ist größer als 5!"); // Gib eine entsprechende Nachricht aus, wenn die Bedingung erfüllt ist
            }
        }

        if (zahl1 < zahl2) { // <PERSON><PERSON>pr<PERSON>fe, ob zahl1 kleiner ist als zahl2
            System.out.println("Zahl 2. ist Größer als Zahl 1."); // Gib eine entsprechende Nachricht aus, wenn die Bedingung erfüllt ist
        }

        if (zahl1 != zahl2) { // Überprüfe, ob zahl1 ungleich zahl2 ist
            System.out.println("Zahl 1. ist nicht gleich Zahl 2."); // Gib eine entsprechende Nachricht aus, wenn die Bedingung erfüllt ist
        }

        if (zahl1 == zahl2) { // Überprüfe, ob zahl1 gleich zahl2 ist
            System.out.println("Zahl 1. ist gleich wie Zahl 2."); // Gib eine entsprechende Nachricht aus, wenn die Bedingung erfüllt ist
        }

        System.out.println("Vergleichs Operationen sind zu Ende Durchlaufen!"); // Gib eine Abschlussnachricht aus
    }
}
