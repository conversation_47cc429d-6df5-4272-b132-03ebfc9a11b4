package designPatterns.creational.prototype.prototypeWiederholung4DeepCopy;

public class <PERSON>kumentTyp implements Cloneable {

    private Long id;
    private String name;

    public DokumentTyp() {

    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    // da an der Stelle kein anderes Objekt liegt, können wir direkt shallow-copy nutzen
    @Override
    protected DokumentTyp clone() throws CloneNotSupportedException {
        return (DokumentTyp) super.clone();
    }
}
