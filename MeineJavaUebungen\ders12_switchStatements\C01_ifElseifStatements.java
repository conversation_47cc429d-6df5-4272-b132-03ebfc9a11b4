package ders12_switchStatements;

import java.util.Scanner;

public class C01_ifElseifStatements {
    public static void main(String[] args) {
        // Vom Nutzer den Tag als Zahl nehmen und Namen des Tages Ausgeben

        Scanner scan = new Scanner(System.in);
        System.out.println("Bitte Tag in Zahl eingeben");
        int tagNo = scan.nextInt();

        if (tagNo == 1) {
            System.out.println("Montag");
        } else if (tagNo == 2) {
            System.out.println("Dienstag");
        } else if (tagNo == 3) {
            System.out.println("Mittwoch");
        } else if (tagNo == 4) {
            System.out.println("Donnerstag");
        } else if (tagNo == 5) {
            System.out.println("Freitag");
        } else if (tagNo == 6) {
            System.out.println("Samstag");
        } else if (tagNo == 7) {
            System.out.println("Sonntag");
        } /*else if (tagNo <= 0 || tagNo > 7) {
            System.out.println("Ungültige Zahl für Tag der Woche");
        }*/ else {
            System.out.println("Gebe noch mal eine Zahl von 1 bis 7 ein");
        }

    }
}

