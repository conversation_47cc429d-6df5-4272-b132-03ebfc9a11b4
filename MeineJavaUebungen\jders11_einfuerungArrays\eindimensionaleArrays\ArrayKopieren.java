package jders11_einfuerungArrays.eindimensionaleArrays;

public class ArrayKopieren {
    public static void main(String[] args) {

        int[] array1 = {3, 5, 45, 35, 56, 63, 88, 112, 153, 133, 512, 256};

        int[] array2 = new int[array1.length]; // Erstellt ein neues Array "array2" mit der gleichen Länge wie "array1"

        // System.arraycopy zum Kopieren der (Gesamten) Werte von array1 (ab) Index 0 ins array2, 0 (bis) die gesamte Länge array1
        // zum Kopieren ab eines Indexes den genauen index angeben an Stelle von array1.length, in diesem Fall liefert die Kopie 0 für die restlichen werte des arrays1
        System.arraycopy(array1, 3, array2, 0, 6);

        /*
        // Schleife zum Kopieren der Werte von "array1" in "array2"
        for (int i = 0; i < array1.length; i++) {
            array2[i] = array1[i]; // Kopiert alle Werte von "array1[i]" in "array2[i]"
        } */

        // Schleife zum Ausgeben der Werte von "array2"
        for (int j : array2) {

            //System.out.println(array2[i]);

            if (j != 0) {

                System.out.println(j);  // Ausgabe ohne 0 Werte
            }
        }

    }
}
  /*Code 1:for (int i = 0; i < array2.length; i++) {if (array2[i] != 0) {System.out.println(array2[i]);}}
    *
    * Verwendet eine herkömmliche for-Schleife mit einem Zähler variable i zum Durchlaufen des Arrays array2.
    * Prüft in jedem Schleifendurchlauf, ob der Wert von array2[i] ungleich 0 ist, und gibt ihn dann aus.

    * Code 2: for (int j : array2){if (j != 0) {System.out.println(j);}}
    * Verwendet eine erweiterte for-Schleife, auch bekannt als "for-each"-Schleife, zum Durchlaufen des Arrays array2.
    * Der Wert des aktuellen Elements wird in der Variablen j gespeichert.
    * Prüft in jedem Schleifendurchlauf, ob der Wert von j ungleich 0 ist, und gibt ihn dann aus.
    * Beide Codes haben das gleiche Ergebnis: Sie geben die Werte von array2 aus, wobei 0-Werte ausgelassen werden.
    *
    * Der Unterschied zwischen den beiden Schleifen besteht hauptsächlich in der Syntax und Lesbarkeit des Codes.
    * Die erweiterte for-Schleife (Code 2) ist etwas kompakter und erfordert keine explizite Zähler variable.
    * Dadurch kann sie einfacher zu lesen und zu verstehen sein, insbesondere wenn der Index nicht benötigt wird.
    * Der erste Code verwendet eine traditionelle for-Schleife (Code 1), die häufiger verwendet wird,
    * wenn ein Zugriff auf den Index erforderlich ist.
    *
    * In Bezug auf den Gesamtcode und die Funktionalität haben die unterschiedlichen Schleifentypen
    * keinen wesentlichen Einfluss auf das Ergebnis. Das Kopieren der Werte von array1 in array2 wird
    * durch System.arraycopy() durchgeführt, und die Ausgabe der Werte von array2 wird in beiden Fällen ohne 0-Werte erreicht.*/
