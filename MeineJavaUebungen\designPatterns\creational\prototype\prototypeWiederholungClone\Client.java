package designPatterns.creational.prototype.prototypeWiederholungClone;

public class Client {

    public static void main(String[] args) {
        TestClone main = new TestClone();
        TestClone clone = main.clone();
        // Der Wert ist direkt kopiert, und wird nicht vom Konstruktor ausgeführt
        System.out.println(clone.getInstanceVariable() + clone.getInstanceVariable().hashCode());

    }
}
