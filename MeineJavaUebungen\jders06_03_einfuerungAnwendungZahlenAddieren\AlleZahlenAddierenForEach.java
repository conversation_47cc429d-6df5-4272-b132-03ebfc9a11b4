package jders06_03_einfuerungAnwendungZahlenAddieren;

public class AlleZahlenAddierenForEach {
    public static void main(String[] args) {

        // Gesamtzahl von allen Zahlen ausgeben
        /* For-Each-Schleifen werden für Schleifen über Arrays oder Collections verwendet Beispiel:
            int[] array = {1, 2, 3, 4, 5};
            for (int element : array) {
                System.out.println(element);
        }
        */

        int[] zahlen = new int[51];
        int gesamt = 0;

        for (int i = 0; i <= 50; i++) {
            zahlen[i] = i;
        }

        for (int zahl : zahlen) {
            gesamt += zahl;
        }

        System.out.println("Gesamt: " + gesamt);
    }
}
