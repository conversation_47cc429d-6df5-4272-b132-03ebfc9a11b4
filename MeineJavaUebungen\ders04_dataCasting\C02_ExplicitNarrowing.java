package ders04_dataCasting;

/**
 * Diese Klasse demonstriert explizites Casting (Narrowing) in Java,
 * insbesondere die Umwandlung von größeren zu kleineren Datentypen.
 *
 * Beim expliziten Narrowing kann es zu Datenverlust kommen, wenn der Wert
 * außerhalb des Wertebereichs des Zieldatentyps liegt. In diesem Fall
 * wird der Wert "umgebrochen" (Overflow/Underflow).
 *
 * Beispiel: Bei der Umwandlung von int zu byte (Wertebereich -128 bis 127)
 * werden Werte außerhalb dieses Bereichs durch Modulo-Arithmetik angepasst.
 */
public class C02_ExplicitNarrowing {
    /**
     * Die Hauptmethode demonstriert explizites Casting von int zu byte.
     *
     * @param args Kommandozeilenargumente (nicht verwendet)
     */
    public static void main(String[] args) {
        // Wir beginnen mit einem int-Wert, der größer ist als der maximale byte-Wert (127)
        int a = 135;
        System.out.println("Ursprünglicher int-Wert: " + a);

        // Explizites Casting von int zu byte
        // Der Wertebereich von byte ist -128 bis 127
        byte b = (byte) a;
        System.out.println("Nach Casting zu byte: " + b);  // Ausgabe: -121

        /*
         * Erklärung des Ergebnisses:
         *
         * Beim Casting von int (135) zu byte wird der Wert auf den Wertebereich von byte angepasst.
         * Dies geschieht durch eine Modulo-Operation mit der Größe des Wertebereichs (256).
         *
         * Mathematisch: 135 % 256 = 135
         * Da 135 > 127 (maximaler positiver byte-Wert), wird der Wert "umgebrochen":
         * 135 - 256 = -121
         *
         * Weitere Beispiele für int zu byte Casting:
         * - 128 wird zu -128 (Überlauf um genau 1)
         * - 129 wird zu -127 (Überlauf um 2)
         * - 130 wird zu -126 (Überlauf um 3)
         * - 256 wird zu 0 (genau ein voller Durchlauf des Wertebereichs)
         * - 260 wird zu 4 (ein voller Durchlauf plus 4)
         * - 512 wird zu 0 (genau zwei volle Durchläufe)
         * - 522 wird zu 10 (zwei volle Durchläufe plus 10)
         *
         * Diese Art der Umwandlung wird als "Zweierkomplementdarstellung" bezeichnet
         * und ist die Standardmethode für die Darstellung von Ganzzahlen in Computern.
         */

        // Demonstration weiterer Beispiele
        System.out.println("\nWeitere Beispiele für int zu byte Casting:");
        System.out.println("(byte) 128 = " + (byte) 128);  // -128
        System.out.println("(byte) 129 = " + (byte) 129);  // -127
        System.out.println("(byte) 256 = " + (byte) 256);  // 0
        System.out.println("(byte) 260 = " + (byte) 260);  // 4
    }
}
