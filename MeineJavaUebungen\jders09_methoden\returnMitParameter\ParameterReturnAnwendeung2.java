package jders09_methoden.returnMitParameter;

public class ParameterReturnAnwendeung2 {
    public static void main(String[] args) {

        boolean b = vergleiche(5, 3);
        System.out.println("5 ist größer als 3 : " + b);
        System.out.println();
        System.out.println("5 ist größer als 3 : " + b);

        b = vergleiche(15,17);
        System.out.println("2. Ergebnis : " + b);


    }

    public static boolean vergleiche(int a, int b) {

       /* if (a > b) {
            return true;
        } else {
            return false;
        }*/

        boolean zustand = (a > b) ? true : false;  // Kurzform

        return zustand;
    }

}
