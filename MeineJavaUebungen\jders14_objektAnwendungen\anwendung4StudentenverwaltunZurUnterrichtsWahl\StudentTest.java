package jders14_objektAnwendungen.anwendung4StudentenverwaltunZurUnterrichtsWahl;

import java.util.ArrayList;

public class StudentTest {
    public static void main(String[] args) {

        // Wenn Student('Beispiels auf eine Seite') <PERSON><PERSON><PERSON> wurde, jedoch Fächer noch nicht gewählt wurden.
        // Beispielsweise wenn Nutzer keine Werte zuweisen muss = null
        // Wenn wir z.B. eine vordefinierte studentenNummer an anderer Stelle übergeben wollen, sollte studentenNummer = null stehen
        Student student1 = new Student("Jörn", "Musial", 1994, "2350", null);

        // Student wählt seine Fächer in die ArrayList ('Beispiels auf einer anderen Seite') vom System
        ArrayList<String> faecherStudiumBioInformatik = new ArrayList<>();

        // Student füllt Fächer in die Liste
        faecherStudiumBioInformatik.add("Mathematik");
        faecherStudiumBioInformatik.add("Physik");
        faecherStudiumBioInformatik.add("Biologie");

        // Dann wird anschließend Fächer an Studenten Übergeben
        student1.setUnterrichtsFaecher(faecherStudiumBioInformatik);

        // Mehrere Instanzen von Studenten
        Student student2 = new Student("Tim", "Feld", 1995, "2351", null);
        // Wenn Student2 will, kann er das gleiche Studium wie Student1 beziehen
        student2.setUnterrichtsFaecher(faecherStudiumBioInformatik);


        Student student3 = new Student("Jan", "Butter", 1993, "2352", null);

        Student student4 = new Student("Bernd", "Fisch", 1994, "2353", null);

        // Liste enthält definiertes Objekt der Klasse, mit Studenten als Datentyp
        ArrayList<Student> ws = new ArrayList<>();

        // Unsere Studenten Instanzen werden zur Liste hinzugefügt
        // wobei hier wir die Reihenfolge der Liste bestimmen durch unser Hinzufügen
        ws.add(student4);
        ws.add(student3);
        ws.add(student2);
        ws.add(student1);

        /*
        for (Student student : ws) {
            System.out.println(student);
        }
        */

        for (int i = 0; i < ws.size(); i++) {
            // Eine Schleife, die von 0 bis zur Größe der Liste läuft
            // Die Variable "i" dient als Zähler und Index für den Zugriff auf die Elemente der Liste

            // Das aktuelle Studentenobjekt aus der Liste abrufen und der Variable "student" zuweisen.
            // Den Index verwenden, um das Studentenobjekt aus der Liste abzurufen.
            // Das i an der Stelle ws.get(i) dient als Index, um das entsprechende Element in der Liste ws abzurufen.
            // In einer Liste sind die Elemente anhand von Indizes nummeriert, wobei der erste Index 0 ist.
            Student student = ws.get(i);

            // Ausgabe der Informationen des aktuellen Studenten
            System.out.println(student);
           // System.out.println(student.getVorname()+student.getNachname());
        }


    }


}
