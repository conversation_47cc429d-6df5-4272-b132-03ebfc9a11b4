package jders19_interface.anwendung3;

import java.util.ArrayList;

public class PersonenVerarbeitungImpl implements PersonenVerarbeitungsvorgaenge {

    // Eine Leere Liste die wir nur von draußen mit get Methoden erreichen, in unserem fall ohne set-Methode
    private ArrayList<Person> personen = new ArrayList<>();

    @Override
    public boolean personSpeichern(Person person) {

        // da zustand ein boolean wie die Methode selbst ist, können wir direkt unsere zustand variable return
        boolean zustand = personen.add(person);
        // hier könnte auch eine Nutzer-Ausgabe kommen, das alles erfolgreich war

        return zustand;
    }

    @Override
    public boolean personLoeschen(Person person) {

        boolean zustand = personen.remove(person);

        return zustand;
    }

    @Override
    public void personInfosAusgeben(Person person) {


        System.out.println("Vorname : " + person.getVorname());
        System.out.println("Nachname : " + person.getNachname());
        System.out.println("Geburtsjahr : " + person.getGeburtsjahr());

        // Das get liefert dar Objekt als Datentyp der Klasse Adresse als Ergebnis.
        // Wir erstellen eine variable  adress in die dann seine Adresse gespeichert wird, und nun können wir alles von der Klasse erreichen
        Adresse adress = person.getAdresse();

        // Wir erhalten von der gesendeten Person die gespeicherten Werte, die schon zuvor erstellt wurden
        // Wir bevor ziehen die Linke Seite in Betracht statt mit person.getAdresse() = System.out.println(""+person.getAdresse().getStrasse());
        // zu erreichen, weil es noch detaillierter wird
        System.out.println("Straße : " + adress.getStrasse());
        System.out.println("Postleitzahl : " + adress.getPostleitzahl());
        System.out.println("Stadt : " + adress.getStadt());

    }

    @Override
    public void adressInfosAusgeben(Person person) {

        Adresse adress = person.getAdresse();

        System.out.println("Wohnanschrift von : " +person.getVorname() + person.getNachname() + " in : ");

        System.out.println("Straße : " + adress.getStrasse());
        System.out.println("Postleitzahl : " + adress.getPostleitzahl());
        System.out.println("Stadt : " + adress.getStadt());
    }

    // Wir nutzen unsere Liste aus der Klasse
    @Override
    public void personenListe() {

        for (Person person : personen){

            System.out.println("Vorname : " + person.getVorname());
            System.out.println("Nachname : " + person.getNachname());
            System.out.println("Geburtsjahr : " + person.getGeburtsjahr());

            Adresse adress = person.getAdresse();
            System.out.println("Straße : " + adress.getStrasse());
            System.out.println("Postleitzahl : " + adress.getPostleitzahl());
            System.out.println("Stadt : " + adress.getStadt());
            System.out.println("-------------------------------------------------------------------");
        }

    }
}
