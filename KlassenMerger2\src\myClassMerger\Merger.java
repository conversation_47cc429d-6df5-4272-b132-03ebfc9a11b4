package myClassMerger;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;

public class Merger {

    public static void main(String[] args) {
        // Eingabeverzeichnis
        String inputDirectory = "C:\\Users\\<USER>\\JavaPlace\\MeineJavaUebungen\\ajava_se\\klassen\\c04_anonymeInnereKlassen"; // Verzeichnis mit den Eingabedateien
        String outputDirectory = "C:\\Users\\<USER>\\JavaPlace\\KlassenMerger2\\out\\"; // Verzeichnis für die Ausgabedatei

        // Paketnamen dynamisch aus dem Eingabeverzeichnis ableiten
        String packageName = new File(inputDirectory).getName();

        // Ausgabedatei
        String outputFile = outputDirectory + "/MergedFile.java";

        // Eingabeverzeichnis prüfen
        File inputDir = new File(inputDirectory);
        if (!inputDir.exists() || !inputDir.isDirectory()) {
            System.err.println("Das angegebene Eingabeverzeichnis existiert nicht oder ist kein Verzeichnis.");
            return;
        }

        // Ausgabeverzeichnis erstellen, falls es nicht existiert
        File outputDir = new File(outputDirectory);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        // Alle Java-Dateien im Eingabeverzeichnis einlesen
        File[] javaFiles = inputDir.listFiles((dir, name) -> name.endsWith(".java"));

        if (javaFiles == null || javaFiles.length == 0) {
            System.err.println("Keine Java-Dateien im Eingabeverzeichnis gefunden.");
            return;
        }

        // FileWriter für die Ausgabe erstellen
        try (FileWriter writer = new FileWriter(outputFile)) {

        	 // Kommentar mit Verzeichnisname
            writer.write("// Gemergter Code aus dem Verzeichnis: " + inputDirectory + "\n\n");
            
            // Paketdeklaration
            writer.write("// package " + packageName + ";\n\n");

            // Kennzeichnung, wo das neue Package beginnt
            writer.write("// --- BEGIN DES GEMERGTEN CODES AUS DEM VERZEICHNIS ---\n\n");

            for (File file : javaFiles) {
                // Dateiinhalt lesen
                String fileContent = Files.readString(file.toPath());

                // Dateiinhalt ohne Import-Zeilen und Paketnamen hinzufügen
                fileContent = fileContent.replaceAll("(?m)^package .*;\\s*", ""); // Paketnamen entfernen
                fileContent = fileContent.replaceAll("(?m)^import .*;\\s*", ""); // Import-Zeilen entfernen

                // Dateiinhalt in die Ausgabedatei schreiben
                writer.write(fileContent + "\n\n");
            }

            // Kennzeichnung des Endes des Pakets
            writer.write("// --- ENDE DES GEMERGTEN CODES ---\n");

            System.out.println("Die Datei " + outputFile + " wurde erfolgreich erstellt und überschrieben.");
        } catch (IOException e) {
            System.err.println("Fehler beim Schreiben der Ausgabedatei: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
