package jders16_vererbungen.anwendung3ToString;

import java.util.ArrayList;

public class Test {
    public static void main(String[] args) {
        //Ausgabe von dem Constructor mit 5 Parametern, erfolgt ohne die toString Methode

        // Student Objekt erstellen
        Student student1 = new Student ("Sam", "Wolle", 1998, "2380", null);

        ArrayList<String> faecher = new ArrayList<>();
        faecher.add("Mathematik");
        faecher.add("Physik");
        faecher.add("Chemie");

        student1.setUnterrichtsFaecher(faecher);
        // Constructor Klasse nur Person Parametern genutzt
        Student student2 = new Student("Beni", "Fachmann", 1995);
        ArrayList<String> faecher2 = new ArrayList<>();
        faecher2.add("EN");
        faecher2.add("DE");
        faecher2.add("SP");

        student2.setStudentenNummer("2381");
        // Müsste selbst Liste Erstellen für neue Fächer oder die vorhandene Liste nehmen
        student2.setUnterrichtsFaecher(faecher2);

        System.out.println("Name : "+ student1.getVorname());
        System.out.println("Nachname : "+ student1.getNachname());
        System.out.println("Geburtsjahr : "+ student1.getGeburtsjahr());
        System.out.println("Studenten Nummer : "+ student1.getStudentenNummer());
        System.out.println("Unterrichts Fächer : " + student1.getUnterrichtsFaecher());

        System.out.println();
        System.out.println(student1); // toString aus der Studenten Klasse
        System.out.println(student2);

    }

}
