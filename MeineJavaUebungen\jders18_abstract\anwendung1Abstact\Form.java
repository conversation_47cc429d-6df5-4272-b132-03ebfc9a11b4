package jders18_abstract.anwendung1Abstact;

public abstract class Form {

    /*Aus abstract Klassen kann mann keine Instanz Erstellen
    *
    * Form form = new Form();
    * es ist wichtig zu beachten, dass sowohl abstrakte Klassen als auch Schnittstellen nicht direkt
    * instanziiert werden können. Sie dienen als Grundlage für die Erstellung
    * von konkreten Klassen, die dann instanziiert werden können.
    *
    * Bei abstrakten Klassen können nicht-abstrakte Methoden von abgeleiteten Klassen überschrieben werden.
    * Abstrakte Methoden müssen von abgeleiteten Klassen implementiert werden, können jedoch nicht überschrieben werden.
    * */

    // Alle extends Klassen sind verpflichtet die getInhalt methode Automatisch zu nutzen(import)
    public abstract double getInhalt();
    // Alle extends Klassen sind verpflichtet die getUmfang methode Automatisch zu nutzen(import)
    public abstract double getUmfang();

    /* Definition -Eine abstrakte Klasse ist eine Klasse, die mit dem Schlüsselwort "abstract" markiert ist
    und mindestens eine abstrakte Methode enthält. Eine abstrakte Methode ist
    eine Methode ohne Implementierung, die von den abgeleiteten Klassen (Subklassen) implementiert werden muss.
    Eine abstrakte Klasse kann auch normale (konkrete) Methoden enthalten, die bereits implementiert sind
    und von den abgeleiteten Klassen geerbt werden.

    Mehrfachvererbung -Eine Java-Klasse kann nur von einer abstrakten Klasse erben,
    d. h. die Mehrfachvererbung ist in Java nicht erlaubt.

    Implementierung -Die abgeleitete Klasse (Subklasse) erweitert die abstrakte Klasse
    mit dem Schlüsselwort "extends" und implementiert die abstrakten Methoden der abstrakten Klasse.
    */
}
