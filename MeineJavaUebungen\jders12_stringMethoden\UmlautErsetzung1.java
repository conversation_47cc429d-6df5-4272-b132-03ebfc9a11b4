package jders12_stringMethoden;

public class UmlautErsetzung1 {

    /* In der main-Methode wird ein Beispielstring erstellt, der Umlaute enthält.
     * Der String wird dann an die replaceUmlaute()-Methode übergeben,
     * um die Umlaute zu ersetzen und Auszugeben */

    private static String[][] UMLAUT_REPLACEMENTS = {
            {"Ä", "Ae"},
            {"Ü", "Ue"},
            {"Ö", "Oe"},
            {"ä", "ae"},
            {"ü", "ue"},
            {"ö", "oe"},
            {"ß", "ss"}
    };

    public static void main(String[] args) {
        String input = "Özer Özözbek"; // Originaler String

        String umlauteErsetzt = replaceUmlaute(input); // Umlaute ersetzen

        System.out.println("Original: " + input);
        System.out.println("Ersetzt: " + umlauteErsetzt);
    }

    // Methode zum Ersetzen der Umlaute
    public static String replaceUmlaute(String input) {
        String result = input;

        // Durchlaufe alle Einträge im UMLAUT_REPLACEMENTS-Array
        for (int i = 0; i < UMLAUT_REPLACEMENTS.length; i++) {
            String umlaut = UMLAUT_REPLACEMENTS[i][0];
            String ersatz = UMLAUT_REPLACEMENTS[i][1];

            // Ersetze Umlaut durch entsprechenden Ersatz
            result = result.replace(umlaut, ersatz);
        }

        return result;
    }

    /*Das UMLAUT_REPLACEMENTS-Array ist im Klassenrumpf und mit dem private-Zugriffsmodifikator deklariert,
     * um es als private Klassen Variable zu kennzeichnen. Das bedeutet,
     * dass die Variable nur innerhalb der Klasse UmlautErsetzung zugänglich ist
     *  und von anderen Klassen nicht direkt verändert werden kann.
     *
     * Es wird empfohlen, die UMLAUT_REPLACEMENTS-Variable als private zu deklarieren,
     * um die Datenkapselung und die Prinzipien der objektorientierten Programmierung zu wahren.
     * Indem das Array privat ist, wird der direkte Zugriff und die Manipulation durch andere Klassen eingeschränkt,
     * und Änderungen können nur über die definierte Methode replaceUmlaute() vorgenommen werden.
     *
     * Auf diese Weise wird die Kontrolle über die Daten und ihre Verwendung in der Klasse behalten,
     * was zu einer besseren Codequalität, einfacherem Debugging und weniger unvorhersehbaren
     * Nebeneffekten führen kann.*/

}
