package jders22_generic.anwendung2GenericExtends;


public class Test {

    public static void main(String[] args) {
        // Durch Verarbeitungsvorgaenge<T extends Person> erreichen wir das nur Personen gesendet werden können und keine Fahrzeuge
        // wie, Verarbeitungsvorgaenge<Fahrzeug> verarbeitungFahrzeug = new Verarbeitungsvorgaenge<Fahrzeug>();

        Dozent dozent1 = new Dozent("Emir", "Oezdemir", 1984, "Java");

        Student student1 = new Student("Jörn", "Bruckner", 2002, "112200");

        Student student2 = new Student("Börn", "Bruckner", 2002, "303030");


        Verarbeitungsvorgaenge<Dozent> dozentVerarbeitungsvorgaenge = new Verarbeitungsvorgaenge<Dozent>();

        dozentVerarbeitungsvorgaenge.speicher(dozent1);

        Verarbeitungsvorgaenge<Student> studentVerarbeitungsvorgaenge = new Verarbeitungsvorgaenge<Student>();
        studentVerarbeitungsvorgaenge.speicher(student1);
        studentVerarbeitungsvorgaenge.speicher(student2);

        //dozentVerarbeitungsvorgaenge.getListe();

        System.out.println("Dozenten : ");
        for (Dozent dozent : dozentVerarbeitungsvorgaenge.getListe()) {
            System.out.println("Vorname: " + dozent.getVorname() + "\nNachname: " + dozent.getNachname());
        }

        System.out.println("Studenten : ");
        for (Student student : studentVerarbeitungsvorgaenge.getListe()) {
            System.out.println("Studenten Nummer: " + student.getStudentenNummer() + "\nNachname: " + student.getNachname());
        }

        System.out.println(dozentVerarbeitungsvorgaenge.getListe() + "\n" + studentVerarbeitungsvorgaenge.getListe());


    }
}
