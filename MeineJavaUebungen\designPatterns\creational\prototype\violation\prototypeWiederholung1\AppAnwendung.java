package designPatterns.creational.prototype.violation.prototypeWiederholung1;

import java.util.Date;

public class AppAnwendung {

    public static void main(String[] args) {

        /* In der Klasse PublicEntityService wird die prototypeWiederholung4DeepCopy-basierte Implementierung des Kopiermusters verwendet.
        In dieser Implementierung wird eine Dokument-Instanz als Prototyp verwendet, von dem neue Instanzen erstellt werden.*/
        // Beim <PERSON>n der Dokumente verlieren wir 4 Sekunden bis das nächste Dokument erstellt werden kann.

        PublicEntityService publicEntityService = new PublicEntityService();

        Date startZeit = new Date();
        Dokument dokument1 = publicEntityService.findDokumentById(1L);
        Date endZeit = new Date();

        Long prozessDauer = getProzessDauer(startZeit, endZeit);

        System.out.println(dokument1);
        System.out.println(dokument1.hashCode());
        System.out.println(prozessDauer);

        Date startZeit1 = new Date();
        Dokument dokument2 = publicEntityService.findDokumentById(2L);
        Date endZeit2 = new Date();

        Long prozess2Dauer = getProzessDauer(startZeit1, endZeit2);

        System.out.println(dokument2);
        /* Der Hashcode einer Referenz in Java wird normalerweise von der Object-Klasse bereitgestellt, es sei denn,
        Sie überschreiben die hashCode()-Methode in Ihrer eigenen Klasse. Wenn zwei verschiedene Klassen in unterschiedlichen Paketen
        (Unterpackages des gleichen Mutterpakets) das Prototypenmuster verwenden und keine spezifische Implementierung
        der hashCode()-Methode überschreiben, verwenden sie standardmäßig die von der Object-Klasse geerbte hashCode()-Implementierung.
        Diese Implementierung generiert einen Hashcode basierend auf der Speicheradresse des Objekts.

        Wenn Sie Objekte klonen oder referenzieren, ohne die hashCode()-Methode zu überschreiben, werden die Hashcodes der Objekte
        gleich sein, da sie auf denselben Speicherbereich verweisen. Dies ist unabhängig davon, ob sich die Klassen in verschiedenen
        Paketen befinden oder nicht.

        Um die Hashcodes der Objekte zu ändern, müssten Sie die hashCode()-Methode in Ihren Klassen überschreiben und
        eine benutzerdefinierte Implementierung bereitstellen, die auf den Inhalten der Objekte basiert,
        anstatt auf der Speicheradresse. Dies wäre jedoch nur notwendig, wenn Sie die Hashcodes tatsächlich
        für eine spezifische Logik verwenden und nicht, wenn Sie sie einfach für allgemeine Verwendungszwecke abrufen.
        Beachten Sie jedoch, dass das Überschreiben der hashCode()-Methode auch die equals()-Methode überschreiben sollte,
        um sicherzustellen, dass Objekte korrekt verglichen werden können.*/
        System.out.println(dokument2.hashCode());
        System.out.println(prozess2Dauer);


    }

    private static Long getProzessDauer(Date startZeit, Date endZeit) {

        long milliseconds = 1000;

        // Berechnung der Prozessdauer in Sekunden.
        long prozessDauer = (endZeit.getTime() / milliseconds) - (startZeit.getTime() / milliseconds);

        return prozessDauer;
    }
}
