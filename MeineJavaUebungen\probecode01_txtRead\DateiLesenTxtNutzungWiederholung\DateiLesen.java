package probecode01_txtRead.DateiLesenTxtNutzungWiederholung;

import java.io.FileInputStream;
import java.io.IOException;

public class DateiLesen {
    public static void main(String[] args) throws IOException {

        // Pfad zur Textdatei
        String path = "src/probecode01_txtRead/DateiLesenTxtNutzung/waren.txt";

        // FileInputStream erstellen, um die Datei zu lesen
        FileInputStream fis = new FileInputStream(path);  // Wir können die Ausnahme auch zur main hin zu deklarieren

        int kontrol = 0; // Variable zur Kontrolle des gelesenen Bytes

        String lyrik = ""; // String zur Speicherung des gelesenen Texts
        while ((kontrol = fis.read()) != -1) {
            lyrik += (char) kontrol; // Das gelesene Byte zu lyrik hinzufügen und als Zeichen interpretieren
        }

        int warenAnzahl = 0; // Variable zur Speicherung der Anzahl der Warenbestandteile

        // Den gelesenen Text anhand des Trennzeichens '-' in ein Array aufteilen
        String arr[] = lyrik.split("-");
        // Die Anzahl der Warenbestandteile ist die Länge des Arrays minus 1 (wegen des Trennzeichens)
        System.out.println("Anzahl der Warenbestandteile : " + (arr.length - 1));

        int ueberschriftAnzahl=0; // Variable zur Speicherung der Anzahl der Warenkategorien

        int index = 0;

        // Schleife zum Durchlaufen des gelesenen Texts
        for (int i = 0; i < lyrik.length(); i++) {
            char charakter = lyrik.charAt(i); // Aktuelles Zeichen

            if (i == 0) { // Wenn wir am Anfang des Texts sind
                if (Character.isDigit(charakter)) { // Wenn das Zeichen eine Ziffer ist
                    ueberschriftAnzahl++; // Erhöhe die Anzahl der Warenkategorien
                }
            } else {
                char letzerCharakter = lyrik.charAt(i - 1); // Letztes Zeichen

                // Wenn das aktuelle Zeichen eine Ziffer ist und das letzte Zeichen keine Ziffer ist
                if (Character.isDigit(charakter) && (!Character.isDigit(letzerCharakter))) {
                    ueberschriftAnzahl++; // Erhöhe die Anzahl der Warenkategorien
                }
            }
        }

        // Ausgabe der Anzahl der Warenkategorien
        System.out.println("Anzahl der Waren Kategorien : " + ueberschriftAnzahl);
    }
}

