package jders18_abstract.anwendung2Abstact;

public abstract class Fahrzeug {

    /* Wann man eine Methode nicht überschreiben kann:
      -Wenn die Methode in der übergeordneten Klasse final ist, kann sie nicht überschrieben werden.
      -Wenn die Methode in der übergeordneten Klasse static ist, kann sie nicht überschrieben werden.
      -Wenn die Methode in der übergeordneten Klasse eine andere Signatur hat, kann sie nicht überschrieben werden.*/

    private String farbe;

    private String fahrzeugArt;

    private double gewicht;

    public Fahrzeug() {

    }

    public Fahrzeug(String farbe, String fahrzeugArt, double gewicht) {
        this.farbe = farbe;
        this.fahrzeugArt = fahrzeugArt;
        this.gewicht = gewicht;
    }

    // Neue abstract Methode für den Liter Verbrauch pro 100Km ohne Methodenrumpf,
    // muss von jeder Unterklasse die erbt vordefiniert genutzt werden
    public abstract double literVerbrauch();

    public String getFarbe() {
        return farbe;
    }

    public void setFarbe(String farbe) {
        this.farbe = farbe;
    }

    public String getFahrzeugArt() {
        return fahrzeugArt;
    }

    public void setFahrzeugArt(String fahrzeugArt) {
        this.fahrzeugArt = fahrzeugArt;
    }

    public double getGewicht() {
        return gewicht;
    }

    public void setGewicht(double gewicht) {
        this.gewicht = gewicht;
    }

    @Override
    public String toString() {
        return "Fahrzeug{" +
                "farbe='" + farbe + '\'' +
                ", fahrzeugArt='" + fahrzeugArt + '\'' +
                ", gewicht=" + gewicht +
                '}';
    }
}
