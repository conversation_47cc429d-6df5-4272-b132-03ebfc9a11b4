package jders05_kontrollstrukturen;

public class BreakWhile1 {
    public static void main(String[] args) {

        int a = 0;

        while (a < 10) {
            if (a == 5) {  // Eintritt wenn a=5

                break;
            }

            System.out.println(a);
            a++;
        }
        System.out.println("Vorgang Ende");
    }
}

        /*  OUTPUT
        0
        1
        2
        3
        4
        Vorgang Ende
        */
