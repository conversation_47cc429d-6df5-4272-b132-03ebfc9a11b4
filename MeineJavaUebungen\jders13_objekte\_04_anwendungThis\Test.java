package jders13_objekte._04_anwendungThis;

public class Test {
    public static void main(String[] args) {

        Student student1 = new Student("bj<PERSON><PERSON>","<PERSON><PERSON>",1991,"2320");

        student1.getVorname();

        Student student2 = new Student("<PERSON>","<PERSON>", 1997, "2331");

        student2.setVorname("Osman"); // Vornamen ändern

        System.out.println("Name : " + student1.getVorname()+
                ", Nachname : "+ student1.getNachname()+
                ", Studenten Nummer : " + student1.getStudentenNummer()+
                ", Geburtsjahr : "+student1.getGerburtsjahr());

    }
}
