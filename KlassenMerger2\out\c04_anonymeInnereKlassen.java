// Gemergter Code aus dem Verzeichnis: C:\Users\<USER>\JavaPlace\MeineJavaUebungen\ajava_se\klassen\c03_lokaleKlassen

// package c03_lokaleKlassen;

// --- BEGIN DES GEMERGTEN CODES AUS DEM VERZEICHNIS ---

public class C01_LocalBeispiel {

    // wir haben innerhalb der Main eine Klassendefinition, diese Klassendefinition
    // der Lokalen-Klasse ist nur gültig innerhalb der main Methode
    public static void main(String[] args) {

        class LocalClass {
            public String toString() {
                return "Ich bin lokal";
            }
        }
        // Und dort:(innerhalb der main Methode) können wir davon ein Objekt anlegen und die toString Methode aufrufen.
        LocalClass local = new LocalClass();
        System.out.println(local.toString());
    }
    /* Der Code, der eine innere Klasse in einer Methode verwendet, kann für verschiedene Anforderungen
       nützlich sein, insbesondere wenn Sie Teile des Codes in einem begrenzten Kontext kapseln und darauf beschränken möchten.
       Hier sind einige Anforderungen und Anwendungsfälle, für die dieser Ansatz geeignet sein könnte:

       -Datenkapselung: Wenn Sie Teile Ihres Codes in einer Methode haben, die nur für diese Methode relevant sind
                        und nicht von anderen Teilen des Codes verwendet werden sollten, ist dies eine gute Möglichkeit, sie zu isolieren.

       -Begrenzter Gültigkeitsbereich: Innere Klassen in Methoden haben einen begrenzten Gültigkeitsbereich. Dies bedeutet,
                                       dass sie nur in der Methode sichtbar sind, in der sie erstellt wurden. Wenn Sie bestimmte
                                       Funktionen oder Daten auf den Gültigkeitsbereich einer Methode beschränken möchten, kann dies hilfreich sein.

       -Verwendung von lokalen Variablen: Sie können lokale Variablen der äußeren Methode in der inneren Klasse verwenden,
                                          selbst wenn sie nicht als final gekennzeichnet sind (sofern sie effektiv final sind,
                                          dh sie werden während der Lebensdauer der inneren Klasse nicht geändert).

       -Verbesserung der Lesbarkeit: Wenn Sie die Lesbarkeit und Verständlichkeit des Codes erhöhen möchten, können Sie komplexe
                                     Teile des Codes in benannte innere Klassen platzieren, die den Zweck und die Verwendung
                                     dieser Teile klar dokumentieren.

       -Implementierung von Schnittstellen und Abstraktionen: Innere Klassen in Methoden können hilfreich sein,
                                                              wenn Sie eine Schnittstelle oder eine abstrakte Klasse implementieren müssen,
                                                              die nur in einer bestimmten Methode verwendet wird.

       -Vermeidung von Duplikation: Dieser Ansatz kann zur Vermeidung von Code-Duplizierung beitragen,
                                    indem Teile des Codes in einer Methode wiederverwendet werden, ohne den gesamten Code erneut schreiben zu müssen.

       Beachten Sie jedoch, dass der Einsatz von inneren Klassen in Methoden auch die Komplexität erhöhen kann und in einigen Fällen
       die Code-Wartbarkeit beeinträchtigen kann. Daher sollte er mit Bedacht eingesetzt werden, wenn er tatsächlich einen Vorteil bietet,
       beispielsweise in Bezug auf bessere Strukturierung und Lesbarkeit des Codes oder auf die Beschränkung des Gültigkeitsbereichs
       von bestimmten Teilen des Codes.*/
}




public class C02_LocalMitVariable {

    public static void main(String[] args) {

        final String ausgabe = "Ich bin Lokal";

        class LocalClass {
            public String toString() {
                return ausgabe;
            }
        }

        LocalClass local = new LocalClass();
        System.out.println(local.toString());
    }
}


public class C03_LocalMitVariableUndInstanz {

    private String variable = "Ich bin nicht lokal";

    public void doIt() {
        final String ausgabe = "Ich bin lokal";

        class LocalClass {
            public String toString(){
                return ausgabe + ", " + variable;
            }
        }
        LocalClass local = new LocalClass();
        System.out.println(local.toString());
    }
}


public class C03_TestDrive {

    public static void main(String[] args) {

        C03_LocalMitVariableUndInstanz instance = new C03_LocalMitVariableUndInstanz();
        instance.doIt();

 /*
 Lokale Klassen

 Was ist eine lokale Klasse?
 Eine lokale Klasse ist eine Klasse, die innerhalb einer Methode, eines Konstruktors oder einer statischen Initialisierungsblock definiert wird.
 Wofür werden lokale Klassen verwendet?
 Lokale Klassen werden häufig verwendet, um komplexe Datenstrukturen oder Algorithmen zu implementieren, die nur innerhalb einer Methode oder eines Konstruktors benötigt werden.

 Welche Einschränkungen haben lokale Klassen?
 -Lokale Klassen können nur auf Methoden und Variablen der äußeren Klasse zugreifen, die final sind.
 -Lokale Klassen können nicht als statische Klassen deklariert werden.
 -Lokale Klassen können nicht als öffentliche, private oder geschützte Klassen deklariert werden.

  Konkrete Erklärung zu den Punkten:
 -Lokale Klassen sind nicht als Eigenschaften einer Klasse deklariert. Sie werden innerhalb einer Methode
  oder eines Konstruktors deklariert und können nur innerhalb dieser Methode oder dieses Konstruktors verwendet werden.

 -Lokale Klassen können innerhalb von Anweisungsblöcken, Methoden und statischen Initialisierungsblöcken deklariert werden.

 -Lokale Klassen können nicht als Schnittstellen deklariert werden.

 -Lokale Klassen können auf Methoden und Variablen der äußeren Klasse zugreifen, die final sind.

 -Lokale Klassen, die in einer statischen Methode deklariert sind, können keine Methoden der äußeren Klasse aufrufen,
 die ein Objekt der äußeren Klasse benötigen.

 -Lokale Klassen können nicht als public, private oder static deklariert werden.*/


    }
}


// --- ENDE DES GEMERGTEN CODES ---
