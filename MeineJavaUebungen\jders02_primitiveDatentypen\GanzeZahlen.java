package jders02_primitiveDatentypen;

public class GanzeZahlen {
    public static void main(String[] args) {
       /*   Typ      Speicher     gültiger Wertebereich             Beispiel          Verwendung
            -------------------------------------------------------------------------------------
            boolean  ~1 Bit      true/false                       true              Zustandsprüfung
            byte     8 Bit       -128 bis 127                     127               Speicheroptimierung
            short    16 Bit      -32.768 bis 32.767               32767             "
            int      32 Bit      -2.147.483.648 bis 2.147.483.647  2147483647        Standard Ganzzahl
            long     64 Bit      -9.223.372.036.854.775.808 bis   9223372036854775807L  Große Zahlen
                     	          9.223.372.036.854.775.807
            
            Hinweis: Überlauf bei Bereichsüberschreitung beachten!
        */

        // Direkte Initialisierung
        byte byteValue = 127;
        short shortValue = 32767;
        int intValue = 2147483647;
        long longValue = 5555555L;

        // Ausgabe der Werte
        System.out.println("byteValue: " + byteValue);
        System.out.println("shortValue: " + shortValue);
        System.out.println("intValue: " + intValue);
        System.out.println("longValue: " + longValue);

        // Überlauf demonstrieren
        byteValue++; // Überlauf!
        System.out.println("Byte Überlauf: " + byteValue); // -128
    }
}
