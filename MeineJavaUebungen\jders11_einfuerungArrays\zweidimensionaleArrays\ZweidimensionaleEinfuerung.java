package jders11_einfuerungArrays.zweidimensionaleArrays;

public class ZweidimensionaleEinfuerung {
    public static void main(String[] args) {

        // int[] zahlen = new int[5];

        int[][] tabelle = new int[4][7];

        /*
        // für Eindimensionale Arrays
        for (int i = 0; i < zahlen.length; i++) {
            zahlen[i] = 10;
        }

        for (int i = 0; i< zahlen.length;i++){
            System.out.println(zahlen[i]);
        }
        */

        for (int i = 0; i < tabelle.length; i++) { // 4

            for (int j = 0; j < tabelle[i].length; j++) {  // 7

                tabelle[i][j] = 10;

            }
        }

        for (int i = 0; i < tabelle.length; i++) { // 4

            for (int j = 0; j < tabelle[i].length; j++) {  // 7

                System.out.print(tabelle[i][j] + " ");
            }

            System.out.println();
        }
    }

}
