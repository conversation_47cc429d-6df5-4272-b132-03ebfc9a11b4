package ajava_se.klassen.c02_statischeInnereKlassen;

/**
 * Statische verschachtelte Klassen
 * Thema: Innere Klassen (static nested classes)
 * 
 * Diese Klasse demonstriert eine statische verschachtelte Klasse.
 * Statische verschachtelte Klassen benötigen keine Instanz der äußeren Klasse, um zu existieren oder instanziiert
 * 
 * Didaktische Gründe:
 * - Zeigt, wie "static nested classes" unabhängig von äußeren Instanzen genutzt werden.
 * - Verdeutlicht Zugriffsbeschränkungen auf statische und nicht-statische Member.
 * - Vergleicht direkt im Code: statische vs. nicht-statische innere Klassen.
 * - Im Bytecode erscheinen statische verschachtelte Klassen als separate Klassen (z.B. StaticMitglied$Innere.class).
 */
public class C01_StaticMitglied {

    /**
     * Dies ist eine statische innere Klasse (auch als 'static nested class' bekannt).
     * Sie verhält sich ähnlich wie eine normale Top-Level-Klasse, ist aber
     * im Namensraum der äußeren Klasse (C01_StaticMitglied) angesiedelt.
     *
     * Wichtige Eigenschaften:
     * - Benötigt keine Instanz der äußeren Klasse, um instanziiert zu werden.
     * - Kann nicht direkt auf nicht-statische Member (Variablen, Methoden) der äußeren Klasse zugreifen.
     * - Kann auf statische Member der äußeren Klasse zugreifen (auch private).
     * - Kann eigene statische und nicht-statische Member haben.
     * - Wird im Bytecode als eigenständige Klasse mit "$" im Klassennamen gespeichert.
     */
    public static class Innere {
        @Override
        public String toString() {
            return "Ich bin die statische verschachtelte Klasse";
        }
    }

    // Überschreibt toString() zur einfachen Identifikation des Objekts der äußeren Klasse.
    @Override
    public String toString() {
        return "Ich bin die äußere Klasse";
    }
}