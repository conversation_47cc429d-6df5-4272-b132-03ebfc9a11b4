package jders11_einfuerungArrays.arrayList;

import java.util.ArrayList;

public class ArrayListEinfuerung {
    public static void main(String[] args) {
        // Erstellen einer neuen ArrayList mit dem Datentyp String
        ArrayList<String> aufgabenListe = new ArrayList<>();  // ArrayList<kann auch leer stehen>

        /*
        * add() wird zum Datentyp hinzugefügt
        * remove
        * get
        * IndexOf
        */

        aufgabenListe.add("Putzen");
        aufgabenListe.add("Kochen");
        aufgabenListe.add("Programmieren");

        for (String aufgabe : aufgabenListe){
            System.out.println(aufgabe);
        }

        aufgabenListe.remove(1);
        System.out.println("---------");

        // Durchlaufen der ArrayList mit einer Schleife und Ausgabe der Elemente
        for (int i = 0; i<aufgabenListe.size();i++){  // .size() da Länge unbekannt bleibt
            // die Methode get() wird verwendet, um das Element an der aktuellen Position abzurufen
            System.out.println(aufgabenListe.get(i));
        }

    }


}
        /*
         * In einer ArrayList in Java wird die Anzahl der Elemente mit der Methode size() abgerufen,
         * nicht mit length wie bei Arrays. Der Grund dafür liegt
         * in der unterschiedlichen Implementierung von Arrays und ArrayLists.
         *
         * Arrays in Java sind eine feste Größe, die bei der Initialisierung festgelegt wird.
         * Die Eigenschaft length gibt die Länge des Arrays an und bleibt während der Laufzeit unverändert.
         *
         * ArrayLists in Java sind dagegen dynamisch. Sie können wachsen und schrumpfen,
         * indem Elemente hinzugefügt oder entfernt werden. Die Methode size() gibt die
         * aktuelle Anzahl der Elemente in der ArrayList zurück. Da sich die Größe der ArrayList ändern kann,
         * ist size() die richtige Methode, um die Anzahl der Elemente abzurufen.
         *
         * Daher wird in der Schleifenbedingung i < aufgabenListe.size() die Methode size() verwendet,
         * um sicherzustellen, dass die Schleife alle Elemente der ArrayList durchläuft.
         * Wenn stattdessen length verwendet sein würde, würde es zu einem Compiler Fehler kommen,
         * da length für Arrays und nicht für ArrayLists definiert ist.
         */