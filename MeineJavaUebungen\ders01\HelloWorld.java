package ders01;

/**
 * Dies ist die klassische "Hello World"-<PERSON><PERSON><PERSON><PERSON>, die als Einstieg in die Java-Programmierung dient.
 * Sie demonstriert die grundlegende Struktur eines Java-Programms mit einer Hauptklasse und der main-Methode.
 */
public class HelloWorld {
    /**
     * Die main-Methode ist der Einstiegspunkt für jedes Java-Programm.
     * Sie wird automatisch vom Java Virtual Machine (JVM) aufgerufen, wenn das Programm gestartet wird.
     *
     * @param args Ein Array von Strings, das Kommandozeilenargumente enthält (wird hier nicht verwendet)
     */
    public static void main(String[] args) {
        // Die println-Methode gibt den Text auf der Konsole aus und fügt einen Zeilenumbruch hinzu
        System.out.println("Hello, Public Suggestion is alive!");

        // Java gibt alles zwischen Anführungszeichen ("") genau so aus, wie es geschrieben wurde
        // Dies wird als String-Literal bezeichnet und ist eine grundlegende Form der Datendarstellung in Java
    }
}
