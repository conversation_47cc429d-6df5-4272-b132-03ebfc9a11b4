package ders13_recursiveMethod;

public class C04_Palindrome {

    public static void main(String[] args) {
        // Eingabe-String definieren
        String input = "isASi";
        // Aufruf der Methode palindromeKontrollieren mit dem Eingabe-String
        boolean erg = palindromeKontrollieren(input);
        // Ausgabe des Ergebnisses
        System.out.println(erg);
    }

    // Methode zum Überprüfen, ob der übergebene String ein Palindrom ist
    private static boolean palindromeKontrollieren(String input) {
        // Wenn die Länge des Eingabestrings 0 oder 1 ist, handelt es sich um ein Palindrom
        if ((input.length()==0 || input.length()==1)) {  // oder auch (input.length() <= 1)
            return true;
        } else {
            // Überprüfung, ob der erste und letzte Buchstabe des Eingabestrings übereinstimmen
            if (input.substring(0, 1).equalsIgnoreCase(input.substring(input.length() - 1))) {
                // Wenn der erste und letzte Buchstabe des Eingabestrings übereinstimmen,
                // rufen wir die Methode erneut mit dem Eingabestring ohne den ersten und letzten Buchstaben auf
                return palindromeKontrollieren(input.substring(1, input.length() - 1));
            } else {
                // Wenn der erste und letzte Buchstabe des Eingabestrings nicht übereinstimmen,
                // handelt es sich nicht um ein Palindrom
                return false;
            }
        }
    }
}
