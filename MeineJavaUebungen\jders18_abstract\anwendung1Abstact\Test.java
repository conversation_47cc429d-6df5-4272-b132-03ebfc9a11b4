package jders18_abstract.anwendung1Abstact;

public class Test {
    public static void main(String[] args) {
        /*
        // Wenn wir von der Abstract-Klasse ein neues Objekt erstellen dann bekommen wir die beiden Methoden,
        // und wir bekommen in dem Fall die Return Werte der Klasse
        Form form1 = new Form() {
            @Override
            public double getInhalt() {
                return 0;
            }

            @Override
            public double getUmfang() {
                return 0;
            }
        };
        */

        Viereck viereck1 = new Viereck(4);
        Kreis kreis1 = new Kreis(4);
        // Ohne die Set-Methode können wir keine werte später ändern wie: viereck1.setSeite(10);
        // Ohne Set müsste mit Viereck viereck1 = new Viereck(4); ein neues Objekt erstellt werden
        // wobei das Alte Objekt viereck1 komplett vom Speicher gelöscht wird
        /*
        Form form =viereck1;
        System.out.println("Inhalt : " + form.getInhalt());
        System.out.println("Umfang : " + form.getUmfang());
        */
        System.out.println("Inhalt : " + viereck1.getUmfang());
        System.out.println("Umfang : " + viereck1.getInhalt());

        berechne(viereck1);
        berechne(kreis1);

    }

    public static void berechne(Form form){

        System.out.println("Inhalt : " + form.getInhalt());
        System.out.println("Umfang : " + form.getUmfang());
    }
}
