package ders07_concatenation_operators;

public class C02_01_Operators {
    public static void main(String[] args) {

        int a = 20;

        a = a + 10;  // beim assignment wird erst Rechts der wert gefunden und dan findet Links die Zuweisung statt

        int b = 20;

        // a und b gleich ist...

        System.out.println(a == b); // false

        System.out.println(a == b + 10); // true

        boolean c = 20 ==b;

        System.out.println(c);

    }
}
