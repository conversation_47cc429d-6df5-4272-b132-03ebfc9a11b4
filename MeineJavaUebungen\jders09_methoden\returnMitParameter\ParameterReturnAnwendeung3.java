package jders09_methoden.returnMitParameter;

import java.util.Scanner;

public class ParameterReturnAnwendeung3 {
    public static void main(String[] args) {

        String a = takeNameNachname();

        // String nameNachname = a;
        ausgeben(a);

    }

    /* Wir können leider nicht unsere Strings einzeln return,
     * daher nutzen wir für unser Beispiel beide Strings oder
     * es wird nur ein String returned
     * */
    public static String takeNameNachname() {

        Scanner sc = new Scanner(System.in);

        String name;
        String nachname;

        System.out.print("Ihr Name : ");
        name = sc.next();
        System.out.print("Ihr Nachname : ");
        nachname = sc.next();

        sc.close();

        return name + " " + nachname;

    }

    public static void ausgeben(String nameNachname) {

        System.out.println("Willkommen, " + nameNachname);

    }

}
