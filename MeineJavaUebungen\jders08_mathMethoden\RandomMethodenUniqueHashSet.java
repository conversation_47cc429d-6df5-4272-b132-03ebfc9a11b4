package jders08_mathMethoden;

import java.util.HashSet;
import java.util.Random;
import java.util.Set;

public class RandomMethodenUniqueHashSet {
    public static void main(String[] args) {

        /*
        Dieser Code erzeugt eine Menge von 10 eindeutigen Zufallszahlen zwischen 1 und 20,
        indem er ein Set erstellt und dann Zufallszahlen generiert und sie dem Set hinzufügt,
        bis die Größe des Sets 10 erreicht hat. Anschließend wird eine Schleife durchlaufen,
        um jedes Element des Sets auszugeben, wobei jede <PERSON>all<PERSON>ahl mit einer Zählvariable versehen wird.

        Wir Verwenden das Set-Interface, hinzu verwenden wir die nextInt-Methode von Random
        für das Generieren der Zahlen.
        Verwendet wird die for-each-Schleife, um durch das Set zu iterieren und erfordert es keine Kenntnis der Größe des Arrays oder der Collection.
         */

        Set<Integer> set = new HashSet<Integer>(); // Erstellung eines Sets für die einzigartigen Zufallszahlen
        Random rand = new Random(); // Erstellung eines Objekts für die Erzeugung von Zufallszahlen

        while (set.size() < 10) { // Solange die Größe des Sets kleiner als 10 ist, führe die Schleife aus
            int num = rand.nextInt(20) + 1; // Erzeugung einer neuen Zufallszahl zwischen 1 und 20
            set.add(num); // Hinzufügen der Zufallszahl zum Set
        }

        int count = 1; // Startwert für die Zählvariable
        for (int num : set) { // Iteration durch das Set
            System.out.println(count + ". Zufallszahl: " + num); // Ausgabe der aktuellen Zufallszahl mit Zählvariable
            count++; // Inkrementierung der Zählvariable
        }
    }
}
