package jders05_kontrollstrukturen;

public class DoWhile1 {
    public static void main(String[] args) {
        /*
        Wird die do-while-Schleife ausgeführt, so wird als Erstes der im Rumpf der Schleife enthaltende Code ausgeführt.
        Im Anschluss wird die Bedingung geprüft und sofern die Bedingung zu true evaluiert
        wird ein erneuter Schleifendurchlauf gestartet. Wenn die Bedingung false ist wird der Code nach der Schleife weiter ausgeführt.
        */
        int a = 0;

        do {

            System.out.println("Wert von a : " + a);
            a+=3;
        } while (a < 10);
    }
}
