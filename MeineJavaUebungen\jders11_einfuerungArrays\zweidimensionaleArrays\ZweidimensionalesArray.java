package jders11_einfuerungArrays.zweidimensionaleArrays;

public class ZweidimensionalesArray {
    public static void main(String[] args) {

        char[][] tabelle = new char[8][14];

        for (int i = 0; i < tabelle.length; i++) {

            for (int j = 0; j < tabelle[i].length; j++) {

                if (i == 0 || i == (tabelle.length - 1)) {
                    tabelle[i][j] = '#';  // Setzt # in die Erste Zeile von links bis rechts und mit || auch in die letzte Zeile

                } else if (j == 0 || j == tabelle[i].length -1) {
                    tabelle[i][j] = '#';  // Setzt # von der zweiten Zeile, erste Spalte bis letzte Spalte und ihrer Spiegelung

                } else if (i == j ){
                    tabelle[i][j] = '#';  // Setzt # auf der Diagonalen Linie

                } else {
                    tabelle[i][j] = '-'; // Setzt - für alle anderen Bedingungen
                }

            }
        }

        for (int i = 0; i < tabelle.length; i++) {

            for (int j = 0; j < tabelle[i].length; j++) {

                System.out.print(tabelle[i][j] + " ");  // auf der Zeile mit einem Leerzeichen Ausgeben

            }
            System.out.println();  // auf die nächste Zeile von der Schleife nächste Spalte Ausgeben
        }

    }
}
