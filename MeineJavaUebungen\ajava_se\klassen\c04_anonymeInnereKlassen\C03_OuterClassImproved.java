package ajava_se.klassen.c04_anonymeInnereKlassen;

public class C03_OuterClassImproved {

    public void method() {
        // Erstelle einen neuen Thread für die "Hello, world!"-Nachricht
        Thread helloWorldThread = new Thread(new Runnable() {
            @Override
            public void run() {
                // Thread-Namen und Nachricht ausgeben
                System.out.println(Thread.currentThread().getName() + ": Hello, world!");
            }
        });

        // Starte den Thread für die "Hello, world!"-Nachricht
        helloWorldThread.start();
    }

    public static void main(String[] args) {
        // Erstelle einen neuen Thread für die "Merhabalar" -Nachricht
        Thread merhabalarThread = new Thread(new Runnable() {
            @Override
            public void run() {
                // Thread-Namen und Nachricht ausgeben
                System.out.println(Thread.currentThread().getName() + ": Merhabalar");
            }
        });

        // Starte den Thread für die "Merhabalar"-Nachricht
        merhabalarThread.start();

        // Erstelle eine Instanz der OuterClass und rufe die Methode auf
        C02_OuterClass outer = new C02_OuterClass();

        outer.method();

    }
    /* Erwartete Ausgabe:
     * Thread-0: Merhabalar
     * Thread-1: Hello, world!
     */
}
